<?php
include('Crypt/RSA.php');

$rsa = new Crypt_RSA();

$rsa->setPublicKeyFormat(CRYPT_RSA_PUBLIC_FORMAT_OPENSSH);

// extract($rsa->createKey());
$rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);

echo $publickey='MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmrt2/KK2d0e9NQM699YTTFAlcgPiLA3HYEHoRSBJAAUPhApGEmi1fBRcNxd0vUe9NsWs5MTborjWumsJKrDRTekqAzmHGvmqQEm9Zmo9A58IkGIKnsi/3CyfpW2eUY0mwSS9/Uk4wLOY6tETjIN5f+1BhGJm9v9DJLPWUHYW1JUWa3Gc3Ly4028bzMm2IPX1LXqsZ1Z0Dgzafyxy/59WGie4T5LSXUBgKBiAZR2RNtKy/A4yGCrqChUgbpIZu1y0V59DU/NHO/NcZJc0OvwenH9KLbpk//S98dbJX1O0EuwGeFd0JnIniD+Y17B9VcZyBBktk5Lj8g0mAvA90vEuBQIDAQAB';
echo '</br>';
echo '</br>';
// echo '</br>';
echo $privatekey='MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCau3b8orZ3R701Azr31hNMUCVyA+IsDcdgQehFIEkABQ+ECkYSaLV8FFw3F3S9R702xazkxNuiuNa6awkqsNFN6SoDOYca+apASb1maj0DnwiQYgqeyL/cLJ+lbZ5RjSbBJL39STjAs5jq0ROMg3l/7UGEYmb2/0Mks9ZQdhbUlRZrcZzcvLjTbxvMybYg9fUteqxnVnQODNp/LHL/n1YaJ7hPktJdQGAoGIBlHZE20rL8DjIYKuoKFSBukhm7XLRXn0NT80c781xklzQ6/B6cf0otumT/9L3x1slfU7QS7AZ4V3QmcieIP5jXsH1VxnIEGS2TkuPyDSYC8D3S8S4FAgMBAAECggEAP/89TWx3D4qqZRy67Pqr7KpRcoDMsO/aAKQBz2V1C/8L1c7yAHcyaOl64oZfEFJ1ngvQYKZTOVA978iJeBC94P2JFOBcUvnkQsa9AXj7V14VirIiNl5NjGEgfqmCC7TXzJbHxdTdFkax/p0O+tT+Eb0tlfWlJI+bU7jV8V2GP9HtUZN7OMRZFdP5u2MG0y8Z61dPV/CthVlE3JEx10vdXannHAV5NKGlDdDEcki5PP5oJNwdP9Ndy06S6jnuwSz61n5SX5dY+5I1N0bfCL+cnFo6mbLQEneoiffsHgteP+C6RcnD3yDMmoMmL9QZbKV03YIo66EqBMxYrgGDmATlGQKBgQDLSAwIZgcSVcfstncRAHCokvJv70VImmvMqOm9d11b0IUdp69OSiudX2ph8y/o3cfMLift0V63fJfKaBbG/KKyT20Xxy8h1N4tDzGzr2hw2CN3v7E/vU6h6pHzBuua8PGVRfBslkDeCakUUtMjCYjMO1rGa42mfFH/KYIx8yRCjwKBgQDC3DdZ+0tOydcqaKbn70wTGhh29LH5qRiK65EOV8EJU9xlhbaf4LwBXhx3ATUxrA/vJCSxdFUuxME4Zm3KIXX9PT5iC35sDpipljjYluxF+S7INi/jfw7M9p+dt43t/3C481nshmIF0og7Kh7AuYrBnf0cGB2D+N9iqrdNC4YAKwKBgQCx1/5u+pQFPncCaUAR58ioMqdXtoVu12qoSr7zu5I52Vwcba+5VrqBnBB+UTwBBsbdoyPbs+ihrqc9tYL3496TEVnoC6L4fjtCQKDDCxrcXrvo8BQqJ3y7u3SkodFOZK12YxHMbkeh+eGqniuJ47WCuAravQ3YMbuq+K2m9YkrkQKBgGDnw18sabc1CZw9j0Oo7dxSDfiamCfgwCPWIr1PRuCso96ZMMkxqk3Dz8QfBaU+PG5hOx5FDFfF4ZF63v62xUa7Plrj/Gsglys/WeXcqIPj+C/5QreQaHvl6ls0/ZBuiFByNTY7XmQm3zkNKV4O58KCH23pUERhcDZRT/cw0+MhAoGAKKVZDBztbpJ31VhtL8khJzXSQVePiNqLby/LjwvQBLwcf5pzUwrm7GAuedCs3L6B2xU9PtombmyNV6wgPSIaK7PpS+lhz3aSvVv5CVO07wtFbWhNzPlj//nDT1/1A54mp17+I6dXhAqoR1s3C6Uc3cshSqz9Gr7gTdGeaPLSX/w=';
echo '</br>';
echo '</br>';
// $plaintext = 'change the world in all life';
// $rsa->loadKey($publickey);
// $encrypted = $rsa->encrypt($plaintext);

// $encode = base64_encode($encrypted);


$decoded =  base64_decode("aeIjlUOYm5vyG2RJyociaQABavuOcdO9EcNP/+LuT8bhpknOzgHvHZdhlXS4DUu7KbRpv97xz1jwiW4eUrb+3+J4oHxe4uDDKu5YUZq9cSEOS9dHP/08s0VO9pn981NDVazi2SfA4LHzJM8cfbVPYpx/y7I57xG3CCWlFmZMXWZUNO64iOfTBPi/zBrsrAwsE5cyCiwiyzV7/n4rJloP53vB/aqBp8zRCd1N9mNUX0yEJH6hLclDbub4VsSNW2IWk2uYt9CdRpY1vc8WGlzDsxYaVuSg40yETq4WFq49wbo3KLO1OLuH3D+erHsvvUtGtO8u8EBNnKn+Kl+C6gdyyA==");
$rsa->loadKey($privatekey);
echo $decrypted = $rsa->decrypt($decoded);
// echo base64_encode($decrypted);
?>