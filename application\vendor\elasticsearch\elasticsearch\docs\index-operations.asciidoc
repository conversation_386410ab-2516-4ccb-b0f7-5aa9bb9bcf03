
== Index Management Operations

Index management operations allow you to manage the indices in your Elasticsearch cluster, such as creating, deleting and
updating indices and their mappings/settings.

=== Create an index

The index operations are all contained under a distinct namespace, separated from other methods that are on the root
client object.  As an example, let's create a new index:

[source,php]
----
$client = ClientBuilder::create()->build();
$params = [
    'index' => 'my_index'
];

// Create the index
$response = $client->indices()->create($params);
----
{zwsp} +

You can specify any parameters that would normally be included in a new index creation API.  All parameters that
would normally go in the request body are located in the 'body' parameter:

[source,php]
----
$client = ClientBuilder::create()->build();
$params = [
    'index' => 'my_index',
    'body' => [
        'settings' => [
            'number_of_shards' => 3,
            'number_of_replicas' => 2
        ],
        'mappings' => [
            'my_type' => [
                '_source' => [
                    'enabled' => true
                ],
                'properties' => [
                    'first_name' => [
                        'type' => 'string',
                        'analyzer' => 'standard'
                    ],
                    'age' => [
                        'type' => 'integer'
                    ]
                ]
            ]
        ]
    ]
];


// Create the index with mappings and settings now
$response = $client->indices()->create($params);
----
{zwsp} +

=== Create an index (advanced example)

This is a more complicated example of creating an index, showing how to define analyzers, tokenizers, filters and
index settings. Although essentially the same as the previous example, the more complicated example can be helpful
for "real world" usage of the client, since this particular syntax is easy to mess up.

[source,php]
----
$params = [
    'index' => 'reuters',
    'body' => [
        'settings' => [ <1>
            'number_of_shards' => 1,
            'number_of_replicas' => 0,
            'analysis' => [ <2>
                'filter' => [
                    'shingle' => [
                        'type' => 'shingle'
                    ]
                ],
                'char_filter' => [
                    'pre_negs' => [
                        'type' => 'pattern_replace',
                        'pattern' => '(\\w+)\\s+((?i:never|no|nothing|nowhere|noone|none|not|havent|hasnt|hadnt|cant|couldnt|shouldnt|wont|wouldnt|dont|doesnt|didnt|isnt|arent|aint))\\b',
                        'replacement' => '~$1 $2'
                    ],
                    'post_negs' => [
                        'type' => 'pattern_replace',
                        'pattern' => '\\b((?i:never|no|nothing|nowhere|noone|none|not|havent|hasnt|hadnt|cant|couldnt|shouldnt|wont|wouldnt|dont|doesnt|didnt|isnt|arent|aint))\\s+(\\w+)',
                        'replacement' => '$1 ~$2'
                    ]
                ],
                'analyzer' => [
                    'reuters' => [
                        'type' => 'custom',
                        'tokenizer' => 'standard',
                        'filter' => ['lowercase', 'stop', 'kstem']
                    ]
                ]
            ]
        ],
        'mappings' => [ <3>
            '_default_' => [    <4>
                'properties' => [
                    'title' => [
                        'type' => 'text',
                        'analyzer' => 'reuters',
                        'term_vector' => 'yes',
                        'copy_to' => 'combined'
                    ],
                    'body' => [
                        'type' => 'text',
                        'analyzer' => 'reuters',
                        'term_vector' => 'yes',
                        'copy_to' => 'combined'
                    ],
                    'combined' => [
                        'type' => 'text',
                        'analyzer' => 'reuters',
                        'term_vector' => 'yes'
                    ],
                    'topics' => [
                        'type' => 'text',
                        'index' => 'not_analyzed'
                    ],
                    'places' => [
                        'type' => 'text',
                        'index' => 'not_analyzed'
                    ]
                ]
            ],
            'my_type' => [  <5>
                'properties' => [
                    'my_field' => [
                        'type' => 'text'
                    ]
                ]
            ]
        ]
    ]
];
$client->indices()->create($params);
----
<1> The top level `settings` contains config about the index (# of shards, etc) as well as analyzers
<2> `analysis` is nested inside of `settings`, and contains tokenizers, filters, char filters and analyzers
<3> `mappings` is another element nested inside of `settings`, and contains the mappings for various types
<4> The `_default_` type is a dynamic template that is applied to all fields that don't have an explicit mapping
<5> The `my_type` type is an example of a user-defined type that holds a single field, `my_field`


=== Delete an index

Deleting an index is very simple:

[source,php]
----
$params = ['index' => 'my_index'];
$response = $client->indices()->delete($params);
----
{zwsp} +

=== Put Settings API
The Put Settings API allows you to modify any index setting that is dynamic:

[source,php]
----
$params = [
    'index' => 'my_index',
    'body' => [
        'settings' => [
            'number_of_replicas' => 0,
            'refresh_interval' => -1
        ]
    ]
];

$response = $client->indices()->putSettings($params);
----
{zwsp} +

=== Get Settings API

Get Settings API will show you the currently configured settings for one or more indexes:

[source,php]
----
// Get settings for one index
$params = ['index' => 'my_index'];
$response = $client->indices()->getSettings($params);

// Get settings for several indices
$params = [
    'index' => [ 'my_index', 'my_index2' ]
];
$response = $client->indices()->getSettings($params);
----
{zwsp} +

=== Put Mappings API

The Put Mappings API allows you to modify or add to an existing index's mapping.

[source,php]
----
// Set the index and type
$params = [
    'index' => 'my_index',
    'type' => 'my_type2',
    'body' => [
        'my_type2' => [
            '_source' => [
                'enabled' => true
            ],
            'properties' => [
                'first_name' => [
                    'type' => 'string',
                    'analyzer' => 'standard'
                ],
                'age' => [
                    'type' => 'integer'
                ]
            ]
        ]
    ]
];

// Update the index mapping
$client->indices()->putMapping($params);
----
{zwsp} +

=== Get Mappings API

The Get Mappings API will return the mapping details about your indexes and types.  Depending on the mappings that you wish to retrieve, you can specify a number of combinations of index and type:

[source,php]
----
// Get mappings for all indexes and types
$response = $client->indices()->getMapping();

// Get mappings for all types in 'my_index'
$params = ['index' => 'my_index'];
$response = $client->indices()->getMapping($params);

// Get mappings for all types of 'my_type', regardless of index
$params = ['type' => 'my_type' ];
$response = $client->indices()->getMapping($params);

// Get mapping 'my_type' in 'my_index'
$params = [
    'index' => 'my_index'
    'type' => 'my_type'
];
$response = $client->indices()->getMapping($params);

// Get mappings for two indexes
$params = [
    'index' => [ 'my_index', 'my_index2' ]
];
$response = $client->indices()->getMapping($params);
----
{zwsp} +

=== Other APIs in the Indices Namespace
There are a number of other APIs in the indices namespace that allow you to manage your elasticsearch indexes (add/remove templates, flush segments, close indexes, etc).

If you use an IDE with autocompletion, you should be able to easily explore the indices namespace by typing:

[source,php]
----
$client->indices()->
----
And perusing the list of available methods.  Alternatively, browsing the `\Elasticsearch\Namespaces\Indices.php` file will show you the full list of available method calls (as well as parameter lists in the comments for each method).
