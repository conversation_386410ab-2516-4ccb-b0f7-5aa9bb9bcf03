<?php

error_reporting(1);
defined('BASEPATH') OR exit('No direct script access allowed');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttle
 *
 * <AUTHOR>
 */
class Shuttle extends CI_Controller {

    public function index() {
        $ct = "";
        $headers = getallheaders();
        if (isset($headers)) {
           // $key = $headers['PiaP79bER'];
            $mobileno = $headers['no'];
            $ct = $headers['ct'];
            if ($mobileno == 1) {
                $this->load->model('Shuttlemdl');
                $jdskuse = $this->input->post("jdskuse");
                $var = str_replace(' ', '+', $jdskuse);
                if ($ct == "ip") {
                    $decrypted = $var;
                } else {
                    $decrypted = $this->decrypt($var, DECRYPT_KEY1, DECRYPT_KEY2);
                }
                $keydata = array();
                $keydata = explode('@@#', $decrypted);
                switch ($keydata[0]) {
                    case 5000:
                        $ret = $this->Shuttlemdl->shuttle_registraton($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5020:
                        $ret = $this->Shuttlemdl->shuttle_otpcreation($keydata[1], $keydata[2],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 5010:
                        $ret = $this->Shuttlemdl->shuttle_otpverified($keydata[1], $keydata[2], $keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5070:
                        $ret = $this->Shuttlemdl->forgot_password($keydata[1], $keydata[2],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 5080:
                        $ret = $this->Shuttlemdl->generate_newpassword($keydata[1], $keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5081:
                        $this->Shuttlemdl->insert_route_master($keydata[1], $keydata[2], $keydata[3], $keydata[4], $ct);
                        break;                  
                    default:
                        $element_array = array('status' => 3);
                        echo $this->output($element_array, $ct);
                        break;
                }
            } else {
                $models = array(
                    'Shuttlemdl' => 'smodel',
                    'ShuttleElasticmdl' => 'semodel',
                    'FixedShuttlemdl' => 'fmodel'
                );
                $this->load->model($models);
                $ct = $headers['ct'];
                $ssvaluecheck = "";
                $ssid = $headers['idval'];
                $arrayval = array();                
                $ssvaluecheck = $this->smodel->ssvaluecheck($mobileno, $ssid,$ct);
                $arrayval = explode(',', $ssvaluecheck);
//                if ($arrayval[0] == 'true') 
//                {
                $jdskuse = $this->input->post("jdskuse");
                $var = str_replace(' ', '+', $jdskuse);                
                $decrypted = "";
                if ($ct == "ip") {
                    $decrypted = $var;
                } else {
                    $decrypted = $this->decrypt($var, DECRYPT_KEY1, DECRYPT_KEY2);
                }
                $keydata = array();
                $keydata = explode('@@#', str_replace('+', ' ', $decrypted));
                switch ($keydata[0]) {
                    case 3000:
                        $ret = $this->smodel->appversion($keydata[1], $keydata[2],$keydata[3]);
                        echo $this->output($ret, $ct);
                        break;
                    case 3050:
                        $ret = $this->smodel->reason($keydata[1]);
                        echo $this->output($ret, $ct);
                        break;
                    case 4000:
                        $ret = $this->fmodel->shuttle_fixed_routes();
                        echo $this->output($ret, $ct);
                        break;
                    case 4010:
                        $ret = $this->fmodel->shuttle_fixed_route_search($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9]);
                        echo $this->output($ret, $ct);
                        break;
//                    case 5000:
//                        $ret = $this->smodel->shuttle_search($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12]);
//                        echo $this->output($ret, $ct);
//                        break;
                    case 5005:
                        $ret = $this->smodel->shuttle_searchroute($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5030:
                        $ret = $this->semodel->cabPath($keydata[1], $keydata[2], $keydata[3]);
                        echo $this->output($ret, $ct);
                        break;
//                    case 5040:
//                        $ret = $this->smodel->shuttle_insert($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20], $keydata[21], $keydata[22],$keydata[23],$keydata[24],$keydata[25]);
//                        echo $this->output($ret, $ct);
//                        break;
                    case 5045:
                        $ret = $this->smodel->shuttle_booking_insert($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20],$keydata[21],$keydata[22],$keydata[23]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5050:
                        $ret = $this->smodel->paytmaddmoney_request($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5055:
                        $ret = $this->smodel->paytmaddmoney_response($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8],$keydata[9],$keydata[10],$keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5060:
                        $ret = $this->smodel->profile_update($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5],$keydata[6],$keydata[7],$keydata[8],$keydata[9],$keydata[10]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5090:
                        $ret = $this->smodel->change_password($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8462:
                        $ret = $this->smodel->booking_details($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8463:
                        $ret = $this->smodel->booking_list($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8465:
                        $ret = $this->smodel->booking_cancel($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$mobileno);
                        echo $this->output($ret, $ct);
                        break;
                    case 8466:
                        $ret = $this->smodel->shuttle_support();
                        echo $this->output($ret, $ct);
                        break;
                    case 8467:
                        $ret = $this->smodel->shuttle_support_log($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8468:
                        $this->smodel->shuttle_invoice($keydata[1]);
                        break;
                    case 8469:
                        //$this->smodel->shuttle_termscondition();
                        $this->load->view('aboutus');
                        break;
                    case 8470:
                        $ret = $this->smodel->shuttle_notification($keydata[1], $keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8471:
                        $ret = $this->smodel->emp_app_panic($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5],$keydata[6],$keydata[7]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8472:
                        $ret = $this->smodel->tracking($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8473:
                        $ret = $this->smodel->payment_history($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8474:
                        $ret = $this->smodel->booking_cancel_confirm($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8475:
                        $ret = $this->smodel->booking_reshedule($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8476://insert employee feedback details
                        $ret = $this->smodel->emp_feedback($keydata[1], $keydata[2], $keydata[3],$keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8477:
                        $ret = $this->smodel->tracking_updated($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9000:
                        $ret = $this->smodel->emp_logout($keydata[1]);
                        echo $this->output($ret);
                        break;
                    
                    case 5082:
                        if($mobileno=="9894400086")
                        {
                                $this->smodel->paytmwalletmoney_refund($keydata[1], $keydata[2], $keydata[3], $keydata[4]);//$order_id, $txnid, $refundamt, $mobile_no
                                break;                        
                        }
                    default:
                        $element_array = array('status' => 4);
                        echo $this->output($element_array, $ct);
                        break;
                }
//            }
//            else
//            {
//                $element_array = array('status' => 5);
//                echo $this->output($element_array, $ct);
//            }                
        }        
      }
      else
      {
          $element_array = array('status' => 6);
          echo $this->output($element_array, $ct);
      }
    }
    function encrypt($message, $initialVector, $secretKey) {
        return base64_encode(
                mcrypt_encrypt(
                        MCRYPT_RIJNDAEL_128, md5($secretKey), $message, MCRYPT_MODE_CFB, $initialVector
                )
        );
    }

    function decrypt($data, $key, $secretKey) {
        $decode = base64_decode($data);
        return mcrypt_decrypt(
                MCRYPT_RIJNDAEL_128, md5($key), $decode, MCRYPT_MODE_CFB, $secretKey
        );
    }

    public function output($element_array, $ct) {
        if (is_array($element_array)) {
            if ($ct == "ip") {
                $valreturn = $element_array;
            } else {
                $string = json_encode($element_array);
                $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
                $valreturn = array('xyssdff' => $returnval);
            }
        } 
        //else {
//            if ($ct == "ip") {
//                $valreturn = array('status' => $element_array);
//            } else {
//                $return = $this->encrypt($element_array, ENCRYPT_KEY1, ENCRYPT_KEY2);
//                $valreturn = array('status' => $return);
//            }
//        }
        return json_encode($valreturn);
    }

}
