<?php

set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
//include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
//include(APPPATH . 'libraries/Shuttledao.php');
//include(APPPATH . 'libraries/EncdecPaytm.php');
include(APPPATH . 'libraries/ShuttleStagedao.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class ShuttleStagemdl extends CI_Model {

    function __construct() {
        parent::__construct();
        $this->load->model('ShuttleElasticmdl', 'semodel');
        $this->load->model('ShuttleCommonmdl', 'cmodel');
        $this->load->model('Shuttlemdl', 'smodel');
    }

    public function shuttle_stage_searchroute($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $stagedao = new ShuttleStagedao();
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->semodel->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->semodel->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->semodel->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->semodel->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
        //print_r($pickarray);

        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            foreach ($output as $val) {
                $p_time = $this->cmodel->sec_to_time($val['APROX_PICK']);
                $d_time = $this->cmodel->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d = $val['DROP_STOP_ID'];

               // $p_route_order = $dao->get_route_order($route_id, $stop_id);
                //$d_route_order = $dao->get_route_order($route_id, $stop_id_d);

                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $stagedao->get_seats($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                       // $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $routeid_name = $row['Route_ID'];
                        $travel_type = $row['Travel_Type'];
                        $tariff_type = $row['Tariff_Type'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));                       
                        
                        $p_order = $dao->get_route_order($route_id,$stop_id);
                        $d_order = $dao->get_route_order($route_id,$stop_id_d);
                        $p_route_order=$p_order['Route_Order'];
                        $d_route_order=$d_order['Route_Order'];

                        $seats = $this->check_seats_instages($p_route_order, $d_route_order, $trip_id, 'check');
                        if ($seats == 'true') {                            
                            $p_time=$p_order['Stop_Time'];
                            $d_time=$d_order['Stop_Time'];
                            $comparetime = $curdate . " " . $stime;
                            $secs = strtotime($stime) - strtotime("00:00:00");
                            $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                            $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));

                            if ($resheduledate != '0000-00-00') {
                                $shuttletime = "ANext";
                                $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');                                
//                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
//                                $totdist=round($traveldist['dist']);
//                                $traveltime = $dao->calculate_time($route_id, $p_route_order, $d_route_order);    
                                
                                $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));                               
                                $totdist=round($traveldist[0]);
                                $traveltime = $traveldist[1];
                                $package = $this->smodel->tariff_cal($totdist, $ttype, $route_type, $vehicle_id, 0,$branchid,$tariff_type,$shedule_date,0);                                
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $totdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order,'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);                               
                            } else {                               
                                    if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                    $shuttletime = "ANext";
                                    if ($route_name == TRIP_P) {
                                        $sts = ($a_p_picktime <= $seat_count_time && $seats_used > 0) ? 'true' :
                                                ($a_p_picktime > $seat_count_time && $seats_used >= 0) ? 'true' : 'false';
                                    } else {
                                        $sts = 'true';
                                    } 
                                    if ($sts == 'true') {
                                        $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                        $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
//                                        $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
//                                        $totdist=round($traveldist['dist']);
//                                        $traveltime = $dao->calculate_time($route_id, $p_route_order, $d_route_order);  
                                        $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));                               
                                        $totdist=round($traveldist[0]);
                                        $traveltime = $traveldist[1];
                                        $package = $this->smodel->tariff_cal($totdist, $ttype, $route_type, $vehicle_id, 0,$branchid,$tariff_type,$shedule_date,0);                                        
                                        $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 
                                            'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $totdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order,'routeid_name' => $routeid_name, 'travel_type' => $travel_type,'package_details' => $package);
                                    }
                                } else if ($shedule_date > $curdate) {
                                    $days=strtotime($shedule_date)-strtotime($curdate);
                                    $diffDays = (floor($days / (60*60*24) )); 
                                    $shuttletime=$diffDays==0?'ANext':$diffDays==1?'BTomorrow':'C'.date('l', strtotime($shedule_date));
                                    $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
//                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
//                                    $totdist=round($traveldist['dist']);
//                                    $traveltime = $dao->calculate_time($route_id, $p_route_order, $d_route_order); 
                                    $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));                               
                                    $totdist=round($traveldist[0]);
                                    $traveltime = $traveldist[1];
                                    $package = $this->smodel->tariff_cal($totdist, $ttype, $route_type, $vehicle_id, 0,$branchid,$tariff_type,$shedule_date,0);                                   
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                        'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $totdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order,'routeid_name' => $routeid_name, 'travel_type' => $travel_type,'package_details' => $package);
                                }
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->cmodel->sec_to_time($val['APROX_PICK']);
                $d_time = $this->cmodel->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d = $val['DROP_STOP_ID'];

                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    //$seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    $seatavial = $stagedao->get_seats($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $travel_type = $row['Travel_Type'];
                        $tariff_type = $row['Tariff_Type'];
                        $routeid_name = $row['Route_ID'];
                        $p_order = $dao->get_route_order($route_id,$stop_id);
                        $d_order = $dao->get_route_order($route_id,$stop_id_d);
                        $p_route_order=$p_order['Route_Order'];
                        $d_route_order=$d_order['Route_Order'];
                        $seats = $this->check_seats_instages($p_route_order, $d_route_order, $trip_id, 'check');
                        if ($seats == 'true') {                            
                            $p_time=$p_order['Stop_Time'];
                            $d_time=$d_order['Stop_Time'];
                            $comparetime = $curdate . " " . $stime;
                            $secs = strtotime($stime) - strtotime("00:00:00");
                            $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                            if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                                $shuttletime = "ANext";
                                $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
//                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
//                                $totdist=round($traveldist['dist']);
//                                $traveltime = $dao->calculate_time($route_id, $p_route_order, $d_route_order); 
                                
                                $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));                               
                                $totdist=round($traveldist[0]);
                                $traveltime = $traveldist[1];
                                $package = $this->smodel->tariff_cal($totdist, $ttype, $route_type, $vehicle_id, 0,$branchid,$tariff_type,$shedule_date,0);                               
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $totdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order,'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            } else {
                                if ($shedule_date == $curdate && $stime > $curtime) {                                    
                                    $shuttletime = "ANext";                                    
                                    $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                    $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
//                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
//                                    $totdist=round($traveldist['dist']);
//                                    $traveltime = $dao->calculate_time($route_id, $p_route_order, $d_route_order); 
                                    
                                    $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));                               
                                    $totdist=round($traveldist[0]);
                                    $traveltime = $traveldist[1];
                                    $package = $this->smodel->tariff_cal($totdist, $ttype, $route_type, $vehicle_id, 0,$branchid,$tariff_type,$shedule_date,0);                                    
                                    $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                        'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $totdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order,'routeid_name' => $routeid_name, 'travel_type' => $travel_type,'package_details' => $package);
                                } else if ($shedule_date > $curdate) {
                                    $days=strtotime($shedule_date)-strtotime($curdate);
                                    $diffDays = (floor($days / (60*60*24) )); 
                                    $shuttletime=$diffDays==0?'ANext':$diffDays==1?'BTomorrow':'C'.date('l', strtotime($shedule_date));
                                    $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                    $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
//                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
//                                    $totdist=round($traveldist['dist']);
//                                    $traveltime = $dao->calculate_time($route_id, $p_route_order, $d_route_order); 
                                    $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));                               
                                    $totdist=round($traveldist[0]);
                                    $traveltime = $traveldist[1];
                                    $package = $this->smodel->tariff_cal($totdist, $ttype, $route_type, $vehicle_id, 0,$branchid,$tariff_type,$shedule_date,0);                                    
                                    $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                        'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $totdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order,'routeid_name' => $routeid_name, 'travel_type' => $travel_type,'package_details' => $package);
                                }
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->cmodel->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->cmodel->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
        $respmsg="Currently we are not operating between your searched place and we will try to start our shuttle service for your request";
        $resptitle="No rides available";
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->semodel->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                 $element = array('status' => 0, 'Message' => $respmsg, 'title'=>$resptitle);
            }
        } else {
            if (count($element_arr) == 0) {
                $this->semodel->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                 $element = array('status' => 0, 'Message' => $respmsg, 'title'=>$resptitle);
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }

    public function check_seats_instages($p_route_order, $d_route_order, $trip_id, $sts) {
        $dao = new Shuttledao();
        $stagedao = new ShuttleStagedao();
        $i = 0;
        $route_diff = 0;
        $output = array();
        $retarray = array();
        $ret = "";
        $output = $stagedao->update_stages($trip_id, $p_route_order, $d_route_order);
        $arr_size = count($output);

        $route_diff = $d_route_order - $p_route_order;
        if ($arr_size > 0 && isset($output['ret']) == 'true') {
            $ret = "true";
            $retarray = array('status' => 1, 'pickup_order' => $p_route_order, 'drop_order' => $d_route_order, 'stage_arr' => $output);
        } else {
            if ($arr_size == $route_diff) {
                $ret = "true";
            } else {
                $ret = "false";
            }
            $retarray = array('status' => 2, 'pickup_order' => $p_route_order, 'drop_order' => $d_route_order, 'stage_arr' => $output);
        }
        if ($sts == 'booking' && $ret == 'true')
            return $retarray;
        else
            return $ret;
    }

    public function shuttle_stage_booking_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d,$stopid_p, $stopid_d, $noofdays,$noofrides, $sdate, $edate,$route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $branch_id,$totdist, $totfare, $payment_category, $p_routeorder_p, $p_routeorder_d, $d_routeorder_p, $d_routeorder_d) {
        $element_array = array();
        $getdate = $this->cmodel->get_datetime();
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
        if ($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0 || $dlat == 0 || $dlong == 0 || $ssotoken == 'null') {
            $element_array = array('status' => 0, 'message' => 'Subsciption Failed');
            return $element_array;
            exit;
        }
        if ($noofdays == 1 && $branch_id != 1) {
            $element_array = array('status' => 0, 'message' => 'Subsciption only allowed');
            return $element_array;
            exit;
        }
        if ($noofdays > 1) {
            $where = "EmployeeId='$eid' and '$sdate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $this->cmodel->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if (date("w", strtotime($sdate)) == 6 || date("w", strtotime($sdate)) == 0) {
                $element_array = array('status' => 0, 'message' => 'There is no shuttle service for this date');
                return $element_array;
                exit;
            }
            $subscribtionsts = 0;
        }
        $message = $this->stage_seats_confirmed_sts($ttype, $tripid_p, $tripid_d, $p_routeorder_p, $p_routeorder_d, $d_routeorder_p, $d_routeorder_d);
        if ($message == "Confirmed") {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {               
                $data = array('BranchId' => $branch_id, 'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => trim($ppoint), 'DropPoint' => trim($dpoint), 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $totfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $sdate, 'EndDate' => $edate, 'NoofDays' => $noofdays, 'NoofRides' => $noofrides, 'TotalPaidAmt' => $totfare, 'RouteType' => $route_type, 'TariffId' => 0);
                $cnt = $this->cmodel->c_insert('shuttle_booking', $data);
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = $payment_category == "paytm" ? "P" . strtotime(date('YmdHis')) . $id : "J" . strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $this->cmodel->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {
                        if ($payment_category == "paytm" || empty($payment_category)) {
                            $response = $this->stage_paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $totfare, $stopid_p, $stopid_d, $p_routeorder_p, $p_routeorder_d, $d_routeorder_p, $d_routeorder_d);
                            $element_array = array('status' => 1, 'message' => $response);
                        } else {
                            $response = $this->smodel->jiomoney_debitbalance($paymentno, $mobileno, $totfare, $stopid_p, $stopid_d, $p_routeorder_p, $p_routeorder_d, $d_routeorder_p, $d_routeorder_d);
                            if (is_array($response)) {
                                $element_array = array('status' => 1, 'message' => 'success', 'jio_response' => $response);
                            } else {
                                $element_array = array('status' => 1, 'message' => $response);
                            }
                        }
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscribtion already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
        // $this->ShuttleElasticmdl->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, json_encode($element_array));
        return $element_array;
    }

    public function stage_seats_confirmed_sts($ttype, $tripid_p, $tripid_d, $p_routeorder_p, $p_routeorder_d, $d_routeorder_p, $d_routeorder_d) {
        $dao = new Shuttledao();
        $avail_p = 'false';
        $avail_d = 'false';
        $message = "";
        if ($ttype == "B") {
            $avail_p = $this->check_seats_instages($p_routeorder_p, $p_routeorder_d, $tripid_p, 'check');
            $avail_d = $this->check_seats_instages($d_routeorder_p, $d_routeorder_d, $tripid_d, 'check');
            if ($avail_p == 'true' && $avail_d == 'true') {
                $message = "Confirmed";
            } else if ($avail_p == 'true' && $avail_d == 'false') {
                $message = "Pickup routes only available";
            } else if ($avail_p == 'false' && $avail_d == 'true') {
                $message = "Return routes only available";
            } else {
                $message = "No seats available";
            }
        } else {
            $avail_p = $this->check_seats_instages($p_routeorder_p, $p_routeorder_d, $tripid_p, 'check');
            if ($avail_p == 'true') {
                $message = "Confirmed";
            } else {
                $message = "No seats avialable";
            }
        }
        return $message;
    }

    public function stage_booking_reshedule($old_trip_id, $roster_id, $trans_id, $new_trip_id, $stop_id, $eid, $emplat, $emplong, $p_routeorder_p, $p_routeorder_d) {
        $dao = new Shuttledao();
        $stagedao = new ShuttleStagedao();
        $sts = "false";
        $element_array = array();
        $category = 1;
        $ret = $dao->shuttle_booking_update($trans_id, $category, $roster_id, $eid, 4); //4-'reschedule
        if ($ret == 1) {
            $stagedao->stagebooking_payment_tokenno($new_trip_id, $trans_id, $stop_id, $eid, $p_routeorder_p, $p_routeorder_d);
            $sts = 'true';
        }

        $element_array = array('status' => $sts);
        return $element_array;
    }
    public function stage_paytmwalletmoney_withdraw($order_id, $ssotoken, $cust_id, $mobile_no, $fare, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        $encdec = new EncdecPaytm();
        //$dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $cur_time = $getdate['cur_date_time']; //have to check $ssotoken is null or not,$cust_id is null or not
        $checkSum = "";
        
        
//        $where = "PaymentNo='$order_id'";
//        $data1 = array('PaymentStatus' => 'S', 'UpdatedDatetime' => $cur_time);
//        $this->cmodel->c_update('shuttle_booking', $data1, $where);
//        $response = $this->stage_paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no, $stopid_p,$stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
//        exit;
//        
        $paramList = array();
        $paramList["MID"] = PAYTM_MID;
        $paramList["OrderId"] = $order_id;
        $paramList["IndustryType"] = PAYTM_INDUSTRY_TYPE_ID;
        $paramList["Channel"] = PAYTM_CHANNEL_ID_WEB;
        $paramList["TxnAmount"] = $fare;
        $paramList["AuthMode"] = "USRPWD";
        $paramList["ReqType"] = "WITHDRAW";
        $paramList["SSOToken"] = $ssotoken;
        $paramList["AppIP"] = $_SERVER["REMOTE_ADDR"];
        $paramList["Currency"] = "INR";
        $paramList["DeviceId"] = $mobile_no;
        $paramList["PaymentMode"] = "PPI";
        $paramList["CustId"] = $cust_id;
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CheckSum"] = $checkSum;
        $data = array('OrderId' => $order_id, 'CustId' => $cust_id, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => PAYTM_CHANNEL_ID_WEB, 'TxnAmount' => $fare, 'RequestType' => 'WITHDRAW', 'SsoToken' => $ssotoken,
            'CheckSumHash' => $checkSum, 'Currency' => 'INR', 'DeviceId' => $mobile_no, 'PaytmCustomerId' => $cust_id, 'AppIp' => $_SERVER["REMOTE_ADDR"], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
        $cnt = $this->cmodel->c_insert('shuttle_payment_request', $data);
        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_SERVER_MONEY_WITHDRAW, $paramList);
            if (isset($resparr['ResponseCode']) && isset($resparr['ResponseMessage']) && isset($resparr['Status'])) {
                $txnid = $resparr['TxnId'];
                $orderid = $resparr['OrderId'];
                $txnamt = $resparr['TxnAmount'];
                $respdata = array('OrderId' => $orderid, 'TxnId' => $txnid, 'TxnAmount' => $resparr['TxnAmount'], 'BankTxnId' => $resparr['BankTxnId'],
                    'ResponseCode' => $resparr['ResponseCode'], 'ResponseMessage' => $resparr['ResponseMessage'], 'Status' => $resparr['Status'], 'PaymentMode' => $resparr['PaymentMode'],
                    'BankName' => $resparr['BankName'], 'CheckSumHash' => $resparr['CheckSum'], 'PaytmCustomerId' => $resparr['CustId'], 'Mbid' => $resparr['MBID'], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
                $cnt = $this->cmodel->c_insert('shuttle_payment_response', $respdata);
                $stsarr = array();
                $stsarr['MID'] = PAYTM_MID;
                $stsarr['ORDERID'] = $orderid;
                //$respconfirm = $encdec->callAPI(PAYTM_TXNSTATUS_URL,$stsarr);                 
                if ($cnt > 0 && $resparr['Status'] == 'TXN_SUCCESS' && $resparr['ResponseMessage'] == 'Txn Successful.' && $resparr['ResponseCode'] == '01') {
                    // if ($cnt > 0 && $respconfirm['STATUS'] == 'TXN_SUCCESS' && $respconfirm['RESPMSG'] == 'Txn Successful.' && $respconfirm['RESPCODE'] == '01') {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'S', 'UpdatedDatetime' => $cur_time);
                    $this->cmodel->c_update('shuttle_booking', $data1, $where);
                    $response = $this->stage_paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no, $stopid_p,$stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
                } else {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'F', 'UpdatedDatetime' => $cur_time);
                    $this->cmodel->c_update('shuttle_booking', $data1, $where);
                    $response = "Payment Failed";
                   // $response = $this->stage_paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no, $stopid_p,$stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
                }
            } else {                
                $respdata = array('OrderId' => $order_id, 'ResponseMessage' => $resparr['Error'], 'CreatedTime' => $cur_time);
                $cnt = $this->cmodel->c_insert('shuttle_payment_response', $respdata);
                $response = "Payment Failed";
            }
        }
        return $response;
    }
    
    public function stage_paytmwithdraw_response($ORDERID, $txnid, $txnamt, $mobile_no, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        $dao = new Shuttledao();
        $stagedao = new ShuttleStagedao();
        $getdate = $this->cmodel->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];
        $empid = "";
        $stime = "";
        $etime = "";
        $ppoint = "";
        $dpoint = "";
        $tmode = "";
        $pamt = 0;
        $route_id = 0;
        $duration = 0;
        $branch_id = 0;
        try{
        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile_no, AES_ENCRYPT_KEY);
        $row = $dao->getshuttle_bookingdetails($ENC_MOBILE, $ORDERID);
        if ($row) {
            $branch_id = $row['branchid'];
            $stime = $row['StartTime'];
            $etime = $row['EndTime'];
            $ppoint = $row['PickupPoint'];
            $dpoint = $row['DropPoint'];
            $tmode = $row['TravelMode'];
            $pamt = $row['TotalPaidAmt'];
            $dist = $row['PackageKm'];
            $tripid_p = $row['PickupRosterId'];
            $tripid_d = $row['DropRosterId'];
            $empid = $row['EmployeeId'];
            $sdate = $row['StartDate'];
            $enddate = $row['EndDate'];
            $duration = $row['NoofDays'];
            $emp_id = $row['EmployeeId'];
            $noofrides = $row['NoofRides'];
            $email = $this->cmodel->AES_DECRYPT($row['EMAIL'], AES_ENCRYPT_KEY);
            $ename = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
            $msg = $duration == 1 ? " 1 day" : $duration . " days " . $noofrides . " rides";
            $smsmessage = "Dear $ename, Thanks for your subscriptions $msg plan.For any queries contact 04440003002, enjoy the ride!";
            $this->cmodel->insert_sms($branch_id, 'TOPSCS', $mobile_no, $smsmessage);
            //$ret = ($duration > 1) ? $this->smodel->shuttle_subscription_mail($ORDERID, $ename, $email, $empid, $mobile_no) : '';
            $date_to = $enddate;
            $date_from = strtotime($sdate);
            $date_to = strtotime($enddate);
            //$message = $this->seats_confirmed_sts($tmode, $tripid_p, $tripid_d);
            $message = $this->stage_seats_confirmed_sts($tmode,$tripid_p,$tripid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d); 
            if ($sdate >= $curdate && $enddate >= $curdate && $message == 'Confirmed') {

                // for ($i = $date_from; $i <= $date_to; $i+=86400) {
                for ($i = 0; $i < $duration; $i++) {
                    $shuttledate = date('Y-m-d', strtotime("+$i day", strtotime($sdate)));
                    if (date("w", strtotime($shuttledate)) == 6 || date("w", strtotime($shuttledate)) == 0) {
                        
                    } else {
                        if ($tmode == "B") {
                            $trip_fare = $pamt / 2;
                            $seat_cost = $pamt / $noofrides;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id = $row['Vehicle_ID'];
                                $route_name = $row['Route_Name'];
                                // $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist/2),$vehicle_id,'P',$dpoint,$route_name);
                                $stagedao->stage_seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'P', $dpoint, $route_name,$p_routeorder_p,$p_routeorder_d,$branch_id);
                            }
                            $row1 = $dao->get_routeid_sheduletime($tripid_d);
                            if ($row1) {
                                $route_id = $row1['Schedule_Route_ID'];
                                $schedule_time = $row1['Schedule_Time'];
                                $vehicle_id = $row1['Vehicle_ID'];
                                $route_name = $row1['Route_Name'];
                                $stagedao->stage_seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_d, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'D', $dpoint, $route_name,$p_routeorder_p,$p_routeorder_d,$branch_id);
                            }
                        } else {
                            $seat_cost = $pamt / $noofrides;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id = $row['Vehicle_ID'];
                                $route_name = $row['Route_Name'];
                                //$dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, $dist, $vehicle_id, 'P', $dpoint, $route_name,$branch_id);
                                $stagedao->stage_seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, $dist, $vehicle_id, 'P', $dpoint, $route_name,$p_routeorder_p,$p_routeorder_d,$branch_id);
                            }
                        }
                    }
                }
                // $this->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                // $resp = "Payment Success.Your ride start from 1st Jan 2018 to 31st Jan 2018";
                $resp = "Payment Success";
                $response_message = ((intval(date('m', strtotime($curdate)))) == 12 && (intval(date('Y', strtotime($curdate)))) == 2017 && $branch_id != 1) ? $resp : "Payment Success";
            } else {
                $this->smodel->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                $response_message = "No seats avilable.Your amount has been refunded";
            }
        }
        }
        catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
        return $response_message;
    }
    
    public function shuttle_stage_insert($route_id,$cab_id,$sch_date,$sch_time,$routeorder_p,$routeorder_d,$s_loc,$e_loc)
    {
        $stagedao = new ShuttleStagedao();
        $dao=new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        try{
        $cab=$stagedao->getcab_capacity($cab_id);
        $route_order=$stagedao->getroute_order($routeorder_p, $routeorder_d, $route_id);
        $dist=$dao->calculate_distance($route_id, $routeorder_p, $routeorder_d);
        $time=$dao->calculate_time($route_id, $routeorder_p, $routeorder_d);
        $tottime=$this->cmodel->sec_to_time($time);         
        $secs = strtotime($tottime) - strtotime("00:00:00");
        $end_time = date("H:i:s", strtotime($sch_time) + $secs);
        $capacity=$cab['seatcount'];
        $veh_id=$cab['vehid'];
        $vendor_id=$cab['vendorid'];
        $branch_id=$cab['branchid'];
        $data=array('Schedule_Route_ID'=>$route_id,'Total_Capacity'=>$capacity,'Seats_Used'=>0,'Vehicle_ID'=>1,
            'Schedule_Date'=>$sch_date,'Schedule_Time'=>$sch_time,'status'=>2,'Created_Date'=>$cur_date_time,'Created_By'=>$cab_id);
        $cnt=$this->cmodel->c_insert('shuttle_trip',$data);
        $id = $this->db->insert_id();
        $route_count=count($route_order);
        for($i=1;$i<=$capacity;$i++)
        {
            $data1[]=array('Trip_ID'=>$id,'Seat_Number'=>$i);
           // $this->cmodel->c_insert('shuttle_trip_seat',$data1);
           // $trip_seat_id = $this->db->insert_id();
            for($j=0;$j<$route_count;$j++)
            {
                $stage_id=$route_order[$j]['Route_Order'];
                $data2[]=array('stage_id'=>$stage_id,'trip_id'=>$id ,'seat_number'=>$i);
               // $this->cmodel->c_insert('shuttle_stage',$data2);
            }
        }
        $c=$this->db->insert_batch('shuttle_trip_seat', $data1);
        $c1=$this->db->insert_batch('shuttle_stage', $data2); 
        if($c>0 && $c1>0)
        {
            $stime=$sch_date.' '.$sch_time;
            $etime=$sch_date.' '.$end_time;
            $roster=array('BRANCH_ID'=>$branch_id,'ROUTE_ID'=>'S'.$id,'ESTIMATE_START_TIME'=>$stime,'ESTIMATE_END_TIME'=>$etime,'TRIP_TYPE'=>'P',
                'START_LOCATION'=>$s_loc,'END_LOCATION'=>$e_loc,'VENDOR_ID'=>$vendor_id,'CAB_ID'=>$cab_id,'CAB_ALLOT_TIME'=>$stime,
                'CAB_CAPACITY_COUNT'=>$capacity,'TRIP_APPROVED_KM'=>$dist['dist'],'ROSTER_STATUS'=>9,'ACTIVE'=>1,'CREATED_BY'=>$cab_id,'created_at'=>$cur_date_time);
            $this->cmodel->c_insert('rosters',$roster);
            $element_array = array('status' => 1, 'message' => 'true');
        }
        else
        {
            $element_array = array('status' => 0, 'message' => 'false');
        }
        //$element_array = array('status' => 1, 'message' => $message);
        }
        catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
        return $element_array;
        
    }


}
