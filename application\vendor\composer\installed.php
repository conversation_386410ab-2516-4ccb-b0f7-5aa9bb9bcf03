<?php return array(
    'root' => array(
        'name' => 'codeigniter/framework',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '7d116f382eac51d91e7774969ebc61a49267923e',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'codeigniter/framework' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '7d116f382eac51d91e7774969ebc61a49267923e',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'elasticsearch/elasticsearch' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => '48b8a90e2b97b4d69ce42851c1b9e59f8054661a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../elasticsearch/elasticsearch',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'guzzlehttp/ringphp' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'reference' => '5e2a174052995663dd68e6b5ad838afd47dd615b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/ringphp',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'guzzlehttp/streams' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/streams',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mikey179/vfsstream' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => 'fc0fe8f4d0b527254a2dc45f0c265567c881d07e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikey179/vfsstream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'razorpay/razorpay' => array(
            'pretty_version' => '2.5.0',
            'version' => '2.5.0.0',
            'reference' => '96c0167176cf53e3da15640622e9b993a3450ec7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../razorpay/razorpay',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/promise' => array(
            'pretty_version' => 'v2.11.0',
            'version' => '2.11.0.0',
            'reference' => '1a8460931ea36dc5c76838fec5734d55c88c6831',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'reference' => '87932f52ffad70504d93f04f15690cf16a089546',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
