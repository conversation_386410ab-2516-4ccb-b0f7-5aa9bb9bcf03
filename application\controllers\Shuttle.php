<?php

error_reporting(1);
defined('BASEPATH') OR exit('No direct script access allowed');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttle
 *
 * <AUTHOR>
 */
class Shuttle extends CI_Controller {
    
     public function __construct() {
        parent::__construct();

        // Load form helper library
        $this->load->helper('form');

        // Load form validation library
        $this->load->library('form_validation');        
        header('Access-Control-Allow-Origin: *');
        header("Access-Control-Allow-Headers: X-API-KEY, Origin, X-Requested-With, Content-Type, Accept, Access-Control-Request-Method");
        header("Access-Control-Allow-Methods: GET,OPTIONS,PUT,DELETE,POST");
        $method = $_SERVER['REQUEST_METHOD'];
        if($method == "OPTIONS") {
            die();
        }
    
     
    }

    public function index() {        
        $ct = "";
        $headers = getallheaders(); 
        if (isset($headers)) {            
           // $key = $headers['PiaP79bER'];
            $mobileno = $headers['no'];
            $ct = $headers['ct'];
            if ($mobileno == 1) {                
                $models = array(
                    'Shuttlemdl' => 'smodel',
                    'ShuttleRefferalmdl' => 'refmodel',
                    'ShuttleStagemdl'=>'stagemodel'                    
                );  
                          
//                $this->load->model($models); 
//                $json = file_get_contents('php://input');
//                $obj = json_decode($json);
//                $ret=$this->smodel->shuttleimage_upload($obj->jdskuse);
////                $jdskuse = $this->input->post("jdskuse");
////                $ret=$this->smodel->shuttleimage_upload($jdskuse);
//                echo $this->output($ret, 'ip');
//                exit;
                $this->load->model($models); 
                $jdskuse = $this->input->post("jdskuse");
                $var = str_replace(' ', '+', $jdskuse);                
                if ($ct == "ip") {
                    $decrypted = $var;
                } else {
                    $decrypted = $this->decrypt($var, DECRYPT_KEY1, DECRYPT_KEY2);
                }
                $keydata = array();
                $keydata = explode('@@#', $decrypted);

               
                switch ($keydata[0]) {
                    case 5000:
                        $ret = $this->smodel->shuttle_registraton($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5015:
                        $ret = $this->smodel->shuttle_registraton_new($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9],$keydata[10],$keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5016:
                        $ret = $this->refmodel->shuttle_registration_withreferral($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9],$keydata[10],$keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5020:
                        $ret = $this->smodel->shuttle_otpcreation($keydata[1], $keydata[2],$ct);
                        echo $this->output($ret, $ct);
                        break;
                     case 5025:
                        $ret = $this->smodel->shuttle_otpresend($keydata[1],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 5010:
                        $ret = $this->smodel->shuttle_otpverified($keydata[1], $keydata[2], $keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 5012:
                        $ret = $this->smodel->shuttle_loginverification_web($keydata[1], $keydata[2],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 5070:
                        $ret = $this->smodel->forgot_password($keydata[1], $keydata[2],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 5080:
                        $ret = $this->smodel->generate_newpassword($keydata[1], $keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5081:
                        $this->smodel->insert_route_master($keydata[1], $keydata[2], $keydata[3], $keydata[4],$keydata[5],$keydata[6],$keydata[7],$ct);
                        break;
                    case 5083:
                        $this->smodel->insert_route_master_driver($keydata[1], $keydata[2], $keydata[3], $keydata[4],$keydata[5],$keydata[6],$ct);
                        break;                    
                    case 9002:
                        $ret = $this->smodel->getlocation();
                        echo $this->output($ret,$ct);
                        break;
                    case 9003:
                        $ret = $this->refmodel->shuttle_registration_withreferral($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9],$keydata[10],$keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret,$ct);
                        break;
                    case 9004:
                        $ret = $this->smodel->shuttle_notification_mail();
                        echo $this->output($ret,$ct);
                        break;
                    default:
                        $element_array = array('status' => 3);
                        echo $this->output($element_array, $ct);
                        break;
                }
            } else {
                $models = array(
                    'Shuttlemdl' => 'smodel',
                    'ShuttleElasticmdl' => 'semodel',
                    'FixedShuttlemdl' => 'fmodel',
                    'ShuttleCommonmdl'=>'cmodel',
                    'ShuttleStagemdl'=>'stagemodel' 
                );                
                $this->load->model($models);
                $ct = $headers['ct'];
                $ssvaluecheck = "";
                $ssid = $headers['idval'];
                $arrayval = array();                
                $ssvaluecheck = $this->smodel->ssvaluecheck($mobileno, $ssid,$ct);
                $arrayval = explode(',', $ssvaluecheck);
			//print_r($arrayval);
//                if ($arrayval[0] == 'true') 
//                {
                $jdskuse = $this->input->post("jdskuse");                
                $var = str_replace(' ', '+', $jdskuse);                
                $decrypted = "";
                if ($ct == "ip") {
                    $decrypted = $var;
                } else {
                    $decrypted = $this->decrypt($var, DECRYPT_KEY1, DECRYPT_KEY2);
                }
                
                $keydata = array();
                $keydata = explode('@@#', str_replace('+', ' ', $decrypted));
               // $this->semodel->shuttleApiRequestInsert($keydata[1], $decrypted);
//print_r($keydata);
                switch ($keydata[0]) {
                    case 3000:
                        $ret = $this->smodel->appversion($keydata[1], $keydata[2],$keydata[3],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 3050:
                        $ret = $this->smodel->reason($keydata[1]);
                        echo $this->output($ret, $ct);
                        break;
                    case 4000:
                        $ret = $this->fmodel->shuttle_fixed_routes($keydata[1]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5020:                        
                        $ret=$this->smodel->mobilenoverification($keydata[1], $keydata[2],$keydata[3],$keydata[4]);
                        echo $this->output($ret, $ct);
                        break;                                    
//                  case 4010:
//                        $ret = $this->fmodel->shuttle_fixed_route_search($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9]);
//                        echo $this->output($ret, $ct);
//                        break;
//                  case 5000:
//                        $ret = $this->smodel->shuttle_search($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12]);
//                        echo $this->output($ret, $ct);
//                        break;
                    case 5005:
                       // $ret = $this->smodel->shuttle_searchroute($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13]);
                        $ret = $this->smodel->shuttle_searchroute($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13],$keydata[14]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5030:
                        $ret = $this->semodel->cabPath($keydata[1], $keydata[2], $keydata[3]);
                        echo $this->output($ret, $ct);
                        break;
//                    case 5040:
//                        $ret = $this->smodel->shuttle_insert($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20], $keydata[21], $keydata[22],$keydata[23],$keydata[24],$keydata[25]);
//                        echo $this->output($ret, $ct);
//                        break;
                    case 5045:
                        $ret = $this->smodel->shuttle_booking_insert($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20],$keydata[21],$keydata[22],$keydata[23],$keydata[24],$keydata[25],$keydata[26],$keydata[27],$keydata[28]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5050:
                        $ret = $this->smodel->paytmaddmoney_request($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5055:
                        $ret = $this->smodel->paytmaddmoney_response($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8],$keydata[9],$keydata[10],$keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5060:
                        $ret = $this->smodel->profile_update($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5],$keydata[6],$keydata[7],$keydata[8],$keydata[9]);
                        echo $this->output($ret, $ct);
                        break;
                    case 5070:
                        $ret = $this->smodel->profile_update_new($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5],$keydata[6],$keydata[7],$keydata[8],$keydata[9],$keydata[10],$keydata[11],$keydata[12],$mobileno);
                        echo $this->output($ret, $ct);
                        break;                   

                    case 5090:
                        $ret = $this->smodel->change_password($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8462:
                        $ret = $this->smodel->booking_details($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8463:
                        $ret = $this->smodel->booking_list($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8465:
                        $ret = $this->smodel->booking_cancel($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10],$keydata[11],$keydata[12],$mobileno);
                        echo $this->output($ret, $ct);
                        break;
                    case 8466:
                        $ret = $this->smodel->shuttle_support();
                        echo $this->output($ret, $ct);
                        break;
                    case 8467:
                        $ret = $this->smodel->shuttle_support_log($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8468:
                        $this->smodel->shuttle_invoice($keydata[1]);
                        break;
                    case 8469:
                        //$this->smodel->shuttle_termscondition();
                        $this->load->view('aboutus');
                        break;
                    case 8470:
                        $ret = $this->smodel->shuttle_notification($keydata[1], $keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8471:
                        $ret = $this->smodel->emp_app_panic($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5],$keydata[6],$keydata[7]);
                        echo $this->output($ret, $ct);
                        break;
//                    case 8472:
//                        $ret = $this->smodel->tracking($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
//                        echo $this->output($ret, $ct);
//                        break;
                    case 8473:
                        $ret = $this->smodel->payment_history($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
//                    case 8474:
//                        $ret = $this->smodel->booking_cancel_confirm($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
//                        echo $this->output($ret, $ct);
//                        break;
                    case 8475:
                        $ret = $this->smodel->booking_reshedule($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8476://insert employee feedback details
                        $ret = $this->smodel->emp_feedback($keydata[1], $keydata[2], $keydata[3],$keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
		      case 8477:
                        $ret = $this->smodel->tracking_updated($keydata[1], $keydata[2], $keydata[3], $keydata[4]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9000:
                        $ret = $this->smodel->emp_logout($keydata[1]);
                        echo $this->output($ret);
                        break;
                    case 9001:
                        $origin="13.022962,80.176016"; $destination="12.9974873,80.2006371";$waypoints="12.9974873,80.2006371";
                        $ret=$this->cmodel->calkm($origin, $destination, $waypoints, 'both', 'driving');
                       $x = explode("-", $ret);
                        if ($x[1] > 0) {
                            $hours = floor($x[1] / 3600);
                            $mins = floor($x[1] / 60 % 60);
                            if ($hours == 0) {
                                $minutes = $mins . " mins";
                            } else {
                                $minutes = $hours . 'hrs -' . $mins . " mins";
                            }
                        } else {
                            $minutes = "NA";
                        }
                        //echo "minutes==".$minutes;
                        break;                   
                    case 5082:
                        if($mobileno=="9894400086")
                        {
                                $this->smodel->paytmwalletmoney_refund($keydata[1], $keydata[2], $keydata[3], $keydata[4]);//$order_id, $txnid, $refundamt, $mobile_no
                                break;                        
                        }
                    case 5084:
                        if($mobileno=="9894400086")
                        {
                            //5084@@#46@@#0@@#Fixed@@#0@@#22@@#42@@#monthly@@#2018-11-01
                                $this->smodel->tariff_cal($keydata[1], $keydata[2], $keydata[3], $keydata[4],$keydata[5],$keydata[6],$keydata[7],$keydata[8]);//$order_id, $txnid, $refundamt, $mobile_no
                                break;                        
                        }
                    case 7040:
                        $ret=$this->smodel->jiomoney_getcustomerinfo($keydata[1]);//$mobileno,$fname,$lname 
                        echo $this->output($ret,$ct);
                        break;
                    case 7050:
                        $ret=$this->smodel->jiomoney_registration($keydata[1], $keydata[2],$keydata[3]);//$mobileno,$fname,$lname 
                        echo $this->output($ret,$ct);
                        break;
                    case 7060:
                        $ret=$this->smodel->jiomoney_registration_otpverification($keydata[1],$keydata[2]);//$mobileno,$otp 
                        echo $this->output($ret,$ct);
                        break;
                    case 7065:
                        $ret=$this->smodel->jiomoney_deregistration($keydata[1]);//$mobileno
                        echo $this->output($ret,$ct);
                        break;
                    case 7070:
                        $ret=$this->smodel->jiomoney_checkbalance($keydata[1]);//$mobileno 
                        echo $this->output($ret,$ct);
                        break;
                    case 8000:
                        $ret=$this->smodel->jiomoney_debitbalance($keydata[1], $keydata[2]);//$mobileno,$txn_amount 
                        echo $this->output($ret,$ct);
                        break;
                    case 8010:
                        $ret=$this->smodel->jiomoney_debitbalanceotp($keydata[1],$keydata[2],$keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7],$keydata[8],$keydata[9]);//$tran_ref_no,$otp,$mobile_no,$stopid_p,$stopid_d  
                        echo $this->output($ret,$ct);
                        break;

                    case 8015:
                       // $ret=$this->smodel->ccavenue_transaction_initiate($keydata[1],$keydata[2],$keydata[3]);//$mobile_no,$tran_ref_no,$amount 
                           $ret=$this->smodel->ccavenue_response($keydata[1],$keydata[2],$keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7],$keydata[8]);
                        echo $this->output($ret,$ct);
                        break;

                     case 8016:
                       // $ret=$this->smodel->ccavenue_transaction_initiate($keydata[1],$keydata[2],$keydata[3]);//$mobile_no,$tran_ref_no,$amount 
                           $ret=$this->smodel->ccavenue_response_v1($keydata[1],$keydata[2],$keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7],$keydata[8]);
                        echo $this->output($ret,$ct);
                        break;

                     case 8017:                      
                           $ret=$this->smodel->razorpay_paymentresponse($keydata[1],$keydata[2]);
                           echo $this->output($ret,$ct);
                           break; 

                   
                    case 8020:
                        $ret=$this->smodel->jiomoney_add($keydata[1],$keydata[2],$keydata[3]);//$mobile_no,$tran_ref_no,$amount 
                        echo $this->output($ret,$ct);
                        break;
                    case 8030:
                        $ret=$this->smodel->update_rating($keydata[1],$keydata[2]);//$auto_id,$rating_sts,$rating_value 
                        echo $this->output($ret,$ct);
                        break;
                    case 8040:
                        $ret = $this->stagemodel->shuttle_stage_searchroute($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8045:
                        $ret = $this->stagemodel->shuttle_stage_booking_insert($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20],$keydata[21],$keydata[22],$keydata[23],$keydata[24],$keydata[25],$keydata[26],$keydata[27],$keydata[28],$keydata[29],$keydata[30],$keydata[31]);
                        echo $this->output($ret, $ct);
                        break;
                    case 8075:
                        $ret = $this->stagemodel->stage_booking_reshedule($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8],$keydata[9],$keydata[10]);
                        echo $this->output($ret, $ct);
                        break;                    
                   case 9005:
                       // $ret = $this->smodel->shuttle_searchroute($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13]);
                        $ret = $this->smodel->shuttle_searchroute_corp($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9015:
                        $ret = $this->smodel->profile_update_v1($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5],$keydata[6],$keydata[7],$keydata[8],$keydata[9],$keydata[10],$keydata[11],$keydata[12],$keydata[13],$keydata[14],$keydata[15]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9025:
                        $ret = $this->smodel->profile_inactive_v1($keydata[1],$keydata[2],$keydata[3]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9030:
                        $ret = $this->smodel->shuttle_booking_insert_v1($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20],$keydata[21],$keydata[22],$keydata[23],$keydata[24],$keydata[25],$keydata[26],$ct);
                        echo $this->output($ret, $ct);
                        break;
                    case 9035:
                        $ret = $this->fmodel->shuttle_fixed_routes_v1($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9037:
                        $ret = $this->smodel->shuttle_searchroute_driver($keydata[1],$keydata[2],$keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7],$keydata[8]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9038:
                        $ret = $this->stagemodel->shuttle_stage_insert($keydata[1],$keydata[2],$keydata[3],$keydata[4],$keydata[5],$keydata[6],$keydata[7],$keydata[8]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9039:
                        $ret = $this->smodel->getImagePath($keydata[1],$keydata[2]);
                        echo $this->output($ret, $ct);
                        break;
                    case 9040:
                        $ret = $this->smodel->shuttle_booking_insert_without_payment($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16], $keydata[17], $keydata[18], $keydata[19], $keydata[20],$keydata[21],$keydata[22],$keydata[23],$keydata[24],$keydata[25],$ct);
                        echo $this->output($ret, $ct);
                        break;
		      case 9041:
                        $ret = $this->smodel->shuttle_searchroute_v1($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13],$keydata[14]);
                        echo $this->output($ret, $ct);
                        break;
            case 9042:
                $ret = $this->smodel->week_roster($keydata[1],$keydata[2],$keydata[3]);
                echo $this->output($ret, $ct);
                break;
            case 9043:
                $ret = $this->smodel->week_roster_insert($keydata[1],$keydata[2],$keydata[3],$keydata[4],$keydata[5],$keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13],$keydata[14],$ct);
                echo $this->output($ret, $ct);
                break;
		      case 9044:
                        $ret = $this->smodel->adhoc_shuttle_searchroute($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11],$keydata[12],$keydata[13],$keydata[14]);
                        echo $this->output($ret, $ct);
                        break;
			 case 9045:
                        $ret = $this->smodel->insert_roster_web($keydata[1], $keydata[2]);
                        echo $this->output($ret, $ct);
                        break;

            case 9046:
                $ret = $this->fmodel->shuttle_fixed_routes_v2($keydata[1],$keydata[2]);
                echo $this->output($ret, $ct);
                break;
            case 9047:
                $ret = $this->smodel->shuttle_booking_insert_v2($keydata[1], $keydata[2], $keydata[3], $keydata[4], $keydata[5], $keydata[6], $keydata[7], $keydata[8], $keydata[9], $keydata[10], $keydata[11], $keydata[12], $keydata[13], $keydata[14], $keydata[15], $keydata[16],$ct);
                echo $this->output($ret, $ct);
                break;
            case 9048:
                $ret = $this->smodel->booking_list_v2($keydata[1],$keydata[2]);
                echo $this->output($ret, $ct);
                break;
            case 9049:
                $ret = $this->fmodel->shuttle_vehicle_tracking_v3($keydata[1],$keydata[2]);
                echo $this->output($ret, $ct);
                break;


                    default:
                        $element_array = array('status' => 4);
                        echo $this->output($element_array, $ct);
                        break;
                }

                
//          }
//            else
//            {
//                $element_array = array('status' => 5);
//                echo $this->output($element_array, $ct);
//            }               
        }        
      }
      else
      {
          $element_array = array('status' => 6);
          echo $this->output($element_array, $ct);
      }
    }
    function encrypt($message, $initialVector, $secretKey) {
        return base64_encode(
                mcrypt_encrypt(
                        MCRYPT_RIJNDAEL_128, md5($secretKey), $message, MCRYPT_MODE_CFB, $initialVector
                )
        );
    }

    function decrypt($data, $key, $secretKey) {
        $decode = base64_decode($data);
        return mcrypt_decrypt(
                MCRYPT_RIJNDAEL_128, md5($key), $decode, MCRYPT_MODE_CFB, $secretKey
        );
    }

    public function output($element_array, $ct) {
	//echo  json_encode($element_array);

        if (is_array($element_array)) {
            if ($ct == "ip") {
                $valreturn = $element_array;
            } else {
                $string = json_encode($element_array);
                $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
                $valreturn = array('xyssdff' => $returnval);
            }
             return json_encode($valreturn);
        } 
        else {
            return $element_array;
        }
       
    }

}
