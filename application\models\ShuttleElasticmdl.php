<?php
include(APPPATH . 'vendor/autoload.php');

use Elasticsearch\ClientBuilder;
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of ShuttleElasticmdl
 *
 * <AUTHOR>
 */
class ShuttleElasticmdl extends CI_Model{
   protected $client;

    public function __construct() {
        $this->client = ClientBuilder::create()->build();
        //$this->client = ClientBuilder::create()->setHosts(['hosts' => '*************:9200'])->build();
    }
    
    public static function date_time() {
        date_default_timezone_set(ES_TIME_ZONE);
        $cur_datetime = date('Y-m-d H:i:s');
        $cur_date = date('Y-m-d');
        $cur_time = date('H:i:s');
        return $cur_date . 'T' . $cur_time;
    }
    
    public function eindex($index, $type) {

        $itemsindex = [
            'index' => [
                '_index' => $index,
                '_type' => $type,
            ]
        ];
        return $itemsindex;
    }
    
    public function matchPhrase($field, $value) {
        $matchphrase = [
            'match_phrase' => [
                $field => $value
            ]
        ];
        return $matchphrase;
    }
    
    public function singleRange($ope, $field, $value) {
        $item = [
            'range' => [
                $field => [
                    $ope => $value
                ]
            ]
        ];
        return $item;
    }
    
    public function doubleRange($greaterope, $greatervalue, $lesserope, $lesservalue, $field) {
        $item = [
            'range' => [
                $field => [
                    $greaterope => $greatervalue,
                    $lesserope => $lesservalue
                ]
            ]
        ];
        return $item;
    }
    
    public function geoDistance($field,$lat,$lng) {
        $item = [
            '_geo_distance' => [
                $field => [
                    'lat' => $lat,
                    'lon' => $lng
                ],
                'order' => 'asc',
                'unit' => ES_SEARCH_UNIT,
                'distance_type' => ES_SEARCH_TYPE
            ]
        ];
        return $item;
    }

    //Result build
    
    private function buildCollection($items) {
        if ($i < count($items['hits']['hits'])) {
            for ($i = 0; $i < count($items['hits']['hits']); $i++) {
                $doc[] = $items['hits']['hits'][$i]['_source'];
            }
            $docs = array_reverse($doc);
            $res = ['RESULT' => $docs];          
        } else {
            $res = ['RESULT' => 'No Record'];
        }
        return $res;
    }
    
    //Cab navigation data
    public function cabNavigation($cabid, $gps_date) {
        $params = $this->gpsCabNavigation($cabid, $gps_date);       
        $response = $this->client->search($params);        
        $element_array = $this->buildCollection($response);
        return $element_array;
        //echo $this->output($element_array, $ct);
    }    

    public function gpsCabNavigation($cabid, $gps_date) {
         $must[] = $this->matchPhrase(ES_FIELD_CAB_ID, $cabid);
        if ($gps_date == ES_DEFAULT_DATE) {
            $size=ES_SIZE_SINGLE;
        }else{
            $size=ES_SIZE_THIRTY;            
            $must[] = $this->singleRange('gte', ES_FIELD_GPS_DATE, $gps_date);
        }       
            $itemsGpsSearch = [
                "size" => $size,
                'index' => ES_INDEX,
                'type' => ES_TYPE_GPS_LOGGING,
                'body' => [
                    '_source'=> [ES_FIELD_POSITION,ES_FIELD_GPS_DATE],
                    'query' => [
                        'bool' => [
                            'must' => $must
                        ]
                    ],
                    "sort" => [
                        [
                            "GPS_DATE" => [
                                "order" => "desc"
                            ]
                        ]
                    ]
                ]
            ];
        
        return $itemsGpsSearch;
    }
    
    //Shuttle Route path
    
     public function cabPath($route_id, $from, $to) {
        $params = $this->gpsCabPath($route_id, $from, $to);             
        $response = $this->client->search($params);        
        $element_array = $this->buildCollection($response);
        return $element_array;
        //echo $this->output($element_array, $ct);
    }
    
    public function gpsCabPath($route_id, $from, $to) {        
        $must[] = $this->matchPhrase(ES_FIELD_ROUTE_ID, $route_id);
		$must[] = $this->matchPhrase('ACTIVE_STATUS', "1");
        //$must[] = $this->doubleRange('gte', $from, 'lte', $to, ES_FIELD_ROUTE_ORDER);     
        if($from!='0000-00-00'){
           $must[] = $this->doubleRange('gte', $from, 'lte', $to, ES_FIELD_ROUTE_ORDER); 
        }         

        $itemsGpsPath = [
            "scroll" => ES_TIME_SCROLL, // how long between scroll requests. should be small!
            "size" => ES_SIZE_MAX, // how many results *per shard* you want back
            'index' =>ES_INDEX,
            'type' => ES_TYPE_PATH,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $must
                    ]
                ],
                "sort" => [
                    [
                        ES_FIELD_ROUTE_ORDER => [
                            "order" => "asc"
                        ]
                    ]
                ]
            ]
        ];
        return $itemsGpsPath;
    }
    
    
    //search route path


     //
     public function getSearchGroup($clat, $clng,$branchid ,$type,$routetype) {
        $params = $this->searchRoutePath($clat, $clng, $branchid,$routetype);
       // print_r($params);

        $items = $this->client->search($params);
 // print_r($items);
        $result = array();
        for ($i = 0; $i < count($items['aggregations']['top-uids']['buckets']); $i++) {
            //$pickupDistance = $items['aggregations']['top-uids']['buckets'][$i]['top_uids_hits']['hits']['hits'][0]['sort'][1];
           // $dropDistance = $items['aggregations']['top-uids']['buckets'][$i]['top_uids_hits']['hits']['hits'][0]['sort'][2];
           // if (floatval($pickupDistance) < floatval($dropDistance)) {
                $searchResponse[$i] = $items['aggregations']['top-uids']['buckets'][$i]['top_uids_hits']['hits']['hits'][0]['_source'];
                if ($type == 'D') {
                    $dtime=array("APROX_DROP" => $searchResponse[$i]['APROX_DURATION']);
                    $drop = array("DROP_POSITION" => $searchResponse[$i]['POSITION']);
                     $dloc = array("DROP_LOCATION" => $searchResponse[$i]['LOCATION']);
                    $ddate = array("DROP_DATE" => $searchResponse[$i]['ROUTE_DATE']);
                    $result[] = array_merge($searchResponse[$i], $drop,$dloc,$dtime,$ddate);
                } else {
                    $ptime=array("APROX_PICK" => $searchResponse[$i]['APROX_DURATION']);
                    $pick = array("PICK_POSITION" => $searchResponse[$i]['POSITION']);
                    $ploc = array("PICK_LOCATION" => $searchResponse[$i]['LOCATION']);
                    $pdate = array("PICK_DATE" => $searchResponse[$i]['ROUTE_DATE']);
                    $result[] = array_merge($searchResponse[$i],$pick,$ploc,$ptime,$pdate);
                }
           // }
        }
       // print_r($result);
        return $result;
        
    }

    public function searchRoutePath($clat,$clng,$branchid,$routetype) { 
        $dist= ES_SEARCH_KM;       
       // $must[] = $this->matchPhrase(ES_FIELD_BRANCH_ID, $branchid);
	     $must[] = $this->matchPhrase('ACTIVE_STATUS', "1");
        if($routetype==ES_SEARCH_FIXED){
            $must[] = $this->matchPhrase(ES_FIELD_ROUTE_TYPE, 'FIXED');
            $dist=ES_FIX_SEARCH_KM; 
        }        
        $sort[]=$this->geoDistance(ES_FIELD_POSITION, $clat, $clng);       
        $itemsSearch = [
                    'size' => ES_SIZE_ZERO,
                    'index' =>ES_INDEX,
                    'type' => ES_TYPE_PATH,
                    'body' => [
                    'query' => [
                        'bool' => ['must' => $must,
                            'filter' => [
                                'geo_distance' => [
                                    'distance' => $dist,
                                    ES_FIELD_POSITION => [
                                        'lat' => $clat,
                                        'lon' => $clng
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'aggs' => [
                        'top-uids' => [
                            'terms' => [
                                'field' => ES_FIELD_ROUTE_ID,
                                'size' => ES_SIZE_MAX
                            ],
                            'aggs' => [
                                'top_uids_hits' => [
                                    'top_hits' => [
                                        'sort' => $sort,
                                        'size' => ES_SIZE_SINGLE
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
        ];      
        return $itemsSearch;
    }
    
    ///......shuttle search unavailable insert..............
    public function shuttleUnavailableInsert($pickuplat, $pickuplng, $droplat, $droplng, $pickuptime, $droptime
    , $traveltype, $rescheduletime, $searchtype, $empid, $branchid, $category) {
        $params['body'][] = $this->eindex(ES_INDEX, ES_TYPE_SHUTTLE_UNAVAILABLE);
        $params['body'][] = $this->unavailableShuttle($pickuplat, $pickuplng, $droplat, $droplng, $pickuptime, $droptime
                , $traveltype, $rescheduletime, $searchtype, $empid, $branchid, $category);
        $response = $this->client->bulk($params);
    }
    
    public function unavailableShuttle($pickuplat, $pickuplng, $droplat, $droplng, $pickuptime, $droptime
                , $traveltype, $rescheduletime, $searchtype, $empid, $branchid, $category) {
        $cur_datetime = $this->date_time();
        $items = [
            
            'PICKUP_POINT' => $pickuplat.','.$pickuplng,
            'DROP_POINT' => $droplat.','. $droplng,
            'TRAVEL_TYPE' => $traveltype,
            'PICKUP_TIME'=>$pickuptime,
            'DROP_TIME'=>$droptime,
            'RESCHEDULE_TIME'=>$rescheduletime,
            'SEARCH_TYPE' => $searchtype,
            'EMP_ID'=> $empid,
            'BRANCH_ID' => $branchid,
            'CATEGORY'=> $category,
            'PROCESS_DATE' => $cur_datetime                
        ];
        return $items;
    }

    //path insert
    public function pathInsert($routeid,$branchid,$duration,array $pathArr,$type,$travel_type,$route_name) {
        $dao = new Shuttledao();   
        $this->load->model('Shuttlemdl');
        $getdate=$dao->get_datetime();
        $cur_time=$getdate['cur_time'];
        $curdate=$getdate['cur_date'];
        $addresscount=0;
        
        $pathcnt=count($pathArr);
        $addresscount=round($pathcnt/10);
        $sec=round($duration/$pathcnt);
        $i=0;
        foreach ($pathArr as $arr) {
            $i++;
            $route_id = $routeid;
            $branch_id = $branchid;            
            $position = $arr['lat'].','.$arr['lng'];
            
            if($i==$addresscount || $i==1)
            {
                $addresscount=$addresscount+10;
                $location =$this->Shuttlemdl->getAddress($arr['lat'],$arr['lng']);
            }
            else
            {
               $location=$location;
            }      
            //echo "loc==".$location;
            $arrivel_time = $sec*$i ;
            $ind_duration=$this->sec_to_time($arrivel_time);
            $secs = strtotime($cur_time)-strtotime("00:00:00");
            $result = date("H:i:s",strtotime($ind_duration)+$secs);
            
            $route_date = $curdate.'T'.$result;
            $params['body'][] = $this->eindex(ES_INDEX, ES_TYPE_ROUTE_PATH);
            $params['body'][] = $this->routePath($route_id, $branch_id, $position,$location,$arrivel_time,$route_date,$type,$travel_type,$route_name);
        }
        $response = $this->client->bulk($params);
        $rcnt = count($response['items']);
        if ($rcnt == 0) {
            return $element_arr = array("Path" => "False");
        } else {
            return $element_arr = array("Path" => "True");
        }
    }
    
    
     public function routePath($route_id, $branch_id, $position,$location,$duration,$route_date,$type,$travel_type,$route_name) {
        $cur_datetime = $this->date_time();
        $itemsPath = [
            'ROUTE_ID' => $route_id,
            'BRANCH_ID' => $branch_id,
            'POSITION' => $position,
            'LOCATION'=>$location,
            'ROUTE_TYPE'=>$type,
            'STOP_STATUS'=>ES_DEFAULT,
            'TRAVEL_TIME'=>ES_DEFAULT,
            'APROX_DURATION' => $duration,
            'ROUTE_DATE'=> $route_date,
            'PROCESS_DATE' => $cur_datetime,
            'ACTIVE_STATUS'=>'1',
            "STOP_ID"=> "0",
            "TYPE"=> $travel_type

        ];
        return $itemsPath;
    }
    
    
    public function pathInsert_Driver($routeid,$branchid,$duration,array $pathArr,$type,$travel_type) {
        $dao = new Shuttledao();   
        $this->load->model('Shuttlemdl');
        $getdate=$dao->get_datetime();
        $cur_time=$getdate['cur_time'];
        $curdate=$getdate['cur_date'];
        $addresscount=0;
        
        $pathcnt=count($pathArr);
        $addresscount=round($pathcnt/10);
        $sec=round($duration/$pathcnt);
        $i=0;
        foreach ($pathArr as $arr) {
            $i++;
            $route_id = $routeid;
            $branch_id = $branchid;            
            $position = $arr['lat'].','.$arr['lng'];
            $location =$this->Shuttlemdl->getAddress($arr['lat'],$arr['lng']);
            $distance=$arr['distance'];
            
//            if($i==$addresscount || $i==1)
//            {
//                $addresscount=$addresscount+10;
//                $location =$this->Shuttlemdl->getAddress($arr['lat'],$arr['lng']);
//            }
//            else
//            {
//               $location=$location;
//            }      
            //echo "loc==".$location;
            $arrivel_time = $sec*$i ;
            $ind_duration=$this->sec_to_time($arrivel_time);
            $secs = strtotime($cur_time)-strtotime("00:00:00");
            $result = date("H:i:s",strtotime($ind_duration)+$secs);
            
            $route_date = $curdate.'T'.$result;
            $params['body'][] = $this->eindex('topsa_master', 'topsa_routes');
            $params['body'][] = $this->routePath_Driver($route_id, $branch_id, $position,$location,$arrivel_time,$route_date,$type,$travel_type,$distance);
        }
        $response = $this->client->bulk($params);
        $rcnt = count($response['items']);
        if ($rcnt == 0) {
            return $element_arr = array("Path" => "False");
        } else {
            return $element_arr = array("Path" => "True");
        }
    }
    
    
     public function routePath_Driver($route_id, $branch_id, $position,$location,$duration,$route_date,$type,$travel_type,$distance) {
        $cur_datetime = $this->date_time();
        $itemsPath = [
            'ROUTE_ID' => $route_id,
            'BRANCH_ID' => $branch_id,
            'POSITION' => $position,
            'LOCATION'=>$location,
            'ROUTE_TYPE'=>$type,
            'STOP_STATUS'=>ES_DEFAULT,
            'TRAVEL_TIME'=>ES_DEFAULT,
            'APROX_DURATION' => $duration,
            'ROUTE_DATE'=> $route_date,
            'PROCESS_DATE' => $cur_datetime,
            'ACTIVE_STATUS'=>'1',
            "STOP_ID"=> "0",
            "TYPE"=> $travel_type,
            "STAGE"=>0,
            "DISTANCE"=>$distance

        ];
        return $itemsPath;
    }
    
    
    function sec_to_time($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor($seconds % 3600 / 60);
    $seconds = $seconds % 60;
    return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
  } 


//*****shuttle new bookin search*******
  
  
   public function get_searchgroup($clat, $clng,$branchid ,$type,$routetype,$resheduledate,$reshedulderoute) {
        $params = $this->search_routepath($clat, $clng, $branchid,$routetype,$resheduledate,$reshedulderoute);

       //PRINT_R($params);
       // echo json_encode($params);
        $items = $this->client->search($params); 
        // echo json_encode( $items);
        $result = array();
        for ($i = 0; $i < count($items['aggregations']['top-uids']['buckets']); $i++) {          
                $searchResponse[$i] = $items['aggregations']['top-uids']['buckets'][$i]['top_uids_hits']['hits']['hits'][0]['_source'];
                if ($type == 'D') {
                    $dtime=array("APROX_DROP" => $searchResponse[$i]['APROX_DURATION']);
                    $drop = array("DROP_POSITION" => $searchResponse[$i]['POSITION']);
                    $dloc = array("DROP_LOCATION" => $searchResponse[$i]['LOCATION']);
                    $ddate = array("DROP_DATE" => $searchResponse[$i]['ROUTE_DATE']);
                    $dstopid = array("DROP_STOP_ID" => $searchResponse[$i]['STOP_ID']);
                    $result[] = array_merge($searchResponse[$i], $drop,$dloc,$dtime,$ddate,$dstopid);
                } else {
                    $ptime=array("APROX_PICK" => $searchResponse[$i]['APROX_DURATION']);
                    $pick = array("PICK_POSITION" => $searchResponse[$i]['POSITION']);
                    $ploc = array("PICK_LOCATION" => $searchResponse[$i]['LOCATION']);
                    $pdate = array("PICK_DATE" => $searchResponse[$i]['ROUTE_DATE']);
                    $pstopid = array("PICK_STOP_ID" => $searchResponse[$i]['STOP_ID']);
                    $result[] = array_merge($searchResponse[$i],$pick,$ploc,$ptime,$pdate,$pstopid);
                }
           // }
        }
        return $result;
    }

    public function search_routepath($clat,$clng,$branchid,$routetype,$resheduledate,$reshedulderoute) {    
        $dist= ES_SEARCH_KM;
        $must[] = $this->matchPhrase(ES_FIELD_BRANCH_ID, $branchid);
        $must[] = $this->matchPhrase('STOP_STATUS', '1');
		$must[] = $this->matchPhrase('ACTIVE_STATUS', '1');
        if($routetype==ES_SEARCH_FIXED){
            $must[] = $this->matchPhrase(ES_FIELD_ROUTE_TYPE, $routetype);
            $dist=ES_FIX_SEARCH_KM; 
        }    
        if($resheduledate!='0000-00-00'){
            $must[] = $this->matchPhrase('ROUTE_ID',$reshedulderoute);
        };
       // print_r($must);
        $sort[]=$this->geoDistance(ES_FIELD_POSITION, $clat, $clng);       
        $itemsSearch = [
                    'size' => ES_SIZE_ZERO,
                    'index' =>ES_INDEX,
                    'type' => ES_TYPE_PATH,
                    'body' => [
                    'query' => [
                        'bool' => ['must' => $must,
                            'filter' => [
                                'geo_distance' => [
                                    'distance' => $dist,
                                    ES_FIELD_POSITION => [
                                        'lat' => $clat,
                                        'lon' => $clng
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'aggs' => [
                        'top-uids' => [
                            'terms' => [
                                'field' => ES_FIELD_ROUTE_ID,
                                'size' => ES_SIZE_MAX
                            ],
                            'aggs' => [
                                'top_uids_hits' => [
                                    'top_hits' => [
                                        'sort' => $sort,
                                        'size' => ES_SIZE_SINGLE
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
        ];
        return $itemsSearch;
    }
    
      //Bookinglog insert
        public function shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id,$booking_response) {
        $params['body'][] = $this->eindex(ES_INDEX, ES_TYPE_SHUTTLE_BOOKING);
        $params['body'][] = $this->bookingLog($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id,$booking_response);
        $response = $this->client->bulk($params);
    }
    
    public function bookingLog($eid, $ptpoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime,
            $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts,
            $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id,$booking_response) {
        $cur_datetime = $this->date_time();
        $items = [
            'EMP_ID'=>$eid,
            'BRANCH_ID'=>$branch_id,
            'PICKUP_LOC'=>$ptpoint,
            'PICKUP_POINT'=>$plat.','.$plong,
            'RETURN_LOC'=> $dpoint,
            'RETURN_POINT'=>$dlat.','. $dlong,
            'TRAVEL_TYPE'=>$ttype,
            'PICKUP_TIME'=>$ptime,
            'RETURN_TIME'=>$dtime,
            'PICK_TRIP_ID'=>$tripid_p,
            'RETURN_TRIP_ID'=>$tripid_d,
            'NO_OF_DAYS'=>$noofdays,
            'PICKUP_TAG'=>$ptag,
            'RETURN_TAG'=>$dtag,
            'ROUTE_TYPE'=>$route_type,
            'CONFIRM_STATUS'=>$subs_confirm_sts,
            'PICKUP_STOP_ID'=>$stopid_p,
            'RETURN_STOP_ID'=>$stopid_d,
            'SSO_TOKEN'=>$ssotoken,
            'CUSTOMER_ID'=>$cust_id,
            'MOBILE_NO'=>$mobileno,
            'RESPONSE'=>$booking_response,          
            'PROCESS_DATE' => $cur_datetime                
        ];
        return $items;
    }

       //Request log insert
        public function shuttleApiRequestInsert($eno,$caseid, $request) {
        $params['body'][] = $this->eindex(ES_INDEX, ES_TYPE_SHUTTLE_API_REQUEST);
        $params['body'][] = $this->apiRequestLog($eno,$caseid, $request);       
        $response = $this->client->bulk($params);
    }
    
    public function apiRequestLog($eno,$caseid,$request) {
        $cur_datetime = $this->date_time();
        $items = [
            'NO'=>$eno,            
            'API_REQUEST'=>$request, 
            'API_CASE_ID'=>$caseid,                    
            'REQUEST_DATE' => $cur_datetime                
        ];
        return $items;
    }
	//Request log insert
        public function shuttleNoshowStatusLog($log_res) {
		$cur_datetime = $this->date_time();
		$roster_id= $log_res[0]['ROSTER_ID'];
		$PREVIOUS_STATUS=$log_res[0]['PREVIOUS_STATUS'];
		$UPDATED_STATUS=$log_res[0]['UPDATED_STATUS'];
		$EMPLOYEE_ID=$log_res[0]['EMPLOYEE_ID'];
		$ROSTER_PASSENGER_ID=$log_res[0]['ROSTER_PASSENGER_ID'];
		
		$itemsAction = [
            'ROSTER_ID' => $roster_id,
            'PREVIOUS_STATUS' => $PREVIOUS_STATUS,
            'UPDATED_STATUS' => $UPDATED_STATUS,
            'PROCESS_DATE' => $cur_datetime,
			'EMPLOYEE_ID' => $EMPLOYEE_ID,
            'ROSTER_PASSENGER_ID' => $ROSTER_PASSENGER_ID
        ];
		$params['body'][] = $this->eindex(ES_INDEX, ES_TYPE_SHUTTLE_STATUS_LOG);		
        $params['body'][] = $itemsAction;       
        $response = $this->client->bulk($params);
    }
}
