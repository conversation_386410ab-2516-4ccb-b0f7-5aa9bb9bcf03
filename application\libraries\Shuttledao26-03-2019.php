<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttledao
 *
 * <AUTHOR>
 */
class Shuttledao {

    //put your code here


    public function booking_list_dao($payment_no, $opr, $seat_sts, $table_name) {
        $row = array();
        $CI = & get_instance();
        $getdate = $this->get_datetime();
        $curdate = $getdate['cur_date'];
        $where1 = "sts.Payment_Token='$payment_no' and $seat_sts and st.Schedule_Date $opr '$curdate'";
        $CI->db->select("st.Trip_ID,st.status,addtime(st.Schedule_Time, tstop.Stop_Time) as Stop_Time,st.Schedule_Date,sts.Trip_Seat_ID,"
                . "SUBSTRING(sroute.Route_Name, 1, 1) as Route_Name,sts.Seat_status,st.Schedule_Route_ID,st.Trip_ID,sts.Travel_Type");
        //$CI->db->from("shuttle_trip_seat as sts");
        $CI->db->from("$table_name as sts");
        // $CI->db->join("shuttle_trip_seat as sts","sts.Payment_Token=sb.PaymentNo");
        $CI->db->join("shuttle_trip as st", "st.Trip_ID=sts.Trip_ID");
        $CI->db->join("shuttle_route_stops as tstop", "tstop.Route_Stops_ID=sts.Seat_Begin_Stop");
        $CI->db->join("shuttle_scheduled_route as sroute", "sroute.Scheduled_Route_ID=st.Schedule_Route_ID");
        $CI->db->where($where1);
        $CI->db->order_by("st.Schedule_Date,st.Schedule_Time");
        // $CI->db->limit(50);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function getroster_details($empid, $routeid, $tripdate,$bid) {
        $row = array();
        $CI = & get_instance();
        $where = "rp.EMPLOYEE_ID='$empid' and date(rp.ESTIMATE_START_TIME)='$tripdate' and ROUTE_ID='$routeid' and r.BRANCH_ID='$bid'";
        $CI->db->select("r.CAB_ID,r.ROSTER_ID,ROSTER_STATUS as tracksts,rp.ROSTER_PASSENGER_STATUS,r.ACTUAL_START_TIME");
        $CI->db->from('rosters as r');
        $CI->db->join('roster_passengers as rp', 'rp.ROSTER_ID=r.ROSTER_ID');
        $CI->db->where($where);
        $query = $CI->db->get();
       // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function get_cabid($eid) {
        $row = array();
        $getdate = $this->get_datetime();
        $curdate = $getdate['cur_date'];
       // $cabtracking = implode(',', unserialize(R_CAB_TRACKING));
        $CI = & get_instance();
        $where = "rp.EMPLOYEE_ID='$eid' and date(rp.ESTIMATE_START_TIME)='$curdate' ";
        $CI->db->select("r.CAB_ID,r.ROSTER_ID,ROSTER_STATUS as tracksts");
        $CI->db->from('rosters as r');
        $CI->db->join('roster_passengers as rp', 'rp.ROSTER_ID=r.ROSTER_ID');
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function track1($rosterid, $cabid, $eid) {
        $row = array();
        $getdate = $this->get_datetime();
        $curdate = $getdate['cur_date'];
        $cabtracking = implode(',', unserialize(R_CAB_TRACKING));
        $noshow = implode(',', unserialize(RP_PICKUP_DROP_NOSHOW));
        $CI = & get_instance();
        $where = "rp.EMPLOYEE_ID='$eid' and r.BRANCH_ID=e.BRANCH_ID and date(rp.ESTIMATE_START_TIME)='$curdate' and r.ROSTER_STATUS in(" . $cabtracking . ") and r.ROSTER_ID='$rosterid' and r.CAB_ID='$cabid' and rp.ROSTER_PASSENGER_STATUS not in(" . $noshow . ") and (o.OTP_CATEGORY='Pickup' || o.OTP_CATEGORY='Drop')";
        $CI->db->select("rp.ROUTE_ORDER,e.NAME,e.ADDRESS,l.LOCATION_NAME,l.LATITUDE,l.LONGITUDE,rp.DRIVER_ARRIVAL_TIME as cab_arrived_time,rp.ROSTER_PASSENGER_STATUS,o.OTP,if(r.TRIP_TYPE='P',time(r.ESTIMATE_END_TIME),"
                . "time(r.ESTIMATE_START_TIME))as droptime,time(rp.ESTIMATE_START_TIME) as picktime,r.TRIP_TYPE,b.LAT as blat,b.LONG as blong,b.LOCATION as bloc");
        $CI->db->from('rosters as r');
        $CI->db->join('roster_passengers as rp', 'rp.ROSTER_ID=r.ROSTER_ID');
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=rp.EMPLOYEE_ID');
        $CI->db->join('locations as l', 'l.LOCATION_ID=rp.LOCATION_ID');
        $CI->db->join('branch as b', 'b.BRANCH_ID=r.BRANCH_ID');
        $CI->db->join('otp_verification as o', 'rp.ROSTER_PASSENGER_ID=o.ROSTER_PASSENGER_ID');
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function track2($rosterid, $cabid, $route_order, $eid) {
        $row = array();
        $getdate = $this->get_datetime();
        $curdate = $getdate['cur_date'];
        $cabtracking = implode(',', unserialize(R_CAB_TRACKING));
        $noshow = implode(',', unserialize(RP_PICKUP_DROP_NOSHOW));
        $CI = & get_instance();
        $where = "r.ROSTER_STATUS in(" . $cabtracking . ") and r.BRANCH_ID=e.BRANCH_ID and rp.ROSTER_ID='$rosterid' and r.CAB_ID='$cabid' and rp.ROSTER_PASSENGER_STATUS not in(" . $noshow . ")and rp.EMPLOYEE_ID='$eid' and rp.ROUTE_ORDER >= $route_order";
        $CI->db->select("rp.ROUTE_ORDER,rp.ROSTER_PASSENGER_STATUS,rp.EMPLOYEE_ID,e.NAME,l.LOCATION_NAME,l.LATITUDE,l.LONGITUDE,if(r.TRIP_TYPE='P',time(r.ESTIMATE_END_TIME),time(r.ESTIMATE_START_TIME))as droptime,time(rp.ESTIMATE_START_TIME) as picktime");
        $CI->db->from('rosters as r');
        $CI->db->join('roster_passengers as rp', 'rp.ROSTER_ID=r.ROSTER_ID');
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=rp.EMPLOYEE_ID');
        $CI->db->join('locations as l', 'l.LOCATION_ID=rp.LOCATION_ID');
        $CI->db->order_by("rp.ROUTE_ORDER desc");
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function get_driver_details($cabid) {
        $row = array();
        $CI = & get_instance();
        $where = "c.CAB_ID='$cabid' and c.ACTIVE=1 and d.ACTIVE=1 and v.ACTIVE=1";
        $CI->db->select("d.DRIVERS_NAME,d.DRIVER_MOBILE,v.VEHICLE_REG_NO,vm.MODEL,d.DRIVER_IMAGE");
        $CI->db->from("drivers as d");
        $CI->db->join('cab as c', 'c.DRIVER_ID=d.DRIVERS_ID');
        $CI->db->join('vehicles as v', 'v.VEHICLE_ID=c.VEHICLE_ID');
        $CI->db->join('vehicle_models as vm', 'vm.VEHICLE_MODEL_ID=v.VEHICLE_MODEL_ID');
        $CI->db->where($where);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function get_employee_details($eid,$branch_id) {
        $row = array();
        $CI = & get_instance();
        $where = "e.EMPLOYEES_ID='$eid' and e.ACTIVE=1 and p.PROPERTIE_NAME='SMS TAG' and p.ACTIVE=1 and e.BRANCH_ID='$branch_id'";
        $CI->db->select("e.NAME,e.MOBILE,e.BRANCH_ID,p.PROPERTIE_VALUE,e.MOBILE_GCM,e.MOBILE_CATEGORY");
        $CI->db->from("employees as e");
        $CI->db->join("properties as p", "p.BRANCH_ID=e.BRANCH_ID");
        $CI->db->where($where);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function get_roster_details($roster_id, $trip_date) {
        $row = array();
        $CI = & get_instance();
        $where = "ROSTER_ID='$roster_id' and ACTIVE=1";
        $CI->db->select("ESTIMATE_START_TIME,ROUTE_ID");
        $CI->db->from("rosters");
        $CI->db->where($where);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
            $where1 = "time(ESTIMATE_START_TIME)=time('" . $row['ESTIMATE_START_TIME'] . "') and ROUTE_ID='" . $row['ROUTE_ID'] . "' and ACTIVE=1 and date(ESTIMATE_START_TIME)='$trip_date'";
            $CI->db->select("ROSTER_ID");
            $CI->db->from("rosters");
            $CI->db->where($where1);
            $query1 = $CI->db->get();
            if ($query1->num_rows() > 0) {
                $row1 = $query1->row_array();
            }
        }
        return $row1;
    }

    public function shuttle_booking_update($trans_id, $category, $roster_id, $eid, $sts) {
        $ret = 0;
        $getdate = $this->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $cnt = 1;
        $CI = & get_instance();
        //$stsarray=array_merge(unserialize(RP_ARRIVED_BOARDED),unserialize(RP_PICKUP_DROP_OTP));
        $created_arr = implode(',', unserialize(RP_CREATED));

        // echo "cat==".$category."roster id==".$roster_id;
        // if ($category == 1) {
        if ($roster_id != 0) {
            $cnt = 0;
            /* log */
            $previous_log_passenger = $this->previous_log_arr_rosterpassenger($roster_id, $eid);
            if (count($previous_log_passenger) > 0) {
                $previous_log_passenger[0]['PREVIOUS_STATUS'];
                /* log */
                if (in_array($previous_log_passenger[0]['PREVIOUS_STATUS'], unserialize(RP_CREATED))) {

                    $rp_id = $previous_log_passenger[0]['ROSTER_PASSENGER_ID'];
                    $qry = "UPDATE rosters as r join roster_passengers as rp SET rp.ROSTER_PASSENGER_STATUS = if(conv(bin(ROSTER_PASSENGER_STATUS)+bin(" . RP_NOSHOW . "),2,10)=0,ROSTER_PASSENGER_STATUS,conv(bin(ROSTER_PASSENGER_STATUS)+bin(" . RP_NOSHOW . "),2,10)),"
                            . " r.PASSENGER_NOSHOW_COUNT = r.PASSENGER_NOSHOW_COUNT+1,rp.updated_at='$curdate_time',rp.UPDATED_BY='$eid',"
                            . "r.PASSENGER_ALLOT_COUNT = r.PASSENGER_ALLOT_COUNT-1,"
                            . "rp.ACTUAL_START_TIME=if(r.TRIP_TYPE='P','$curdate_time',rp.ACTUAL_START_TIME),
                                rp.ACTUAL_END_TIME=if(r.TRIP_TYPE='D','$curdate_time',rp.ACTUAL_END_TIME)"
                            . " WHERE rp.EMPLOYEE_ID = '$eid' AND r.ROSTER_ID='$roster_id' AND rp.ROSTER_ID='$roster_id' AND rp.ROSTER_PASSENGER_ID='$rp_id' AND rp.ROSTER_PASSENGER_STATUS in ($created_arr) ";

                    $cnt = $CI->db->query($qry);
                }
                /* log */
                $updated_log_passenger = $this->updated_log_arr_rosterpassenger($roster_id, $eid);
                $log_res[] = $updated_log_passenger[0] + $previous_log_passenger[0];
                $CI->load->model('ShuttleElasticmdl');
                $CI->ShuttleElasticmdl->shuttleNoshowStatusLog($log_res);
            }
            /* log End */
        }

        if ($cnt > 0) {
            $ret = $this->shuttletrip_update($trans_id, $sts, $category);
        } else {
            $ret = $this->shuttletrip_update($trans_id, $sts, $category);
        }
        //} 
        return $ret;
    }

    public function shuttletrip_update($trans_id, $sts, $category) {

        $ret = 0;
        $CI = & get_instance(); //and Seat_status=1
        $where = "Trip_Seat_ID='$trans_id' and Seat_status=1";
        $CI->db->select('Trip_ID,Seat_Number,Payment_Token');
        $CI->db->from('shuttle_trip_seat');
        $CI->db->where($where);
        $CI->db->limit(1);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row1 = $query->row_array();
            $trip_id = $row1['Trip_ID'];
            $payment_no = $row1['Payment_Token'];
            if ($category != 1) {
                $where2 = "PaymentNo='$payment_no' and PaymentStatus='S'";
                $CI->db->set("NoofCancelRides", "NoofCancelRides+1", FALSE);
                $CI->db->where($where2);
                $CI->db->update('shuttle_booking');
                // echo $CI->db->last_query();
            }

            $seat_number = $row1['Seat_Number'];
            $CI->db->set("Seat_status", $sts);
            $CI->db->where($where);
            $CI->db->update('shuttle_trip_seat');

            $where1 = "Trip_ID='$trip_id' and (status='1' or status=2)";
            $CI->db->set("Seats_Used", "Seats_Used-1", FALSE);
            $CI->db->where($where1);
            $CI->db->update('shuttle_trip');

            $data = array('Trip_ID' => $trip_id, 'Seat_Number' => $seat_number);
            $CI->db->set($data);
            $CI->db->insert('shuttle_trip_seat', $CI);
            $ret = 1;
        }
        return $ret;
    }

    public function payment_history($eid, $bid) {
        $row = array();
        $CI = & get_instance();
        $where = "s.EmployeeId='$eid' and s.BranchId='$bid' and e.CATEGORY='Shuttle' and e.BRANCH_ID='$bid' ";//and s.Active!=3
        $CI->db->select("s.PaymentNo,s.PickupPoint,s.DropPoint,s.TravelMode,s.StartDate,s.StartTime,s.EndTime,s.TotalPaidAmt,s.NoofDays,r.RefundAmount,s.RefundReferanceId,r.ResponseCode,r.ResponseMessage,r.Status,r.CreatedTime,r.RefundId");
        $CI->db->from('shuttle_booking as s');
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=s.EmployeeId');
        $CI->db->join('shuttle_payment_response as r', 's.PaymentNo=r.OrderId');
        $CI->db->order_by("r.CreatedTime desc");
        $CI->db->where($where);
        //$CI->db->group_by('r.OrderId');
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function cancellation_policy($transid, $eid, $duration) {
        $row = array();
        $CI = & get_instance();
        $where = "st.Trip_Seat_ID='$transid' and sm.EmployeeId='$eid' and sm.PaymentStatus='S'";
        $CI->db->select("sm.TotalPaidAmt as paidamt,sm.NoofRides,sm.NoofRidesUsed,sm.EndDate,sm.NoShowRides,sm.TravelMode,"
                . "sm.NoofCancelRides,sm.StartDate,sm.EndDate");
        $CI->db->from("shuttle_booking as sm");
        $CI->db->join('shuttle_trip_seat as st', 'st.Payment_Token=sm.PaymentNo');
        $CI->db->where($where);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function update_passenger_count($rosterid, $sts) {
        $ret = false;
        $CI = & get_instance();
        $where = "ROSTER_ID = '$rosterid' and ACTIVE=1";
        if ($sts == "add") {
            $CI->db->set("PASSENGER_ALLOT_COUNT", "PASSENGER_ALLOT_COUNT+1", FALSE);
        } else {
            $CI->db->set("PASSENGER_ALLOT_COUNT", "PASSENGER_ALLOT_COUNT-1", FALSE);
        }
        $CI->db->where($where);
        $CI->db->update('rosters');
        // echo $CI->db->last_query();
        if ($CI->db->affected_rows() > 0) {
            $ret = true;
        }
        return $ret;
    }

//    public function update_shuttle_trans($trans_id, $stime) {
//        $ttime = date('H:i:s', strtotime($stime));
//        $ret = false;
//        $getdate = $this->get_datetime();
//        $cur_datetime = $getdate['cur_date_time'];
//        $CI = & get_instance();
//        $where = "BookingTransId='$trans_id' and Active=1";
//        $CI->db->set("StartTime", "if(StartTime='00:00:00',StartTime,'$ttime')", FALSE);
//        $CI->db->set("EndTime", "if(EndTime='00:00:00',EndTime,'$ttime')", FALSE);
//        $CI->db->set("UpdatedDatetime", $cur_datetime);
//        $CI->db->where($where);
//        $CI->db->update('shuttle_booking_trans');
//        // echo $CI->db->last_query();
//        if ($CI->db->affected_rows() > 0) {
//            $ret = true;
//        }
//        return $ret;
//    }

    public function get_current_tracking_details($eid) {
        $row = array();
        $getdate = $this->get_datetime();
        $cur_datetime = $getdate['cur_date_time'];
        $CI = & get_instance();
        $noshow = implode(',', unserialize(RP_PICKUP_DROP_NOSHOW));
        $cabtracking = implode(',', unserialize(R_CAB_TRACKING));

        $where = "rp.EMPLOYEE_ID = '$eid' and r.BRANCH_ID=e.BRANCH_ID and rp.ACTIVE=1 and ROSTER_PASSENGER_STATUS not in(" . $noshow . ") and r.ROSTER_STATUS in(" . $cabtracking . ") and r.ACTIVE=1 and c.ACTIVE=1 and '" . $cur_datetime . "' <= addtime(r.ACTUAL_START_TIME,'06:00:00')";
        $CI->db->select("rp.ROSTER_ID,rp.ROSTER_PASSENGER_ID,r.CAB_ID,l.LOCATION_NAME,r.START_LOCATION,r.END_LOCATION,r.TRIP_TYPE,c.VEHICLE_ID,v.VEHICLE_REG_NO,rp.ESTIMATE_START_TIME,e.PLACE_PICKER_STATUS");
        $CI->db->from("roster_passengers as rp");
        $CI->db->join('rosters as r', "r.ROSTER_ID=rp.ROSTER_ID");
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=rp.EMPLOYEE_ID');
        $CI->db->join('locations as l', 'l.LOCATION_ID=rp.LOCATION_ID');
        $CI->db->join('cab as c', 'c.CAB_ID=r.CAB_ID');
        $CI->db->join('vehicles as v', 'v.VEHICLE_ID=c.VEHICLE_ID');
        $CI->db->order_by("r.ESTIMATE_START_TIME desc");
        $CI->db->limit(1);
        $CI->db->where($where);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function get_current_feedback_details($eid,$ct) {
        $row = array();
        $CI = & get_instance();
        $noshow = implode(',', unserialize(RP_PICKUP_DROP_NOSHOW));
        $tripclose = implode(',', unserialize(R_TRIP_CLOSE_HISTORY));
        $getdate = $this->get_datetime();
        $cur_date= $getdate['cur_date'];
        if($ct=='ip')
        {
           $where= "rp.EMPLOYEE_ID = '$eid' and r.BRANCH_ID=e.BRANCH_ID and rp.ACTIVE=1 and ROSTER_PASSENGER_STATUS not in(" . $noshow . ") and r.ROSTER_STATUS in(" . $tripclose . ") and r.ACTIVE=1"
                . "  and date(rp.ESTIMATE_START_TIME) BETWEEN DATE_SUB(CURDATE(),INTERVAL 5 DAY) and CURDATE() and rp.ROSTER_ID not in (select ROSTER_ID from feedback_log where EMPLOYEE_ID='$eid')";
        }
        else{
        $where = "rp.EMPLOYEE_ID = '$eid' and r.BRANCH_ID=e.BRANCH_ID and rp.ACTIVE=1 and ROSTER_PASSENGER_STATUS not in(" . $noshow . ") and r.ROSTER_STATUS in(" . $tripclose . ") and r.ACTIVE=1"
                . " and date(rp.ESTIMATE_START_TIME) BETWEEN DATE_SUB(CURDATE(),INTERVAL 5 DAY) and CURDATE() and rp.ROSTER_ID not in (select ROSTER_ID from feedback_log where EMPLOYEE_ID='$eid')";
        }
        $CI->db->select("rp.ROSTER_ID,r.CAB_ID,l.LOCATION_NAME,r.START_LOCATION,r.END_LOCATION,r.TRIP_TYPE,rp.ESTIMATE_START_TIME");        
        $CI->db->from("roster_passengers as rp");
        $CI->db->join('rosters as r', "r.ROSTER_ID=rp.ROSTER_ID");
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=rp.EMPLOYEE_ID');
        $CI->db->join('locations as l', 'l.LOCATION_ID=rp.LOCATION_ID');
        $CI->db->order_by("r.ESTIMATE_START_TIME desc");
        $CI->db->limit(1);
        $CI->db->where($where);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }
    
    
    function check_noservice_date($sdate)
    {
        $row = 1;
        $CI = & get_instance();
        $where = "Active=1 and HolidayDate='$sdate'";
        $CI->db->select("HolidayDate");
        $CI->db->from("shuttle_noservice");       
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row=0;
        }
        return $row;        
       
    }

    //new table query//

    public function get_seatavailability($route_id, $resheduledate,$branchid) {
        $row = array();
        $getdate = $this->get_datetime();
        $cur_date = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $edate = "";
        $sts = "";
        if ($resheduledate == '0000-00-00') {
            $sts = "sr.Status=1";
            if (date("w", strtotime($cur_date)) == 5 || date("w", strtotime($cur_date)) == 6) {                
                $edate = date('Y-m-d', strtotime('next monday', strtotime($cur_date)));                             
            } else { 
                $edate =  date('Y-m-d', strtotime('+1 day', strtotime($cur_date)));                     
            }
        } else {
            $sts = "(sr.Status=1 or sr.Status=2)";
            $cur_date = $resheduledate;
            $edate = $resheduledate;
        }
		
         
       // if($branchid!=1 && (intval(date('m',strtotime($cur_date))))==3 && (intval(date('Y',strtotime($cur_date))))==2018)
        if($branchid!=1 && (intval(date('d',strtotime($cur_date))))>=25 )
        {
            $cur_date=date('Y-m-d',strtotime('first day of +1 month', strtotime($cur_date)));            
            if (date("w", strtotime($cur_date)) == 5)
            {
                $edate=$cur_date;
            }            
            else if (date("w", strtotime($cur_date)) == 6) {                
                $edate = date('Y-m-d', strtotime('next monday', strtotime($cur_date)));                             
            } else { 
                $edate =  date('Y-m-d', strtotime('+1 day', strtotime($cur_date)));                     
            }
          
        }       
        $i=$this->check_noservice_date($cur_date);  
        if($i==0){
        $cur_date =  date('Y-m-d', strtotime("+1 day", strtotime($cur_date))); 
        }
       
        $j=$this->check_noservice_date($edate);  
        if($j==0){
        $edate =  date('Y-m-d', strtotime("+1 day", strtotime($edate))); 
        } 
       
        $CI = & get_instance();
        $where = "s.Schedule_Route_ID='$route_id' AND (s.status=1 OR s.status=2) AND s.Schedule_Date BETWEEN '$cur_date' AND '$edate' "
                . "AND s.Seats_Used < s.Total_Capacity AND $sts";
        $CI->db->select("s.Trip_ID,s.Vehicle_ID,s.Schedule_Date,s.Schedule_Time,s.Seats_Used,sr.Route_Name,sr.Return_Route_No,sr.Route_ID,sr.Travel_Type,sr.Tariff_Type");
        $CI->db->from("shuttle_trip as s");
        $CI->db->join("shuttle_scheduled_route as sr", 'sr.Scheduled_Route_ID=s.Schedule_Route_ID');
        $CI->db->order_by("s.Schedule_Time");
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function get_seatcount($tripid_p) {
        $ret = false;
        $CI = & get_instance();
        $where = "Trip_ID='$tripid_p' and (Total_Capacity-Seats_Used)>0";
        $CI->db->select("Trip_ID");
        $CI->db->from("shuttle_trip");
        $CI->db->where($where);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $ret = true;
        }
        return $ret;
    }

    public function get_routeid_sheduletime($trip_id) {
        $row = array();
        $CI = & get_instance();
        $where = "s.Trip_ID='$trip_id'";
        $CI->db->select("s.Schedule_Route_ID,s.Schedule_Time,s.Vehicle_ID,sr.Route_Name");
        $CI->db->from("shuttle_trip as s");
        $CI->db->join("shuttle_scheduled_route as sr", "sr.Scheduled_Route_ID=s.Schedule_Route_ID");
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function seatcount_update($route_id, $schedule_time, $schedule_date, $seat_cost, $stop_id, $payment_token, $emp_id, $dist, $vehicle_id, $trip_type, $dpoint, $route_name,$branch_id) {
        
        $ret = 0;
        $t_type = "";
        $CI = & get_instance();
        $trip_id = 0;
        $trip_sts = 0;
        $sch_route_id = 0;
        $sch_time = "";
       
        $getdate = $this->get_datetime();
        $cur_date = $getdate['cur_date'];
       // $curtime = $getdate['cur_time'];
       // $cur_date = $getdate['cur_date'];
        $cur_date_time = $getdate['cur_date_time'];

        try {
            if ($trip_type == 'D') {
                $errorlogpath = pathinfo(ini_get('error_log'));
                $errorlogfile = "/var/www/html/TOPSA/application/logs/" . date('Y-m-d') . "-booking.log";
                if (file_exists($errorlogfile)) {
                    ini_set('error_log', $errorlogfile);
                } else {
                    $errfh = fopen($errorlogfile, "a+");
                    if (is_resource($errfh)) {
                        ini_set('error_log', $errorlogfile);
                        fclose($errfh);
                    }
                }
                error_log("~~~~~~~  booking employee id  $emp_id~~~~~~", 0);
            }
            //$CI->db->trans_begin();
            $where = "Schedule_Route_ID='$route_id' and Schedule_Time='$schedule_time' and Vehicle_ID='$vehicle_id' and Schedule_Date='$schedule_date' and (status=1 or status=2) "; //LOCK IN SHARE MODE
            $CI->db->select("st.Trip_ID,st.status,st.Schedule_Time as stoptime,st.Schedule_Route_ID");
            $CI->db->from("shuttle_trip as st");
            $CI->db->where($where);           
            $query2 = $CI->db->get();
            // echo $CI->db->last_query();
            if ($query2->num_rows() > 0) {
                $row2 = $query2->row_array();
                $trip_id = $row2['Trip_ID'];
                $trip_sts = $row2['status'];
                $sch_time = $row2['stoptime'];
                $sch_route_id = $row2['Schedule_Route_ID'];

                $where_update = "Trip_ID=$trip_id and (status=1 or status=2)";
                $CI->db->set("Seats_Used", "Seats_Used+1", FALSE);
                $CI->db->where($where_update);
                $CI->db->limit(1);
                $CI->db->update('shuttle_trip');

                if ($CI->db->affected_rows() > 0) {
					//and loc.DIVISION_ID=1 and dist.BRANCH_ID=1
                    $where2 = " ss.Route_ID='$sch_route_id' and ss.Stop_ID='$stop_id' and stops.status='1' and"
                            . " loc.CREATE_BY=stops.Trip_Type and dist.BRANCH_ID=stops.Branch_ID and loc.ACTIVE='1'";
                    $CI->db->select("ss.Route_Stops_ID,addtime('$sch_time',ss.Stop_Time) as stoptime,loc.LOCATION_ID,"
                            . "dist.APPROVED_DISTANCE,if(stops.Trip_Type='P','Pickup','Drop') as ttype,stops.Stops_Name");
                    $CI->db->from('shuttle_route_stops as ss');
                    $CI->db->join('shuttle_stops as stops', 'stops.Stops_ID=ss.Stop_ID');
                    $CI->db->join('locations as loc', 'stops.Stops_Name=loc.LOCATION_NAME');
                    $CI->db->join('approve_distances as dist', 'dist.LOCATION_ID=loc.LOCATION_ID');
                    $CI->db->where($where2);                   
                    $query = $CI->db->get();   
                   //  echo $CI->db->last_query();
                    if ($trip_type == 'D' || $stop_id == 16) {
                        error_log("~~~~~~~  get stop id query  $stop_id ~~~~~~" . $CI->db->last_query(), 0);
                    }
                    if ($query->num_rows() > 0) {
                        $row = $query->row_array();
                        $trip_id = $trip_id;
                        $route_stop_id = $row['Route_Stops_ID'];
                        $stop_time = $row['stoptime'];
                        $loc_id = $row['LOCATION_ID'];
                        $approvedist = $row['APPROVED_DISTANCE'];
                        $ttype = $row['ttype'];
                        $stop_name = $row['Stops_Name'];
                       // $trip_sts = $row['status'];
                        $where1 = "Trip_ID='$trip_id' and Seat_Cost is null and Seat_Begin_Stop is null";
                        $CI->db->set("Seat_Cost", $seat_cost);
                        $CI->db->set("Seat_Begin_Stop", $route_stop_id);
                        $CI->db->set("Payment_Token", $payment_token);
                        $CI->db->set("Travel_Type", $trip_type);
                        $CI->db->set('Seat_status', 1);
                        $CI->db->where($where1);
                        $CI->db->order_by("Trip_Seat_ID");
                        $CI->db->limit(1);
                        $CI->db->update('shuttle_trip_seat');
                       // echo $CI->db->last_query();
                        if ($trip_type == 'D' || $stop_id == 16) {
                            error_log("~~~~~~~  update query $stop_id~~~~~~" . $CI->db->last_query(), 0);
                        }
                        if ($CI->db->affected_rows() > 0) {

                            if ($schedule_date == $cur_date && $trip_sts == 2) {
                                $roster_id = $this->updateroster_clubbingcount('S' . $trip_id, 'add');
                                if ($roster_id) {
                                    if ($route_name == "P") {
                                        $empdata = array('LOCATION_ID' => $loc_id);
                                        $approvedist = $approvedist;
                                        $t_type = "Pickup";
                                    } else {
                                        $t_type = "Drop";
                                        $wheredrop = "loc.CREATE_BY='D' and loc.LOCATION_NAME='$dpoint'";
                                        $CI->db->select("loc.LOCATION_ID,dist.APPROVED_DISTANCE");
                                        $CI->db->from("locations as loc");
                                        $CI->db->join('approve_distances as dist', 'dist.LOCATION_ID=loc.LOCATION_ID');
                                        $CI->db->where($wheredrop);
                                        $query1 = $CI->db->get();
                                        if ($query1->num_rows() > 0) {
                                            $row1 = $query1->row_array();
                                            $loc_id = $row1['LOCATION_ID'];
                                            $approvedist = $row1['APPROVED_DISTANCE'];
                                        }
                                        $empdata = array('LOCATION_ID' => $loc_id);
                                    }

                                    $whereemp = "EMPLOYEES_ID='$emp_id' and ACTIVE=1 and CATEGORY='Shuttle' and BRANCH_ID='$branch_id'";                                    
                                    $CI->db->where($whereemp);
                                    $CI->db->update('employees', $empdata);                                    

                                    $tripdate_time = $cur_date . " " . $stop_time;
                                    $OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
                                    $data = array('EMPLOYEE_ID' => $emp_id, 'ROSTER_ID' => $roster_id['ROSTER_ID'], 'ESTIMATE_START_TIME' => $tripdate_time, 'ACTIVE' => 1, 'ROUTE_ORDER' => $approvedist,
                                        'CREATED_BY' => $emp_id, 'CREATED_DATE' => $cur_date_time, 'ESTIMATE_END_TIME' => $roster_id['ESTIMATE_END_TIME'], 'LOCATION_ID' => $loc_id); //have to discuss otp_verification 
                                    $CI->db->set($data);                                    
                                    $rpcnt=$CI->db->insert('roster_passengers', $CI);
                                    $rpid=$CI->db->insert_id();
                                    if ($rpcnt > 0 && isset($roster_id['VEHICLE_REG_NO'])) {
                                        $row = $this->get_employee_details($emp_id,$branch_id);
                                        $CI->load->model('ShuttleCommonmdl');
                                        $empname = $CI->ShuttleCommonmdl->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                                        $notify_msg = "$empname - $stop_name  was clubbed in your trip";
                                        $drivergcm = $roster_id['MOBILE_GCM'];
                                        $emp_mobile = $CI->ShuttleCommonmdl->AES_DECRYPT($row['MOBILE'], AES_ENCRYPT_KEY);
                                        // $originator=$row['PROPERTIE_VALUE'];
                                        $data1 = array("MOBILE_NO" => $emp_mobile, 'ROSTER_PASSENGER_ID' => $rpid, 'OTP' => $OTP, 'VERIFIED_STATUS' => 0, 'OTP_CATEGORY' => $ttype, 'CREATED_DATE' => $cur_date_time);                                        
                                        $CI->db->set($data1);                                    
                                        $CI->db->insert('otp_verification', $CI);
                                        $smsmsg = "Your cab details for $t_type - " . $roster_id['VEHICLE_REG_NO'] . "( " . $roster_id['DRIVER_MOBILE'] . " ) on $tripdate_time at $stop_name. Enter OTP $OTP, use TOPSA App to track your cab";
                                        //$smsmsg="Your cab no ".$roster_id['VEHICLE_REG_NO']."( ".$roster_id['DRIVER_MOBILE']." ) will be arriving on $stop_name at $tripdate_time. Use OTP $OTP for Pickup, use TOPSA App to track your cab.";
                                        $CI->ShuttleCommonmdl->insert_sms($branch_id, 'TOPSCS', $emp_mobile, $smsmsg);

                                        //insert employee notification
                                        $empgcm = $row['MOBILE_GCM'];
                                        $appcat = $row['MOBILE_CATEGORY'];

                                        if (!is_null($empgcm)) {
                                            $data3 = array("branch_id" => $branch_id, 'empid_cabid' => $emp_id, 'fcm_id' => $empgcm, 'heading' => 'Cab Details',
                                                'message' => $smsmsg, 'category_value' => 3, 'app_category' => $appcat, 'created_at' => $cur_date_time);
                                            $CI->db->set($data3);                                    
                                            $CI->db->insert('notification', $CI);                                           
                                        }

                                        if (!is_null($drivergcm)) {
                                            $data2 = array("branch_id" => $branch_id, 'empid_cabid' => $roster_id['CAB_ID'], 'fcm_id' => $drivergcm, 'heading' => 'New Club',
                                                'message' => $notify_msg, 'category_value' => 3, 'created_at' => $cur_date_time);
                                            $CI->db->set($data2);                                    
                                            $CI->db->insert('notification', $CI);                                             
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                
            }
        } catch (Exception $e) {
            error_log('***' . 'shuttle booking Error: ' . $e->getMessage() . "***" . $e->getCode() . "***" . $e->getFile() . "***" . $e->getLine());
            return TRUE;
        }
        return $ret;
    }

    public function updateroster_clubbingcount($trip_id, $sts) {
        $roster_id = array();
        $cab_id = array();
        $CI = & get_instance();
        $where = "ROUTE_ID = '$trip_id' and ACTIVE=1";
        if ($sts == "add") {
            $CI->db->set("PASSENGER_CLUBING_COUNT", "PASSENGER_CLUBING_COUNT+1", FALSE);
        } else {
            $CI->db->set("PASSENGER_CLUBING_COUNT", "PASSENGER_CLUBING_COUNT-1", FALSE);
        }
        $CI->db->where($where);
        $CI->db->update('rosters');
        // echo $CI->db->last_query();
        if ($CI->db->affected_rows() > 0) {
            // $ret=true;   
            $where1 = "r.ROUTE_ID = '$trip_id' and r.ACTIVE=1";
            $CI->db->select("r.ROSTER_ID,r.ESTIMATE_END_TIME,r.CAB_ID");
            $CI->db->from("rosters as r");
            $CI->db->where($where1);
            $query = $CI->db->get();
            if ($query->num_rows() > 0) {
                $roster_id = $query->row_array();
                if (is_null($roster_id['CAB_ID'])) {
                    $roster_id = $roster_id;
                } else {
                    $where2 = "c.CAB_ID='" . $roster_id['CAB_ID'] . "' and c.ACTIVE=1 and v.ACTIVE=1 and d.ACTIVE=1";
                    $CI->db->select("v.VEHICLE_REG_NO,d.DRIVER_MOBILE,de.MOBILE_GCM");
                    $CI->db->from("cab as c");
                    $CI->db->join("vehicles as v", "v.VEHICLE_ID=c.VEHICLE_ID");
                    $CI->db->join("drivers as d", "d.DRIVERS_ID=c.DRIVER_ID");
                    $CI->db->join("devices as de", "c.DEVICE_ID=de.DEVICE_ID");
                    $CI->db->where($where2);
                    $query1 = $CI->db->get();
                    if ($query1->num_rows() > 0) {
                        $cab_id = $query1->row_array();
                    }
                }
            }
        }
        return array_merge($roster_id, $cab_id);
    }

    public function insert_sms($branchid, $originator, $mobileno, $message) {
        $getdate = $this->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        $data = array('BRANCH_ID' => $branchid, 'ORIGINATOR' => $originator, 'RECIPIENT' => $mobileno, 'MESSAGE' => $message, 'STATUS' => 'U',
            'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_DATE' => $cur_date_time);
        $CI = & get_instance();
        $CI->db->set($data);
        $CI->db->insert('sms', $CI);
    }

    public function subscription_details($eid, $bid) {
        $row = array();
        $CI = & get_instance();
        $where2 = "sb.EmployeeId='$eid' AND PaymentStatus='S' AND NoofDays >1 AND e.ACTIVE = '1' AND e.CATEGORY='Shuttle'"
                . " AND sb.BranchId='$bid' AND e.BRANCH_ID='$bid' and sb.Active!=3";
        $CI->db->select("sb.NoofRides,sb.NoofRidesUsed,sb.NoShowRides,sb.PaymentNo,sb.TotalPaidAmt,sb.NoofDays,sb.EndDate,"
                . "sb.PickupPoint,sb.DropPoint,sb.TravelMode,sb.NoofCancelRides,sb.StartDate");//st.Tariff_Name,
        $CI->db->from("shuttle_booking as sb");
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=sb.EmployeeId');
       // $CI->db->join('shuttle_tariff as st', 'st.TariffId=sb.TariffId');
        $CI->db->order_by("sb.CreatedDatetime desc");
        $CI->db->where($where2);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function getshuttle_bookingdetails($ENC_MOBILE, $ORDERID) {
        $row = array();
        $CI = & get_instance(); //AND PaymentStatus='S'
        $where2 = "e.MOBILE = '" . $ENC_MOBILE . "' AND PaymentNo='$ORDERID' AND PaymentStatus='S' AND e.ACTIVE = '1' AND e.CATEGORY='Shuttle' AND sb.BranchId=e.BRANCH_ID";
        $CI->db->select("sb.NoofRides,sb.NoofRidesUsed,sb.NoShowRides,sb.PaymentNo,sb.TotalPaidAmt,sb.NoofDays,sb.EndDate,"
                . "sb.PickupPoint,sb.DropPoint,sb.TravelMode,sb.StartTime,sb.EndTime,sb.TravelMode,sb.PackageKm,sb.PickupRosterId,"
                . "sb.DropRosterId,sb.EmployeeId,sb.StartDate,e.NAME,sb.BranchId as branchid,e.EMAIL");
        $CI->db->from("shuttle_booking as sb");
        $CI->db->join('employees as e', 'e.EMPLOYEES_ID=sb.EmployeeId');
        $CI->db->where($where2);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function getvehicleid($trip_id) {
        $row = array();
        $veh_id = 0;
        $CI = & get_instance();
        $where = "Trip_ID='$trip_id'";
        $CI->db->select("Vehicle_ID");
        $CI->db->from("shuttle_trip");
        $CI->db->where($where);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
            $veh_id = $row['Vehicle_ID'];
        }
        return $veh_id;
    }

    public function getTxnid($trans_id) {
        $row = array();
        $CI = & get_instance();
        $where = "st.Trip_Seat_ID='$trans_id' and st.Seat_status=3 and sb.PaymentStatus='S'";
        $CI->db->select("st.Payment_Token,pr.TxnId,pr.TxnTimeStamp");
        $CI->db->from("shuttle_trip_seat as st");
        $CI->db->join("shuttle_booking as sb", "sb.PaymentNo=st.Payment_Token");
        $CI->db->join("shuttle_payment_response as pr", "pr.OrderId=sb.PaymentNo");
        $CI->db->where($where);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }

    public function getpayment_tokenno($new_trip_id, $trans_id, $stop_id, $emp_id) {
        $CI = & get_instance();
        $seat_cost = 0;
        $payment_token = "";
        $dpoint = "";
        $route_name = "";
        $dist = 0;
        $vehicle_id = 0;
        $route_id = 0;$branch_id=0;
        $schedule_time = "";
        $schedule_date = "";
        $trip_type = "";
        $where1 = "st.Trip_Seat_ID='$trans_id' and sb.EmployeeId='$emp_id'";
        $CI->db->select("st.Trip_ID,st.Seat_Cost,st.Payment_Token,st.Travel_Type,sb.DropPoint,sb.BranchId");
        $CI->db->from("shuttle_trip_seat as st");
        $CI->db->join("shuttle_booking as sb", "sb.PaymentNo=st.Payment_Token");
        $CI->db->where($where1);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
            $seat_cost = $row['Seat_Cost'];
            $payment_token = $row['Payment_Token'];
            $trip_type = $row['Travel_Type'];
            $dpoint = $row['DropPoint'];
            $branch_id=$row['BranchId'];
        }
        $where2 = "s.Trip_ID='$new_trip_id' and (s.status=1 or s.status=2)";
        $CI->db->select("s.Schedule_Route_ID,s.Vehicle_ID,s.Schedule_Date,s.Schedule_Time,sr.Route_Name");
        $CI->db->from("shuttle_trip as s");
        $CI->db->join("shuttle_scheduled_route as sr", "sr.Scheduled_Route_ID=s.Schedule_Route_ID");
        $CI->db->where($where2);
        $query1 = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            $route_id = $row1['Schedule_Route_ID'];
            $schedule_date = $row1['Schedule_Date'];
            $schedule_time = $row1['Schedule_Time'];
            $vehicle_id = $row1['Vehicle_ID'];
            $route_name = $row1['Route_Name'];
        }

        // echo $route_id,$schedule_time,$schedule_date,$seat_cost,$stop_id,$payment_token,$emp_id,$dist,$vehicle_id;
        $this->seatcount_update($route_id, $schedule_time, $schedule_date, $seat_cost, $stop_id, $payment_token, $emp_id, $dist, $vehicle_id, $trip_type, $dpoint, $route_name,$branch_id);
    }

    public function alert_signal_failure($roster_id, $cab_id, $gps_date) {
        $row = array();
        $where = "ROSTER_ID='$roster_id' and CAB_ID='$cab_id'";
        $CI = & get_instance();
        $CI->db->select("if(GPS_DATE > '$gps_date','signalfailed','false') as tripsts");
        $CI->db->from("alertsignalfailure");
        $CI->db->where($where);
        $CI->db->limit(1);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
        }
        return $row;
    }
    
    public function getrating_sts($eid,$bid)
    {
        $row = array();//999486,991034,96922
        $sts="";
        $where = "e.EMPLOYEES_ID='$eid' and e.BRANCH_ID = '$bid' and r.BRANCH_ID='$bid' and (date(b.CREATED_DATE)>date(e.RATE_DATE) or e.RATE_DATE is null)"
                . "and (e.RATE_STATUS is null or e.RATE_STATUS=0) ";
        $CI = & get_instance();
        $CI->db->select("e.EMPLOYEES_ID,if(`b`.`FEEDBACK1` in(4,5),1,0) as ratests");
        $CI->db->from("employees as e");
        $CI->db->join("feedback_log as b ","b.EMPLOYEE_ID=e.EMPLOYEES_ID");
        $CI->db->join("rosters as r ","r.ROSTER_ID=b.ROSTER_ID");
        $CI->db->where($where);
        $CI->db->order_by("b.CREATED_DATE desc");
        $CI->db->limit(5);
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }
//    public function c_insert($tablename, $values) {
//        $CI = & get_instance();
//        $CI->db->set($values);
//        $CI->db->insert($tablename, $CI);
//        if ($CI->db->affected_rows() > 0) {
//            return 1;
//        } else {
//            return 0;
//        }
//    }

//    public function c_update($tablename, $values, $where) {
//        $CI = & get_instance();
//        $where1 = $where;
//        if ($where1 != '') {
//            $CI->db->where($where1);
//            $CI->db->update($tablename, $values);
//            if ($CI->db->affected_rows() > 0) {
//                return 1;
//            } else {
//                return 0;
//            }
//        }
//    }

//    public function c_selectrow($tablename, $where) {
//        $CI = & get_instance();
//        $where2 = $where;
//        $CI->db->select('*');
//        $CI->db->from($tablename);
//        $CI->db->where($where2);
//        $query1 = $CI->db->get();
//        if ($query1->num_rows() > 0) {
//            $row1 = $query1->row_array();
//            return $row1;
//        }
//    }

//    public function c_selectarray($tablename, $where) {
//        $CI = & get_instance();
//        $where2 = $where;
//        $CI->db->select('*');
//        $CI->db->from($tablename);
//        $CI->db->where($where2);
//        $query1 = $CI->db->get();
//        if ($query1->num_rows() > 0) {
//            $row1 = $query1->result_array();
//            return $row1;
//        }
//    }

    public function get_datetime() {
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('Y-m-d H:i:s');
        $cur_date = date('Y-m-d');
        $cur_time = date('H:i:s');
        $arr = array('cur_date_time' => $cur_datetime, 'cur_date' => $cur_date, 'cur_time' => $cur_time);
        return $arr;
    }

//    function AES_ENCRYPT($value, $secret) {
//        return rtrim(
//                base64_encode(
//                        mcrypt_encrypt(
//                                MCRYPT_RIJNDAEL_256, $secret, $value, MCRYPT_MODE_ECB, mcrypt_create_iv(
//                                        mcrypt_get_iv_size(
//                                                MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
//                                        ), MCRYPT_RAND)
//                        )
//                ), "\0"
//        );
//    }

//    function AES_DECRYPT($value, $secret) {
//        return rtrim(
//                mcrypt_decrypt(
//                        MCRYPT_RIJNDAEL_256, $secret, base64_decode($value), MCRYPT_MODE_ECB, mcrypt_create_iv(
//                                mcrypt_get_iv_size(
//                                        MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
//                                ), MCRYPT_RAND
//                        )
//                ), "\0"
//        );
//    }

    /* log */

    public function previous_log_arr_rosterpassenger($roster_id, $eid) {
        $CI = & get_instance();
        $created_arr = implode(',', unserialize(RP_CREATED));
        $CI->db->select('ROSTER_PASSENGER_STATUS as PREVIOUS_STATUS,ROSTER_PASSENGER_ID');
        $CI->db->from('roster_passengers');
        $CI->db->where("ROSTER_ID='" . $roster_id . "' and EMPLOYEE_ID='" . $eid . "' and  ROSTER_PASSENGER_STATUS in ($created_arr)");
        $CI->db->order_by("ROSTER_PASSENGER_ID");
        $CI->db->limit(1);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row1 = $query->result_array();
            return $row1;
        }
    }

    public function updated_log_arr_rosterpassenger($roster_id, $eid) {
        $CI = & get_instance();
        $CI->db->select('ROSTER_PASSENGER_STATUS as UPDATED_STATUS,ROSTER_ID,EMPLOYEE_ID,ROSTER_PASSENGER_ID');
        $CI->db->from('roster_passengers');
        $CI->db->where("ROSTER_ID='" . $roster_id . "' and EMPLOYEE_ID='" . $eid . "'");
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row2 = $query->result_array();
            return $row2;
        }
    }

    /* log End */

    public function getshuttle_location() {
        $row2 = array();
        $CI = & get_instance();
        $where = "s.ACTIVE=1 and b.ACTIVE=1";
        $CI->db->select("s.NAME as sitename,s.location as siteloc,b.BRANCH_NAME as branchname,b.BRANCH_ID");
        $CI->db->from("shuttle_site as s");
        $CI->db->join("branch as b", "b.SITE_ID=s.SITE_ID");
        $CI->db->where($where);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row2 = $query->result_array();
            return $row2;
        }
    }

    public function getcompany_location() {
        $row2 = array();
        $CI = & get_instance();
        $where = "b.OPERATION_TYPE='S' and b.ACTIVE=1 and c.ACTIVE=1";
        $CI->db->select("b.BRANCH_NAME as sitename,b.BRANCH_ID as branchid,b.LOCATION as siteloc,c.NAME as branchname,c.COMPANY_ID");
        $CI->db->from("branch as b");
        $CI->db->join("shuttle_company_master as c", "b.BRANCH_ID=c.BRANCH_ID");
        $CI->db->where($where);
        $CI->db->order_by("c.NAME");
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row2 = $query->result_array();
            return $row2;
        }
    }
    
    public function notification($eid, $bid)
    {
        $row2 = array();
        $CI = & get_instance();
        $where = "emp_id='$eid' and branch_id='$bid'";
        $CI->db->select("heading,message,created_at");
        $CI->db->from("shuttle_notification");       
        $CI->db->where($where);
        $CI->db->order_by("created_at desc");
        $CI->db->limit(20);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row2 = $query->result_array();            
        }
        return $row2;
    }
    public function otp_verification($mobile,$otp,$category)
    {
        $otpsts="";
        $getdate = $this->get_datetime();
        $cur_date = $getdate['cur_date'];
        $CI = & get_instance();       
        $where = "MOBILE_NO = '$mobile' and OTP='$otp' and OTP_CATEGORY='$category' and date(CREATED_DATE)='$cur_date'";
        $CI->db->select("if(VERIFIED_STATUS=0,'true','false') as otpsts");
        $CI->db->from("otp_verification");       
        $CI->db->where($where);  
        $CI->db->order_by("CREATED_DATE desc");
        $CI->db->limit(1);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row2 = $query->row_array(); 
            $otpsts=$row2['otpsts'];
        }
        return $otpsts;
    }
    
    //profile update email,mobileno
    
    public function profile_verification($field_name,$field_value)
    {
        $ret=0;
        $CI = & get_instance();
        $where = "$field_name = '" . $field_value . "' and ACTIVE=1 and CATEGORY='Shuttle'";
        $CI->db->select("EMPLOYEES_ID");
        $CI->db->from("employees");       
        $CI->db->where($where);         
        $query = $CI->db->get();
        if ($query->num_rows() > 0)
        {
            $ret=1;
        }
        return $ret;
    }
    public function calculate_distance($route_id,$p_order,$d_order)
    {
        $row2 = array();
        $CI = & get_instance();
        $where = "Route_ID='$route_id' and Route_Order>'$p_order' and Route_Order<='$d_order' ";//and status!=0 --for distance calculation
        $CI->db->select("sum(Distance) as dist");
        $CI->db->from("shuttle_route_stops");       
        $CI->db->where($where); 
        $CI->db->order_by("Route_Order");
        $query = $CI->db->get();
       // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row2 = $query->row_array();            
        }
        return $row2;
    }
    public function calculate_time($route_id,$p_order,$d_order)
    {
        $row2 = array();
        $CI = & get_instance();
        $where = "Route_ID='$route_id' and Route_Order between '$p_order' and '$d_order' ";//and status!=0 -- for time calculation
        $CI->db->select("TIMEDIFF(max(Stop_Time),min(Stop_Time)) as time");
        $CI->db->from("shuttle_route_stops");       
        $CI->db->where($where); 
        $CI->db->order_by("Route_Order");
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row2 = $query->row_array();    
            $time=$row2['time'];
        }
        $sec = 0;
        foreach (array_reverse(explode(':', $time)) as $k => $v)
            $sec += pow(60, $k) * $v;
        return $sec;
       // return $row2;
    }
    public function get_route_order($route_id,$stop_id)
    {
        $row2 = array();
        $CI = & get_instance();
        $route_order=0;
        $where = "Route_ID='$route_id' and Stop_ID='$stop_id' and status!=0";
        $CI->db->select("Route_Order,Stop_Time");
        $CI->db->from("shuttle_route_stops");       
        $CI->db->where($where);       
        $query = $CI->db->get();
        //echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row2 = $query->row_array(); 
            //$route_order=$row2['Route_Order'];
            
        }
        return $row2;
    }
    
    public function send_mail_forall()
    {
        $row2 = array();
        $CI = & get_instance();
        $where = "e.BRANCH_ID='42' and (SHUTTLE_COMPANY_ID=148 or SHUTTLE_COMPANY_ID=149) and ACTIVE=1 and SEND_MAIL_STATUS is null and EMPLOYEES_ID not in"
                . "(select EmployeeId from shuttle_booking where BranchId=42 )";
        $CI->db->select("e.EMAIL,e.EMPLOYEES_ID");
        $CI->db->from("employees e"); 
        $CI->db->where($where);  
       // $CI->db->limit(10);
        $query = $CI->db->get();
       // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row2 = $query->result_array(); 
        }
        return $row2;
    }

}
