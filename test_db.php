<?php
// Test database connection
$hostname = 'localhost';
$username = 'root';
$password = '';
$database = 'b2bshuttleservice';

try {
    $pdo = new PDO("mysql:host=$hostname;dbname=$database", $username, $password);
    echo "✅ Database connection successful!<br>";
    
    // Test if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 Tables found: " . count($tables) . "<br>";
    if (count($tables) > 0) {
        echo "Sample tables: " . implode(', ', array_slice($tables, 0, 5)) . "<br>";
    } else {
        echo "❌ No tables found - SQL import needed!<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage();
}
?>
