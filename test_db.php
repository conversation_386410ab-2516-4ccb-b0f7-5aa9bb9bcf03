<?php
echo "<h2>🔍 Database Connection Test</h2>";

// Test LOCAL database connection
echo "<h3>📍 Testing LOCAL Database (Current Config)</h3>";
$hostname = 'localhost';
$username = 'root';
$password = '';
$database = 'b2bshuttleservice';

try {
    $pdo = new PDO("mysql:host=$hostname;dbname=$database", $username, $password);
    echo "✅ <strong>LOCAL Database connection successful!</strong><br>";
    echo "🏠 Connected to: <strong>localhost/b2bshuttleservice</strong><br><br>";

    // Test if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "📋 Tables found: <strong>" . count($tables) . "</strong><br>";
    if (count($tables) > 0) {
        echo "✅ Sample tables: " . implode(', ', array_slice($tables, 0, 5)) . "<br>";
        echo "🎉 <strong>LOCAL DATABASE IS READY!</strong><br>";
    } else {
        echo "❌ No tables found - SQL import needed!<br>";
    }

} catch (PDOException $e) {
    echo "❌ LOCAL Database connection failed: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// Test SERVER database connection
echo "<h3>🌐 Testing SERVER Database (AWS RDS)</h3>";
$server_hostname = 'goodappdbfrmtopsadb28mar2023.c4ruwpn24miq.ap-southeast-1.rds.amazonaws.com:6005';
$server_username = 'etioswan';
$server_password = '$occer6a!!on$ky';
$server_database = 'b2bshuttleservice';

try {
    $pdo_server = new PDO("mysql:host=$server_hostname;dbname=$server_database", $server_username, $server_password);
    echo "✅ <strong>SERVER Database connection successful!</strong><br>";
    echo "🌐 Connected to: <strong>AWS RDS/b2bshuttleservice</strong><br><br>";

    // Test if tables exist
    $stmt = $pdo_server->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "📋 Tables found: <strong>" . count($tables) . "</strong><br>";
    if (count($tables) > 0) {
        echo "✅ Sample tables: " . implode(', ', array_slice($tables, 0, 5)) . "<br>";
        echo "🎉 <strong>SERVER DATABASE IS AVAILABLE!</strong><br>";
    }

} catch (PDOException $e) {
    echo "❌ SERVER Database connection failed: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>📋 Summary</h3>";
echo "🔧 <strong>Current API Configuration:</strong> LOCAL Database (localhost)<br>";
echo "📁 Config file: application/config/database.php<br>";
echo "🧪 Test your API: <a href='index.php/shuttle' target='_blank'>Test API Endpoint</a><br>";
?>
