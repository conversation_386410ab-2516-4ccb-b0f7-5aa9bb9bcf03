<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');
//set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
//include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
//include(APPPATH . 'libraries/Shuttledao.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of FixedShuttlemdl
 *
 * <AUTHOR>
 */
class FixedShuttlemdl extends CI_Model {

    //put your code here
    function __construct() {
        parent::__construct();
        $this->load->model('ShuttleCommonmdl', 'cmodel');
        $this->load->model('Shuttlemdl', 'smodel');
    }

    public function shuttle_fixed_routes($branch_id) {
        $element_array = array();
        $waypoints = array();

        $bid = empty($branch_id) ? 1 : $branch_id;

        $where = "Route_Type='Fixed' and Status=1 and Branch_ID='$bid' and if(Branch_ID='1',(Route_Name = 'P'||Route_Name = 'D'),Route_Name='P')";
        $array = $this->cmodel->c_selectarray('shuttle_scheduled_route', $where);
        // echo $this->db->last_query();
        foreach ($array as $val) {
            $waypoints = array();
            $route_id = $val['Scheduled_Route_ID'];
            $routeid_name = $val['Route_ID'];
            $travel_type = $val['Travel_Type'];
            $tariff_type = $val['Tariff_Type'];
            $where1 = "rs.Route_ID='$route_id' and rs.status!=0 and s.status=1";
            $this->db->select("rs.Route_ID,rs.Stop_ID,s.Stops_Name,s.Stops_Lat,s.Stops_Long,s.Trip_Type,rs.status as sts");
            $this->db->from("shuttle_route_stops as rs");
            $this->db->join("shuttle_stops as s", "s.Stops_ID=rs.Stop_ID");
            $this->db->where($where1);
            $this->db->order_by("rs.Stop_Time");
            $query = $this->db->get();
            if ($query->num_rows() > 0) {
                $row = $query->result_array();
                foreach ($row as $val1) {
                    $waypoints[] = array('way_point' => $val1['Stops_Name'], 'status' => $val1['sts'], 'lat' => $val1['Stops_Lat'], 'long' => $val1['Stops_Long']);
                }
            }
            $element_array[] = array('route_id' => $route_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'tariff_type' => $tariff_type, 'start_point' => $val['Start_Location'], 'end_point' => $val['End_Location'], 'category' => $val['Route_Type'], 'waypoint_details' => $waypoints);
        }
        $result_array = array('status' => 1, 'Route' => $element_array);

        return $result_array;
    }

    public function shuttle_fixed_routes_v1($branch_id, $shuttle_comp_id) {
        $element_array = array();
        $waypoints = array();
        $comp_name = "";
        $where1 = "COMPANY_ID='$shuttle_comp_id' and BRANCH_ID='$branch_id'";
        $row = $this->cmodel->c_selectrow('shuttle_company_master', $where1);
        if ($row) {
            $comp_name = $row['NAME'];
        }

        $where = "Route_Type='Fixed' and Status=1 and Branch_ID='$branch_id' and if(Branch_ID='$branch_id',(Route_Name = 'P'||Route_Name = 'D'),Route_Name='P')";
        $this->db->select("*");
        $this->db->from("shuttle_scheduled_route");       
        $this->db->where($where);
        $this->db->order_by("Route_ID");   
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
                $array = $query1->result_array();
        }        
        foreach ($array as $val) {
            $waypoints = array();
            $route_id = $val['Scheduled_Route_ID'];
            $routeid_name = $val['Route_ID'];
            $travel_type = $val['Travel_Type'];
            $tariff_type = $val['Tariff_Type'];
            $viapoint=$val['Via_Points'];           
            $route_type=$val['Route_Name'];
            $route_desc=$val['Route_Desc'];
            
            $km=0;
            $stopping_points=0;    
            $inactive_points=0;
            $where1 = "rs.Route_ID='$route_id' and s.status=1";//and rs.status!=0 
            $this->db->select("rs.Route_ID,rs.Stop_ID,s.Stops_Name,s.Stops_Lat,s.Stops_Long,s.Trip_Type,rs.status as sts,rs.Distance as totkm");
            $this->db->from("shuttle_route_stops as rs");
            $this->db->join("shuttle_stops as s", "s.Stops_ID=rs.Stop_ID");
            $this->db->where($where1);
            $this->db->order_by("rs.Stop_Time");
            $query = $this->db->get();
            if ($query->num_rows() > 0) {
                $row = $query->result_array();
                
                foreach ($row as $val1) {
                    $km=$km+$val1['totkm'];
                    $sts = intval($val1['sts']);
                   // $inactive_points=$sts==0?$inactive_points+1:0;
                    $stopping_points=$sts==1?$stopping_points+1:$stopping_points;
                    if($route_type=='P' && $sts!=0){
                        
                        $stop_name = ucwords($val1['Stops_Name']);                        
                        $stops_lat = $val1['Stops_Lat'];
                        $stops_long = $val1['Stops_Long'];                        
                        $waypoints[] = array('way_point' => $stop_name, 'status' => $sts, 'lat' => $stops_lat, 'long' => $stops_long);
                        if (strtolower($comp_name) == strtolower($stop_name)) {
                            break;
                        }
                    }
                    else if($route_type=='D' && $sts!=0)
                    {
//                        if (strtolower($comp_name) == strtolower($stop_name)) {
//                            continue;
//                        }
                        //$stopping_points=$stopping_points+1;
                        $stop_name = $val1['Stops_Name'];
                        $sts = $val1['sts'];
                        $stops_lat = $val1['Stops_Lat'];
                        $stops_long = $val1['Stops_Long'];
                        $waypoints[] = array('way_point' => $stop_name, 'status' => $sts, 'lat' => $stops_lat, 'long' => $stops_long);
                        
                    }
                }                
            }
       // }
            
            $element_array[] = array('route_id' => $route_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'tariff_type' => $tariff_type, 'start_point' => $val['Start_Location'], 'end_point' => $val['End_Location'], 'category' => $val['Route_Type'],'via'=>$viapoint.' - '.$stopping_points.' Stops ('.round($km).')km','route_type'=>$route_type,'route_desc'=>$route_desc,'waypoint_details' => $waypoints);
        }
        $result_array = array('status' => 1, 'Route' => $element_array);
        return $result_array;
    }



   public function shuttle_fixed_routes_v2($branch_id, $shuttle_comp_id) 
   {
        $element_array = array();
        $waypoints = array();
        $comp_name = "";
        $where1 = "COMPANY_ID='$shuttle_comp_id' and BRANCH_ID='$branch_id'";
        $row = $this->cmodel->c_selectrow('shuttle_company_master', $where1);
        if ($row) {
            $comp_name = $row['NAME'];
        }

        $where = "Route_Type='Fixed'  and Tariff_Type='Fixed'  and Status=1 and Branch_ID='$branch_id' and if(Branch_ID='$branch_id',(Route_Name = 'P'||Route_Name = 'D'),Route_Name='P')";
        $this->db->select("*");
        $this->db->from("shuttle_scheduled_route");       
        $this->db->where($where);
        $this->db->order_by("Route_ID");   
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
                $array = $query1->result_array();
        }        
        foreach ($array as $val) {
            $waypoints = array();
            $route_id = $val['Scheduled_Route_ID'];
            $routeid_name = $val['Route_ID'];
            $travel_type = $val['Travel_Type'];
            $tariff_type = $val['Tariff_Type'];
            $viapoint=$val['Via_Points'];           
            $route_type=$val['Route_Name'];
            $route_desc=$val['Route_Desc'];
            
            $km=0;
            $stopping_points=0;    
            $inactive_points=0;
            $where1 = "rs.Route_ID='$route_id' and s.status=1";//and rs.status!=0 
            $this->db->select("rs.Route_ID,rs.Stop_ID,s.Stops_Name,s.Stops_Lat,s.Stops_Long,s.Trip_Type,rs.status as sts,rs.Distance as totkm");
            $this->db->from("shuttle_route_stops as rs");
            $this->db->join("shuttle_stops as s", "s.Stops_ID=rs.Stop_ID");
            $this->db->where($where1);
            $this->db->order_by("rs.Stop_Time");
            $query = $this->db->get();
            if ($query->num_rows() > 0) {
                $row = $query->result_array();
                
                foreach ($row as $val1) {
                    $km=$km+$val1['totkm'];
                    $sts = intval($val1['sts']);
                
                    $stopping_points=$sts==1?$stopping_points+1:$stopping_points;
                    if($route_type=='P' && $sts!=0){
                        
                        $stop_name = ucwords($val1['Stops_Name']);                        
                        $stops_lat = $val1['Stops_Lat'];
                        $stops_long = $val1['Stops_Long'];                        
                        $waypoints[] = array('way_point' => $stop_name, 'status' => $sts, 'lat' => $stops_lat, 'long' => $stops_long);
                        if (strtolower($comp_name) == strtolower($stop_name)) {
                            break;
                        }
                    }
                    else if($route_type=='D' && $sts!=0)
                    {

                        $stop_name = $val1['Stops_Name'];
                        $sts = $val1['sts'];
                        $stops_lat = $val1['Stops_Lat'];
                        $stops_long = $val1['Stops_Long'];
                        $waypoints[] = array('way_point' => $stop_name, 'status' => $sts, 'lat' => $stops_lat, 'long' => $stops_long);
                        
                    }
                }                
            }


            $oneday_fixed_price = 0;       
            $tariff_cal_name = "";
            
            $tariff_array = array();
            $vehicle_id = 1;
            if($branch_id == 2){
              $tariff_loginid = 4;
            }else if($branch_id == 3){
              $tariff_loginid = 5;
            }else{
              $tariff_loginid = 1;
             }
            $where1 = "BranchId='$branch_id' and Active ='1' and Tariff_Type='$tariff_type'  and  '$km'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and VehicleId='$vehicle_id' and LoginId='$tariff_loginid' order by NumberofDays"; 
            $row1 = $this->cmodel->c_selectarray('shuttle_tariff', $where1);
            foreach ($row1 as $val1) {
                $tariffId = $val1['TariffId'];
                $flatCost = $val1['Tariff_Flat_Cost'];
                $tariff_name = $val1['Tariff_Name'];
                $tarffcal_days = $val1['NumberofDays'];
                $tariff_cal_name = $val1['Tariff_Calculation_Name'];
                $baseKm = $val1['Tariff_Base_Km'];
                $basCost = $val1['Tariff_Base_Cost'];
                $extKmCost = $val1['Tariff_Ext_Km_Cost'];
                $percentage = $val1['Percentage'];
                $validTrip = $val1['NumberofTrips'];
                $validDays_Trip = 1;
                $noof_trips = 1;
               
                $validDays = 1;
                if ($flatCost == 0) {
                    $extKmAmt = 0;
                    $exkm = 0;
                    $fare = 0;
                    $percentAmt = 0;
                    $exkm = $dist - $baseKm;
                    if ($exkm > 0) {
                        $extKmAmt = $exkm * $extKmCost;
                    }
                    if ($percentage == 0) {
                        $fare = ($basCost + $extKmAmt) * $validTrip;
                    } else {
                        $percentAmt = ($basCost + $extKmAmt) * $percentage / 100;
                        $fare = ($basCost + $extKmAmt + $percentAmt) * $validTrip;
                    }
                    $actual_fare = ($basCost + $extKmAmt) * $validTrip;
                } else {
                    if ($percentage == 0) {
                        $fare = $flatCost * $validTrip;
                    } else {
                        $percentAmt = ($flatCost) * $percentage / 100;
                        $fare = ($flatCost + $percentAmt) * $validTrip;
                    }
                    $actual_fare = $flatCost * $validTrip;
                }
                $tariff_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'actual_fare' => round($actual_fare), 'fare' => round($fare), 'validDays' => $validDays_Trip, 'validTrip' => $noof_trips, 'onedayFixedprice' => $oneday_fixed_price, 'tariff_cal_name' => $tariff_cal_name, 'percentage' => abs($percentage), 'validity_days' => $validDays);
            }       
            
            $element_array[] = array('route_id' => $route_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'tariff_type' => $tariff_type, 'start_point' => $val['Start_Location'], 'end_point' => $val['End_Location'], 'category' => $val['Route_Type'],'via'=>$viapoint.' - '.$stopping_points.' Stops ('.round($km).')km','route_type'=>$route_type,'route_desc'=>$route_desc,'waypoint_details' => $waypoints,'tariff_details'=>$tariff_array);
        }
        $result_array = array('status' => 1, 'Route' => $element_array);
        return $result_array;
    }

   public function shuttle_vehicle_tracking_v2($branch_id, $shuttle_comp_id)
    {
        $element_array = array();
        $waypoints = array();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $comp_name = "";
        $where1 = "COMPANY_ID='$shuttle_comp_id' and BRANCH_ID='$branch_id'";
        $row = $this->cmodel->c_selectrow('shuttle_company_master', $where1);
        if ($row) {
            $comp_name = $row['NAME'];
        }

        $where = "s.Route_Type='Fixed'  and s.Tariff_Type='Fixed'  and s.Status=1 and s.Branch_ID='$branch_id' 
        and if(s.Branch_ID='$branch_id',(s.Route_Name = 'P'|| s.Route_Name = 'D'),s.Route_Name='P')
         and r.ESTIMATE_START_TIME>='$curdate_time' and r.ACTIVE=1
         and s.Start_Location=r.START_LOCATION and s.End_Location=r.END_LOCATION";
        $this->db->select("r.ROSTER_ID,r.ESTIMATE_START_TIME,s.*");
        $this->db->from("shuttle_scheduled_route as s");   
        $this->db->join("rosters as r", 'r.BRANCH_ID=s.Branch_ID');    
        $this->db->where($where);
        $this->db->order_by("r.Route_ID");   
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
                $array = $query1->result_array();
        }        
        foreach ($array as $val) {
            $waypoints = array();
            $route_id = $val['Scheduled_Route_ID'];
            $routeid_name = $val['Route_ID'];
            $travel_type = $val['Travel_Type'];
            $tariff_type = $val['Tariff_Type'];
            $viapoint=$val['Via_Points'];           
            $route_type=$val['Route_Name'];
            $route_desc=$val['Route_Desc'];
            $roster_id=$val['ROSTER_ID'];
            $start_time=$val['ESTIMATE_START_TIME'];
            
            $km=0;
            $stopping_points=0;    
            $inactive_points=0;
            $where1 = "rs.Route_ID='$route_id' and s.status=1";//and rs.status!=0 
            $this->db->select("rs.Route_ID,rs.Stop_ID,s.Stops_Name,s.Stops_Lat,s.Stops_Long,s.Trip_Type,rs.status as sts,rs.Distance as totkm");
            $this->db->from("shuttle_route_stops as rs");
            $this->db->join("shuttle_stops as s", "s.Stops_ID=rs.Stop_ID");
            $this->db->where($where1);
            $this->db->order_by("rs.Stop_Time");
            $query = $this->db->get();
            if ($query->num_rows() > 0) {
                $row = $query->result_array();
                
                foreach ($row as $val1) {
                    $km=$km+$val1['totkm'];
                    $sts = intval($val1['sts']);
                
                    $stopping_points=$sts==1?$stopping_points+1:$stopping_points;
                    if($route_type=='P' && $sts!=0){
                        
                        $stop_name = ucwords($val1['Stops_Name']);                        
                        $stops_lat = $val1['Stops_Lat'];
                        $stops_long = $val1['Stops_Long'];                        
                        $waypoints[] = array('way_point' => $stop_name, 'status' => $sts, 'lat' => $stops_lat, 'long' => $stops_long);
                        if (strtolower($comp_name) == strtolower($stop_name)) {
                            break;
                        }
                    }
                    else if($route_type=='D' && $sts!=0)
                    {

                        $stop_name = $val1['Stops_Name'];
                        $sts = $val1['sts'];
                        $stops_lat = $val1['Stops_Lat'];
                        $stops_long = $val1['Stops_Long'];
                        $waypoints[] = array('way_point' => $stop_name, 'status' => $sts, 'lat' => $stops_lat, 'long' => $stops_long);
                        
                    }
                }                
            }  
            
            $element_array[] = array('roster_id'=>$roster_id,'start_time'=>$start_time,'route_id' => $route_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'tariff_type' => $tariff_type, 'start_point' => $val['Start_Location'], 'end_point' => $val['End_Location'], 'category' => $val['Route_Type'],'via'=>$viapoint.' - '.$stopping_points.' Stops ('.round($km).')km','route_type'=>$route_type,'route_desc'=>$route_desc,'waypoint_details' => $waypoints);
        }
        $result_array = array('status' => 1, 'Route' => $element_array);
        return $result_array;
    }

  
    public function shuttle_vehicle_tracking_v3($branch_id, $shuttle_comp_id)
    {
        $element_array = array();
        $route_list = array();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $from_time = date('Y-m-d H:i:s', strtotime('-15 minutes'));
        $to_time =  date('Y-m-d H:i:s', strtotime('+3600 minutes'));
        $btween_time =  $from_time . "' and '" . $to_time;
        $comp_name = "";
        $where1 = "COMPANY_ID='$shuttle_comp_id' and BRANCH_ID='$branch_id'";
        $row = $this->cmodel->c_selectrow('shuttle_company_master', $where1);
        if ($row) {
            $comp_name = $row['NAME'];
        }

        $where = "Route_Type='Fixed' and tariff_type='Fixed' and Status=1 and Branch_ID='$branch_id' and if(Branch_ID='$branch_id',(Route_Name = 'P'||Route_Name = 'D'),Route_Name='P')";
        $this->db->select("Route_Desc,Start_Location,End_Location");
        $this->db->from("shuttle_scheduled_route");
        $this->db->where($where);
        $this->db->order_by("Route_ID");
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $array = $query1->result_array();
        }

        foreach ($array as $val) {

            $route_desc = $val['Route_Desc'];
            $Start_Location = $val['Start_Location'];
            $End_Location = $val['End_Location'];

            $where1 = "r.START_LOCATION='$Start_Location' and r.END_LOCATION ='$End_Location' and  r.ESTIMATE_START_TIME between '$btween_time' and r.ACTIVE=1 and r.ROSTER_STATUS not in (345,347,349,351,1024,1113,1115,1117,1119,1033,1035,1037,1039,1049,1051,1053,1055,2397,2399,2909,2911,3165,3167,3081,3083,3085,3087,3097,3101,3103,857,859,861,863)"; //and rs.status!=0 
            $this->db->select("r.ROSTER_ID ,r.TRIP_TYPE, r.CAB_ID ,if(r.ACTUAL_START_TIME = NULL,'On The Way','On Boarding') as cab_status , TIME_FORMAT(r.ESTIMATE_START_TIME,'%h:%i %p')  as est_dipatch_time ,r.TRIP_APPROVED_KM");
            $this->db->from("rosters as r");
            $this->db->where($where1);
            $this->db->order_by("r.Route_ID");
            $query = $this->db->get();
            if ($query->num_rows() > 0) {
                $row = $query->result_array();

                foreach ($row as $val1) {
                    $ROSTER_ID = $val1['ROSTER_ID'];
                    $TRIP_TYPE = $val1['TRIP_TYPE'];
                    $CAB_ID = $val1['CAB_ID'];
                    $cab_status = $val1['cab_status'];
                    $est_dipatch_time = $val1['est_dipatch_time'];
                    $distance = $val1['TRIP_APPROVED_KM'];

                    $route_list[] = array('ROSTER_ID' => $ROSTER_ID, 'TRIP_TYPE' => $TRIP_TYPE, 'CAB_ID' => $CAB_ID, 'cab_status' => $cab_status, 'est_dipatch_time' => $est_dipatch_time, 'distance' => $distance, 'ETA' => '0');
                }
            }

            $element_array[] = array('route_desc' => $route_desc, 'route_lists' => $route_list);
        }

        $result_array = array('status' => 1, 'Route' => $element_array);
        return $result_array;
    }
   


}
