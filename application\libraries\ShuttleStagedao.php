<?php
//include(APPPATH . 'libraries/Shuttledao.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of ShuttleStagedao
 *
 * <AUTHOR>
 */
class ShuttleStagedao {

    //put your code here

    public function get_seats($route_id, $resheduledate) {
        $dao = new Shuttledao();
        $row = array();
        $CI = & get_instance();
        $CI->load->model('ShuttleCommonmdl');
        $getdate = $CI->ShuttleCommonmdl->get_datetime();
        $cur_date = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $edate = "";
        $sts = "";
        if ($resheduledate == '0000-00-00') {
            $sts = "sr.Status=1";
            if (date("w", strtotime($cur_date)) == 5 || date("w", strtotime($cur_date)) == 6) {
                $edate = date('Y-m-d', strtotime('next monday', strtotime($cur_date)));
            } else {
                $edate = date('Y-m-d', strtotime('+1 day', strtotime($cur_date)));
            }
        } else {
            $sts = "(sr.Status=1 or sr.Status=2)";
            $cur_date = $resheduledate;
            $edate = $resheduledate;
        }

        $i = $dao->check_noservice_date($edate);
        if ($i == 0) {
            $edate = date('Y-m-d', strtotime("+1 day", strtotime($edate)));
        }
        $where = "s.Schedule_Route_ID='$route_id' AND (s.status=1 OR s.status=2) AND s.Schedule_Date BETWEEN '$cur_date' AND '$edate' ";
        $CI->db->select("s.Trip_ID,s.Vehicle_ID,s.Schedule_Date,s.Schedule_Time,s.Seats_Used,sr.Route_Name,sr.Return_Route_No,sr.Route_ID,sr.Travel_Type,sr.Tariff_Type");
        $CI->db->from("shuttle_trip as s");
        $CI->db->join("shuttle_scheduled_route as sr", 'sr.Scheduled_Route_ID=s.Schedule_Route_ID');
        $CI->db->order_by("s.Schedule_Time");
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        return $row;
    }

    public function update_stages($trip_id, $p_order, $d_order) {
        $row = array();
        $seat_number = 0;
        $CI = & get_instance();
        $where = "trip_id='$trip_id' and stage_id >= $p_order and stage_id < $d_order and "
                . "booked_status=0 and payment_token='0'";
        $CI->db->select("id,seat_number,stage_id");
        $CI->db->from("shuttle_stage as s");
        $CI->db->where($where);
        $CI->db->group_by("stage_id");
        $CI->db->order_by("stage_id");
        $query = $CI->db->get();
       // echo $CI->db->last_query();

        if ($query->num_rows() > 0) {
            $row = $query->result_array();
        }
        
        return $row;
    }

    public function stagebooking_payment_tokenno($new_trip_id, $trans_id, $stop_id, $emp_id, $p_routeorder_p, $p_routeorder_d) {
        $CI = & get_instance();
        $seat_cost = 0;
        $payment_token = "";
        $dpoint = "";
        $route_name = "";
        $dist = 0;
        $vehicle_id = 0;
        $route_id = 0;
        $schedule_time = "";
        $schedule_date = "";
        $trip_type = "";
        $where1 = "st.Trip_Seat_ID='$trans_id' and sb.EmployeeId='$emp_id'";
        $CI->db->select("st.Trip_ID,st.Seat_Cost,st.Payment_Token,st.Travel_Type,sb.DropPoint,sb.BranchId");
        $CI->db->from("shuttle_trip_seat as st");
        $CI->db->join("shuttle_booking as sb", "sb.PaymentNo=st.Payment_Token");
        $CI->db->where($where1);
        $query = $CI->db->get();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
            $seat_cost = $row['Seat_Cost'];
            $payment_token = $row['Payment_Token'];
            $trip_type = $row['Travel_Type'];
            $dpoint = $row['DropPoint'];
            $branch_id=$row['BranchId'];
        }
        $where2 = "s.Trip_ID='$new_trip_id' and (s.status=1 or s.status=2)";
        $CI->db->select("s.Schedule_Route_ID,s.Vehicle_ID,s.Schedule_Date,s.Schedule_Time,sr.Route_Name");
        $CI->db->from("shuttle_trip as s");
        $CI->db->join("shuttle_scheduled_route as sr", "sr.Scheduled_Route_ID=s.Schedule_Route_ID");
        $CI->db->where($where2);
        $query1 = $CI->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            $route_id = $row1['Schedule_Route_ID'];
            $schedule_date = $row1['Schedule_Date'];
            $schedule_time = $row1['Schedule_Time'];
            $vehicle_id = $row1['Vehicle_ID'];
            $route_name = $row1['Route_Name'];
        }
        $this->stage_seatcount_update($route_id, $schedule_time, $schedule_date, $seat_cost, $stop_id, $payment_token, $emp_id, $dist, $vehicle_id, $trip_type, $dpoint, $route_name, $p_routeorder_p, $p_routeorder_d,$branch_id);
    }

    public function stage_seatcount_update($route_id, $schedule_time, $schedule_date, $seat_cost, $stop_id, $payment_token, $emp_id, $dist, $vehicle_id, $trip_type, $dpoint, $route_name, $p_routeorder_p, $p_routeorder_d,$branch_id) {
        $dao = new Shuttledao();
        $CI = & get_instance();
        $CI->load->model('ShuttleCommonmdl');
        $getdate = $CI->ShuttleCommonmdl->get_datetime();
        $cur_date = $getdate['cur_date'];
        $cur_date_time = $getdate['cur_date_time'];
        $ret = 0;
        $trip_id = 0;
        $trip_sts = 0;
        $sch_route_id = 0;
        $seat_no = 0;
        $t_type = "";
        $sch_time = "";
        try {

            $where = "Schedule_Route_ID='$route_id' and Schedule_Time='$schedule_time' and Vehicle_ID='$vehicle_id' and Schedule_Date='$schedule_date' and (status=1 or status=2) "; //LOCK IN SHARE MODE
            $CI->db->select("st.Trip_ID,st.status,st.Schedule_Time as stoptime,st.Schedule_Route_ID");
            $CI->db->from("shuttle_trip as st");
            $CI->db->where($where);
            $query2 = $CI->db->get();
            if ($query2->num_rows() > 0) {
                $row2 = $query2->row_array();
                $trip_id = $row2['Trip_ID'];
                $trip_sts = $row2['status'];
                $sch_time = $row2['stoptime'];
                $sch_route_id = $row2['Schedule_Route_ID'];

                $where_update = "Trip_ID=$trip_id and (status=1 or status=2)";
                $CI->db->set("Seats_Used", "Seats_Used+1", FALSE);
                $CI->db->where($where_update);
                $CI->db->limit(1);
                $CI->db->update('shuttle_trip');

                if ($CI->db->affected_rows() > 0) {
                    $where2 = " ss.Route_ID='$sch_route_id' and ss.Stop_ID='$stop_id' and stops.status='1' and"
                            . " loc.CREATE_BY=stops.Trip_Type and dist.BRANCH_ID=stops.Branch_ID and loc.ACTIVE='1'";
                    $CI->db->select("ss.Route_Stops_ID,addtime('$sch_time',ss.Stop_Time) as stoptime,loc.LOCATION_ID,"
                            . "dist.APPROVED_DISTANCE,if(stops.Trip_Type='P','Pickup','Drop') as ttype,stops.Stops_Name");
                    $CI->db->from('shuttle_route_stops as ss');
                    $CI->db->join('shuttle_stops as stops', 'stops.Stops_ID=ss.Stop_ID');
                    $CI->db->join('locations as loc', 'stops.Stops_Name=loc.LOCATION_NAME');
                    $CI->db->join('approve_distances as dist', 'dist.LOCATION_ID=loc.LOCATION_ID');
                    $CI->db->where($where2);
                    $query = $CI->db->get();

                    if ($trip_type == 'D' || $stop_id == 16) {
                        error_log("~~~~~~~  get stop id query  $stop_id ~~~~~~" . $CI->db->last_query(), 0);
                    }
                    if ($query->num_rows() > 0) {
                        $row = $query->row_array();
                        $trip_id = $trip_id;
                        $route_stop_id = $row['Route_Stops_ID'];
                        $stop_time = $row['stoptime'];
                        $loc_id = $row['LOCATION_ID'];
                        $approvedist = $row['APPROVED_DISTANCE'];
                        $ttype = $row['ttype'];
                        $stop_name = $row['Stops_Name'];
                        // $trip_sts = $row['status'];

                        $CI->load->model('ShuttleStagemdl');
                        $arr = $CI->ShuttleStagemdl->check_seats_instages($p_routeorder_p, $p_routeorder_d, $trip_id, 'booking');
                        if ($arr['status'] == 1) {
                            $seat_no = $arr['stage_arr']['seat_number'];
                            $where0 = "trip_id=$trip_id and stage_id >= $p_routeorder_p and stage_id < $p_routeorder_d and  booked_status=0 and seat_number=$seat_no";
                            $CI->db->set('booked_status', 1);
                            $CI->db->set('payment_token', $payment_token);
                            $CI->db->where($where0);
                            $CI->db->order_by("stage_id");
                            $CI->db->update('shuttle_stage');
                            // echo $CI->db->last_query();
                        } else {
                            $stagearr = $arr['stage_arr'];
                            for ($j = 0; $j < count($arr['stage_arr']); $j++) {
                                if ($j == 0) {
                                    $seat_no = $stagearr[$j]['seat_number'];
                                }
                                $id = $stagearr[$j]['id'];
                                $where0 = "trip_id=$trip_id and id=$id";
                                $CI->db->set('booked_status', 1);
                                $CI->db->set('payment_token', $payment_token);
                                $CI->db->where($where0);
                                $CI->db->order_by("id");
                                $CI->db->update('shuttle_stage');
                            }
                        }
                        $where = "Trip_ID='$trip_id' and Seat_Number='$seat_no' and Seat_Cost is null and Seat_Begin_Stop is null "; //LOCK IN SHARE MODE
                        $CI->db->select("Trip_Seat_ID");
                        $CI->db->from("shuttle_trip_seat");
                        $CI->db->where($where);
                        $query2 = $CI->db->get();
                        if ($query2->num_rows() > 0) {
                            $row2 = $query2->row_array();
                            $Trip_Seat_ID = $row2['Trip_Seat_ID'];
                            $where1 = "Trip_Seat_ID='$Trip_Seat_ID' and Seat_Cost is null";
                            $CI->db->set("Seat_Cost", $seat_cost);
                            $CI->db->set("Seat_Begin_Stop", $route_stop_id);
                            $CI->db->set("Payment_Token", $payment_token);
                            $CI->db->set("Travel_Type", $trip_type);
                            $CI->db->set('Seat_status', 1);
                            $CI->db->where($where1);
                            $CI->db->order_by("Trip_Seat_ID");
                            $CI->db->limit(1);
                            $CI->db->update('shuttle_trip_seat');
                        } else {
                            $data = array('Trip_ID' => $trip_id, 'Seat_Number' => $seat_no, 'Seat_Cost' => $seat_cost, 'Seat_Begin_Stop' => $route_stop_id,
                                'Payment_Token' => $payment_token, 'Travel_Type' => $trip_type); //have to discuss otp_verification 
                            $CI->ShuttleCommonmdl->c_insert('shuttle_trip_seat', $data);
                        }
                        if ($schedule_date == $cur_date && $trip_sts == 2) {
                            $roster_id = $dao->updateroster_clubbingcount('S' . $trip_id, 'add');
                            if ($roster_id) {
                                if ($route_name == "P") {
                                    $empdata = array('LOCATION_ID' => $loc_id);
                                    $approvedist = $approvedist;
                                    $t_type = "Pickup";
                                } else {
                                    $t_type = "Drop";
                                    $wheredrop = "loc.CREATE_BY='D' and loc.LOCATION_NAME='$dpoint'";
                                    $CI->db->select("loc.LOCATION_ID,dist.APPROVED_DISTANCE");
                                    $CI->db->from("locations as loc");
                                    $CI->db->join('approve_distances as dist', 'dist.LOCATION_ID=loc.LOCATION_ID');
                                    $CI->db->where($wheredrop);
                                    $query1 = $CI->db->get();
                                    if ($query1->num_rows() > 0) {
                                        $row1 = $query1->row_array();
                                        $loc_id = $row1['LOCATION_ID'];
                                        $approvedist = $row1['APPROVED_DISTANCE'];
                                    }
                                    $empdata = array('LOCATION_ID' => $loc_id);
                                }
                                $CI->load->model('ShuttleCommonmdl');
                                $whereemp = "EMPLOYEES_ID='$emp_id' and ACTIVE=1 and CATEGORY='Shuttle' and BRANCH_ID='$branch_id'";
                                $CI->ShuttleCommonmdl->c_update('employees', $empdata, $whereemp);

                                $tripdate_time = $cur_date . " " . $stop_time;
                                $OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
                                $data = array('EMPLOYEE_ID' => $emp_id, 'ROSTER_ID' => $roster_id['ROSTER_ID'], 'ESTIMATE_START_TIME' => $tripdate_time, 'ACTIVE' => 1, 'ROUTE_ORDER' => $approvedist,
                                    'CREATED_BY' => $emp_id, 'CREATED_DATE' => $cur_date_time, 'ESTIMATE_END_TIME' => $roster_id['ESTIMATE_END_TIME'], 'LOCATION_ID' => $loc_id); //have to discuss otp_verification 
                                $rpcnt = $CI->ShuttleCommonmdl->c_insert('roster_passengers', $data);
                                $rpid = $CI->db->insert_id();
                                if ($rpcnt > 0 && isset($roster_id['VEHICLE_REG_NO'])) {
                                    $row = $dao->get_employee_details($emp_id,$branch_id);
                                    $empname = $CI->ShuttleCommonmdl->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                                    $notify_msg = "$empname - $stop_name  was clubbed in your trip";
                                    $drivergcm = $roster_id['MOBILE_GCM'];
                                    $emp_mobile = $CI->ShuttleCommonmdl->AES_DECRYPT($row['MOBILE'], AES_ENCRYPT_KEY);
                                    // $originator=$row['PROPERTIE_VALUE'];
                                    $data1 = array("MOBILE_NO" => $emp_mobile, 'ROSTER_PASSENGER_ID' => $rpid, 'OTP' => $OTP, 'VERIFIED_STATUS' => 0, 'OTP_CATEGORY' => $ttype, 'CREATED_DATE' => $cur_date_time);
                                    $CI->ShuttleCommonmdl->c_insert('otp_verification', $data1);
                                    $smsmsg = "Your cab details for $t_type - " . $roster_id['VEHICLE_REG_NO'] . "( " . $roster_id['DRIVER_MOBILE'] . " ) on $tripdate_time at $stop_name. Enter OTP $OTP, use GTAXIE App to track your cab";
                                    //$smsmsg="Your cab no ".$roster_id['VEHICLE_REG_NO']."( ".$roster_id['DRIVER_MOBILE']." ) will be arriving on $stop_name at $tripdate_time. Use OTP $OTP for Pickup, use TOPSA App to track your cab.";
                                    $CI->ShuttleCommonmdl->insert_sms($branch_id, 'TOPSCS', $emp_mobile, $smsmsg);

                                    //insert employee notification
                                    $empgcm = $row['MOBILE_GCM'];
                                    $appcat = $row['MOBILE_CATEGORY'];

                                    if (!is_null($empgcm)) {
                                        $data3 = array("branch_id" => $branch_id, 'empid_cabid' => $emp_id, 'fcm_id' => $empgcm, 'heading' => 'Cab Details',
                                            'message' => $smsmsg, 'category_value' => 3, 'app_category' => $appcat, 'created_at' => $cur_date_time);
                                        $CI->ShuttleCommonmdl->c_insert('notification', $data3);
                                    }

                                    if (!is_null($drivergcm)) {
                                        $data2 = array("branch_id" => $branch_id, 'empid_cabid' => $roster_id['CAB_ID'], 'fcm_id' => $drivergcm, 'heading' => 'New Club',
                                            'message' => $notify_msg, 'category_value' => 3, 'created_at' => $cur_date_time);
                                        $CI->ShuttleCommonmdl->c_insert('notification', $data2);
                                    }
                                }
                            }
                        }
                        //  }
                    }
                }
            } else {
                
            }
        } catch (Exception $e) {
            error_log('***' . 'shuttle booking Error: ' . $e->getMessage() . "***" . $e->getCode() . "***" . $e->getFile() . "***" . $e->getLine());
            return TRUE;
        }
        return $ret;
    }
    
    public function getcab_capacity($cab_id)
    {
        $seat_count=0;
        $CI = & get_instance();
        $where = "c.ACTIVE=1 and c.CAB_ID='$cab_id' and v.ACTIVE=1";
        $CI->db->select("vm.CAPACITY as seatcount,c.VEHICLE_ID as vehid,c.VENDOR_ID as vendorid,c.BRANCH_ID as branchid");
        $CI->db->from("cab as c");
        $CI->db->join('vehicles as v', 'v.VEHICLE_ID=c.VEHICLE_ID');
        $CI->db->join('vehicle_models as vm', 'vm.VEHICLE_MODEL_ID=v.VEHICLE_MODEL_ID');
        $CI->db->where($where);
        $query1 = $CI->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();           
        }
        
        return $row1;
    }
    public function getroute_order($routeorder_p,$routeorder_d,$route_id)
    {
        $CI = & get_instance();
        $where = "Route_ID='$route_id' and Route_Order between '$routeorder_p' and '$routeorder_d' and status=1";
        $CI->db->select("Route_Order");
        $CI->db->from("shuttle_route_stops");       
        $CI->db->where($where);
        $CI->db->order_by("Route_Order");
        $query1 = $CI->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->result_array();          
        }        
        return $row1;
    }

}
