<?php

set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
include(APPPATH . 'libraries/Shuttledao.php');
include(APPPATH . 'libraries/EncdecPaytm.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttlemdl
 *
 * <AUTHOR>
 */
class Shuttlemdl extends CI_Model {

    public function ssvaluecheck($mobileno, $ssid,$ct) {
        $name = "";
        $ssval = "";
        $ret = "";
        $dao = new Shuttledao();
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
        $row = $dao->c_selectrow('employees', $where);
        if ($row) {
            $name = $row['NAME'];
            $ssval = $row['SESSION_ID'];
            $privatekey = $row['PRIVATE_KEY'];
        }
        if($ct=="ip" && $ssid==$ssval)
        {
             $ret = 'true,';
        }
        else{
            $rsa = new Crypt_RSA();
            $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
            $decoded = base64_decode($ssid);
            $rsa->loadKey($privatekey);
            $decrypted = $rsa->decrypt($decoded);
            if ($decrypted == $ssval) {
                $ret = 'true,';
            } else {
                $ret = 'false';
            }
        }
        return $ret;
    }

    public function rsaencrypt($mobileno) {
        $rsa = new Crypt_RSA();
        extract($rsa->createKey());
        $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
        $element_array = str_ireplace('-----BEGIN PUBLIC KEY-----', '', $publickey);
        $elementrsaarray = trim(str_ireplace('-----END PUBLIC KEY-----', '', $element_array));

        $element_array1 = str_ireplace('-----BEGIN RSA PRIVATE KEY-----', '', $privatekey);
        $elementrsaarray1 = trim(str_ireplace('-----END RSA PRIVATE KEY-----', '', $element_array1));
        $data = array(
            'PUBLIC_KEY' => $elementrsaarray,
            'PRIVATE_KEY' => $elementrsaarray1
        );

        $dao = new Shuttledao();
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
        $where1 = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
        $cnt = $dao->c_update('employees', $data, $where1);
        if ($cnt > 0) {
            return $elementrsaarray;
        } else {
            return $element_array = 'False';
        }
    }

    public function shuttle_registraton($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];      
        $ENCRYP_PWD= $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
        $element_array = array();       
        $ENC_EMAIL=$dao->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "(EMAIL = '".$ENC_EMAIL."' || MOBILE = '".$ENC_MOBILE."') AND CATEGORY='Shuttle' AND ACTIVE=1";
        $row = $this->c_selectrow('employees', $where);
        if ($row) {
            $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
        } else {
            $eid = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $ENC_NAME=$dao->AES_ENCRYPT($name, AES_ENCRYPT_KEY);
            $data = array('BRANCH_ID' => 1, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
            $cnt = $this->c_insert('employees', $data);
            if ($cnt > 0) {
                $message="Thank you for registering with ".SMS_TITLE.".enjoy the ride!";
                $dao->insert_sms(1, 'TOPSCS', $mobile, $message);
                $data1 = array('branch_id' => 1, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                $this->c_insert('shuttle_notification', $data1);
                $element_array = array('status' => 1, 'Message' => 'Successfully registered');
            } else {
                $element_array = array('status' => 0, 'Message' => 'Registration failed');
            }
        }
        return $element_array;
    }

    public function shuttle_otpcreation($mobile, $password,$ct) {
        $smssts = "";
        $smsmsg = "";
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];

        $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);        
        $ENCRYP_PWD=$dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);        
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);       
        $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
        $row = $dao->c_selectrow('employees', $where);       
        if ($row) {
            $ename =$dao->AES_DECRYPT($row['NAME'],AES_ENCRYPT_KEY);
            $eid = $row['EMPLOYEES_ID'];
            //Thank you for registering with {V}. enjoy the ride!
            if($mobile=="9176797925" && $ct=="ip")
            {
                $otp="1111";
            }
            else
            {
                $otp=$otp;
            }
            
            $smsmsg = "Dear $ename, Please use $otp as login OTP in ".SMS_TITLE;            
            $smssts = $this->sendsms($mobile, $smsmsg);
            $data = array('MOBILE_NO' => $mobile, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0, 'SMS_RESPONSE' => $smssts,
                'OTP_CATEGORY' => 'ShuttleLogin', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime);
            $cnt = $dao->c_insert('otp_verification', $data);
            if ($cnt > 0) {
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
            }
        } else {
            $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
        }
        return $element_array;
    }

    public function shuttle_otpverified($mobile, $password, $otp, $deviceinfo, $deviceid, $fcmid,$appversion) {
        $sessionEncrypt = "";
        $encrypted = "";
        $officeaddr = "--";
        $officelat = 0;
        $officelong = 0;
        $element_array = array();

        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        //$ENCRYP_PWD = $this->Encrypt_Script($password, SECERT_KEY_NTL);
        $ENCRYP_PWD=$dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where1 = "OTP='$otp' and VERIFIED_STATUS='0' and MOBILE_NO='$mobile' and OTP_CATEGORY='ShuttleLogin' and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
        $value = array('VERIFIED_STATUS' => 1);
        $cnt = $dao->c_update('otp_verification', $value, $where1);
        if ($cnt > 0) {
            $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $dao->c_selectrow('employees', $where);
            //echo $this->db->last_query();
            if ($row) {
                $name =$dao->AES_DECRYPT($row['NAME'],AES_ENCRYPT_KEY);
                $gender = $row['GENDER'];
                $email = $dao->AES_DECRYPT($row['EMAIL'],AES_ENCRYPT_KEY);
                $address = $row['ADDRESS'];
                $lat = $row['LATITUDE'];
                $long = $row['LONGITUDE'];
                $category = $row['CATEGORY'];
                $bid = $row['BRANCH_ID'];
                $eid = $row['EMPLOYEES_ID'];
                $emergency_no = $row['EMERGENCY_CONTACT_NO'];
                if(!(is_null($emergency_no)))
                {
                    $emergency_no = $dao->AES_DECRYPT($emergency_no,AES_ENCRYPT_KEY);
                }
                else
                {
                   $emergency_no=0; 
                }
                //if ($category != "Shuttle") {
                    $where1 = "BRANCH_ID = '$bid ' AND ACTIVE = '1'";
                    $row1 = $dao->c_selectrow('branch', $where1);
                    if ($row1) {
                        $officeaddr = $row1['LOCATION'];
                        $officelat = $row1['LAT'];
                        $officelong = $row1['LONG'];
                    }
                //}
                $sessionid = rand(10000, 100000);
                $sql_encryt = $dao->AES_ENCRYPT($sessionid, AES_ENCRYPT_KEY);
                $data1 = array(
                    'SESSION_ID' => base64_encode($sql_encryt),
                    'MOBILE_GCM' => $fcmid,
                    'DEVICE_INFO' => $deviceinfo,
                    'DEVICE_ID' => $deviceid,
                    'APP_VERSION'=>$appversion
                );
                $this->c_update("employees", $data1, $where);
                $sessionEncrypt = base64_encode($sql_encryt);
                $encrypted = $this->rsaencrypt($mobile);        
               // echo "encrypt==".$encrypted;
              //  echo "emergency no==".$emergency_no;
                if ($encrypted != 'False') {
                    $element_array = array('status' => 1, 'Message' => 'success', 'branch_id' => $bid, 'employee_id' => $eid, 'mobile' => $mobile, 'emergency_contact_no' => $emergency_no, 'name' => $name, 'gender' => $gender, 'email' => $email,
                        'homeaddress' => $address, 'homelat' => $lat, 'homelong' => $long, 'officeaddress' => $officeaddr, 'officelat' => $officelat, 'officelong' => $officelong,
                        'category' => $category, 'sessionid' => $sessionEncrypt, 'publickey' => $encrypted, 'PAYTM_MID' => PAYTM_MID, 'PAYTM_WEBSITE' => PAYTM_WEBSITE_APP,
                        'PAYTM_INDUSTRY_TYPE_ID' => PAYTM_INDUSTRY_TYPE_ID, 'PAYTM_CHANNEL_ID_APP' => PAYTM_CHANNEL_ID_APP, 'PAYTM_CLIENT_ID' => PAYTM_CLIENT_ID, 'PAYTM_CLIENT_SECRET' => PAYTM_CLIENT_SECRET);
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                }
            } else {
                $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
            }
        } else {
            $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
        }
        return $element_array;
    }

    public function appversion($eid, $bid,$appversion) {
        $dao = new Shuttledao();
        $element_array = array();
        $property_array = array();
        $roster_id = 0;
        $roster_passenger_id = 0;
        $cab_id = 0;
        $feedback_roster_id = 0;
        $sloc = "";
        $eloc = "";
        $emploc = "";
        $trip_type = "";
        $tsloc = "";
        $teloc = "";
        $ttrip_type = "";
        $temploc = "";
        $cabno = "";
        $stime = "";
        $feedbackstime="";
        if($appversion!=0)
        {
            $where1 = "EMPLOYEES_ID = '" . $eid . "' AND BRANCH_ID = '$bid' and ACTIVE=1 and CATEGORY='Shuttle'";
            $data1 = array(                
                'APP_VERSION'=>$appversion
            );
            $this->c_update("employees", $data1, $where1);
        }

        $where = "ACTIVE=1 and PROPERTIE_CATEGORY='ShuttleApp' and BRANCH_ID='$bid'";
        $row = $dao->c_selectarray('properties', $where); // //check employee app property  
        foreach ($row as $val) {
            $property_array[] = array('property_name' => $val['PROPERTIE_NAME'], 'property_value' => $val['PROPERTIE_VALUE']);
        }
        $track = $dao->get_current_tracking_details($eid); //get traking details
        if ($track) {
            $roster_id = $track['ROSTER_ID'];
            $cab_id = $track['CAB_ID'];
            $tsloc = $track['START_LOCATION'];
            $teloc = $track['END_LOCATION'];
            $ttrip_type = $track['TRIP_TYPE'];
            $temploc = $track['LOCATION_NAME'];
            $cabno = $track['VEHICLE_REG_NO'];
            $stime = $track['ESTIMATE_START_TIME'];
            $roster_passenger_id = $track['ROSTER_PASSENGER_ID'];
            if($ttrip_type=="P")
            {
                $tsloc=$temploc;
                $teloc=$teloc;
            }
            else
            {
               $tsloc=$tsloc; 
               $teloc=$temploc;
            }
        }
        $feedback = $dao->get_current_feedback_details($eid); //get feedback deatils
        if ($feedback) {
            $sloc = $feedback['START_LOCATION'];
            $eloc = $feedback['END_LOCATION'];
            $trip_type = $feedback['TRIP_TYPE'];
            $emploc = $feedback['LOCATION_NAME'];
            $feedback_roster_id = $feedback['ROSTER_ID'];
            $feedbackstime=$feedback['ESTIMATE_START_TIME'];
        }
        $element_array = array('status' => 1, 'property_details' => $property_array, 'track_roster_id' => $roster_id, 'track_passenger_id' => $roster_passenger_id, 'track_cab_id' => $cab_id, 'track_sloc' => $tsloc, 'track_eloc' => $teloc, 'track_trip_type' => $ttrip_type, 'track_emp_loc' => $temploc, 'track_cab_no' => $cabno, 'track_start_time' => $stime, 'feedback_roster_id' => $feedback_roster_id, 'sloc' => $sloc, 'eloc' => $eloc, 'trip_type' => $trip_type, 'emp_loc' => $emploc,'trip_time'=>$feedbackstime);
        return $element_array; //return response to controller        
    }

    public function shuttle_search($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";

        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->getSearchGroup($startlat, $startlong, $branchid, 'D', $route_type);
            $droparray1 = $this->ShuttleElasticmdl->getSearchGroup($endlat, $endlong, $branchid, 'P', $route_type);
        }
        $pickarray = $this->ShuttleElasticmdl->getSearchGroup($startlat, $startlong, $branchid, 'P', $route_type);
        $droparray = $this->ShuttleElasticmdl->getSearchGroup($endlat, $endlong, $branchid, 'D', $route_type);
//        print_r($pickarray);
//        print_r($droparray);
//        print_r($pickarray1);
//        print_r($droparray1);
//        exit;
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();

        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->check_seat_availability($route_id, 0);
                    foreach ($seatavial as $row) {
                        $rosterid = $row['ROSTER_ID'];
                        $routedate = $row['ESTIMATE_START_TIME'];
                        $category = $row['category'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['stime'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);


                        $dist = $this->calkm($startlat . ',' . $startlong, $plat . ',' . $plong, $startlat . ',' . $startlong, 'km', 'walk');
                        $dist1 = $this->calkm($endlat . ',' . $endlong, $dlat . ',' . $dlong, $endlat . ',' . $endlong, 'km', 'walk');
                        $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));

                        $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, 0);
                        $traveltime = $traveldist[1];

                        if ($resheduledate == '0000-00-00') {
                            if (strtotime($resheduledate) == strtotime($curdate)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);
                            } else if (strtotime($resheduledate) > strtotime($curdate)) {
                                $tomorrow = date('Y-m-d', strtotime('-1 day', strtotime($curdate)));
                                $comparetime = $tomorrow . " " . $stime;
                                $shuttletime = $this->shuttle_timings($comparetime, 'Regular', $a_p_picktime);
                            } else {
                                $shuttletime = $this->shuttle_timings($comparetime, $category, $a_p_picktime);
                            }
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                        } else {
                            if (strtotime($stime) >= strtotime($curtime)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);
                                
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial1 = $dao->check_seat_availability($route_id, 0);
                    foreach ($seatavial1 as $row) {
                        $rosterid = $row['ROSTER_ID'];
                        $routedate = $row['ESTIMATE_START_TIME'];
                        $category = $row['category'];
                        $a_pick = $val['APROX_PICK'];
                        $destination = $val['DROP_POSITION'];
                        $origin = $val['PICK_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['stime'];
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $comparetime = $curdate . " " . $stime;

                        $dist1 = $this->calkm($startlat . ',' . $startlong, $dlat . ',' . $dlong, $startlat . ',' . $startlong, 'km', 'walk');
                        $dist = $this->calkm($endlat . ',' . $endlong, $plat . ',' . $plong, $endlat . ',' . $endlong, 'km', 'walk');
                        $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                        $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, 0);

                        $traveltime = $traveldist[1];
                        if ($resheduledate == '0000-00-00') {
                            if (strtotime($resheduledate) == strtotime($curdate)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);
                            } else if (strtotime($resheduledate) > strtotime($curdate)) {
                                $tomorrow = date('Y-m-d', strtotime('-1 day', strtotime($curdate)));
                                $comparetime = $tomorrow . " " . $stime;
                                $shuttletime = $this->shuttle_timings($comparetime, 'Regular', $a_p_picktime);
                            } else {
                                $shuttletime = $this->shuttle_timings($comparetime, $category, $a_p_picktime);
                            }
                            $element_arr1[] = array('routeno' => $val['ROUTE_ID'], 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                        } else {
                            if (strtotime($stime) >= strtotime($curtime)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);
                                
                                $element_arr1[] = array('routeno' => $val['ROUTE_ID'], 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                            }
                        }
                        
                    }
                }
            }
        }

        $p_array = $this->msort($element_arr, array('shuttle_time', 'route_date'));
        $d_array = $this->msort($element_arr1, array('shuttle_time', 'route_date'));
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }

    public function shuttle_timings($comparetime, $category, $a_p_picktime) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttlestr = "";
        if ($category == 'Regular') {
            if (strtotime($comparetime) > strtotime($curdatetime)) {
                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                    $shuttlestr = "CMonday ";
                } else {
                    $shuttlestr = "ANext";
                }
            } else {
                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                    $shuttlestr = "CMonday ";
                } else {
                    $shuttlestr = "BTomorrow";
                }
            }
        } else if ($category == 'Oneday') {
            if (strtotime($comparetime) > strtotime($curdatetime)) {
                $shuttlestr = "ANext";
            }
        } else {
            if (strtotime($comparetime) > strtotime($curdatetime)) {
                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                    $shuttlestr = "CMonday ";
                } else {
                    $shuttlestr = "ANext";
                }
            } else {
                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                    $shuttlestr = "CMonday ";
                } else {
                    $shuttlestr = "BTomorrow";
                }
            }
        }
        return $shuttlestr;
    }

    public function tariff_cal($dist, $ttype, $tariff_type,$vehicle_id, $noofdays) {
        if ($dist == 0) {
            $dist = 1;
        }
        $condition = "";
        if ($noofdays > 0) {
            $condition = "and NumberofDays='$noofdays'";
        }
        $oneday_fixed_price=0;
        // echo $dist, $ttype, $tariff_type,$noofdays;
        $element_array = array();
        if (strtolower($tariff_type) == strtolower("Km")) {

            $where = "Active ='1' and  '$dist'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and Tariff_Type='km' and VehicleId='$vehicle_id' $condition order by NumberofDays";
            $row = $this->c_selectarray('shuttle_tariff', $where);
           // echo $this->db->last_query();
            foreach ($row as $val) {
                $tariffId=$val['TariffId'];
                $validDays = $val['NumberofDays'];
                $validTrip = $val['NumberofTrips'];
                $baseKm = $val['Tariff_Base_Km'];
                $basCost = $val['Tariff_Base_Cost'];
                $extKmCost = $val['Tariff_Ext_Km_Cost'];
                $flatCost = $val['Tariff_Flat_Cost'];
                $percentage = $val['Percentage'];
                $oneday_fixed_price=$val['Oneday_Fixed_Cost'];
                $tariff_name=$val['Tariff_Name'];

                if ($flatCost == 0) {
                    $extKmAmt = 0;
                    $exkm = 0;
                    $fare = 0;
                    $percentAmt = 0;
                    $exkm = $dist - $baseKm;
                    if ($exkm > 0) {
                        $extKmAmt = $exkm * $extKmCost;
                    }
                    if ($percentage == 0) {
                        $fare = ($basCost + $extKmAmt) * $validTrip;
                    } else {
                        $percentAmt = ($basCost + $extKmAmt) * $percentage / 100;
                        $fare = ($basCost + $extKmAmt + $percentAmt) * $validTrip;
                    }
                } else {
                    if ($percentage == 0) {
                        $fare = $flatCost * $validTrip;
                    } else {
                        $percentAmt = ($flatCost) * $percentage / 100;
                        $fare = ($flatCost + $percentAmt) * $validTrip;
                    }
                }
                $element_array[] = array('tariff_id'=>$tariffId,'tariff_name'=>$tariff_name,'fare' => round($fare), 'validDays' => $validDays, 'validTrip' => $validTrip,'onedayFixedprice'=>$oneday_fixed_price);
            }
        } else {
            //and  '$dist'BETWEEN Tariff_St_Km AND Tariff_Cl_Km
            $where = "Active ='1' and Tariff_Type='Fixed' and VehicleId='$vehicle_id' $condition order by NumberofDays";
            $row = $this->c_selectarray('shuttle_tariff', $where);
            foreach ($row as $val) {
                 $tariffId=$val['TariffId'];
                $validDays = $val['NumberofDays'];
                $validTrip = $val['NumberofTrips'];
                $flatCost = $val['Tariff_Flat_Cost'];
                $tariff_name=$val['Tariff_Name'];
                $element_array[] = array('tariff_id'=>$tariffId,'tariff_name'=>$tariff_name,'fare' => ($flatCost * $validTrip), 'validDays' => $validDays, 'validTrip' => $validTrip,'onedayFixedprice'=>$oneday_fixed_price);
            }
        }
        return $element_array;
    }

    public function shuttle_routepath($routeid, $approxptime, $approxdtime, $ct) {
        $element_array = array();
        //$where = "RouteId='$routeid' and Duration between '$approxptime' and '$approxdtime' order by Sno";
        $where = "RouteId='$routeid' order by Sno";
        $row = $this->c_selectarray('shuttle_route_path', $where);
        if (count($row) > 0) {
            foreach ($row as $val) {
                $element_array[] = array('plat' => $val['Latitude'], 'plong' => $val['Longitude']);
            }
        }
        $element_arr = array('routepath' => $element_array);
        echo $this->output($element_arr, $ct);
    }

    public function shuttle_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $fare, $dist, $rosterid_p, $rosterid_d, $noofdays, $noofrides, $ptag, $dtag, $route_type, $subs_confirm_sts, $routeid_p, $routeid_d, $ssotoken, $cust_id, $mobileno) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
        $avail_p = false;
        $avail_d = false;

        if ($noofdays > 1) {
            $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            $enddate = date('Y-m-d', strtotime("+$noofdays day", strtotime($shuttledate)));
            $where = "EmployeeId='$eid' and '$shuttledate' between StartDate and EndDate and PaymentStatus='S'";
            $row = $dao->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if ($ptag == "Next") {
                if ($ttype == "B" && $dtag == "Tomorrow") {
                    $shuttledate = $curdate;
                    $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                } else {
                    $shuttledate = $curdate;
                    $enddate = $curdate;
                }
            } else if ($ptag == "Tomorrow") {
                $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            } else {
                $shuttledate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
            }
            $subscribtionsts = 0;
        }
        if ($routeid_p != 0) {
            $row1 = $dao->check_seat_availability($routeid_p, $rosterid_p);
            if ($row1) {
                $avail_p = true;
            }
        } else {
            $row1 = $dao->check_seat_availability($routeid_d, $rosterid_d);
            if ($row1) {
                $avail_d = true;
            }
        }

        if ($avail_p == true || $avail_d == true) {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {

                $origin = $plat . "," . $plong;
                $destination = $dlat . "," . $dlong;
                $tdist = $this->calkm($origin, $destination, $origin, 'km', 'driving');
                $tariff = $this->tariff_cal($tdist, $ttype, $route_type, $noofdays);
                $totdays = 0;
                $tfare = 0;
                $validtrip = 0;
                if (count($tariff) > 0 && $noofdays > 0) {
                    if ($ttype == "B") {
                        $totdays = $noofdays;
                        $tfare = $tariff[0]['fare'] * 2;
                        $validtrip = $tariff[0]['validTrip'] * 2;
                        $totdist = $tdist * 2;
                    } else {
                        $totdays = $noofdays;
                        $tfare = $tariff[0]['fare'];
                        $validtrip = $tariff[0]['validTrip'];
                        $totdist = $tdist;
                    }
                }
                $data = array('EmployeeId' => $eid, 'PickupRosterId' => $rosterid_p, 'DropRosterId' => $rosterid_d, 'PickupPoint' => $ppoint, 'DropPoint' => $dpoint, 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $tfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $shuttledate, 'EndDate' => $enddate, 'NoofDays' => $totdays, 'NoofRides' => $validtrip, 'TotalPaidAmt' => $tfare, 'RouteType' => $route_type);
                $cnt = $dao->c_insert('shuttle_booking', $data);
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $dao->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {
                        $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $tfare);
                       
                        // $element_array = array('status' => 1, 'message' => 'success', 'fare' => $row['TotalPaidAmt'], 'paymentid' => $payment_id, 'paymentno' => $paymentno, 'sdate' => $row['StartDate'], 'edate' => $row['EndDate'], 'noofdays' => $row['NoofDays'], 'noofrides' => $row['NoofRides']);
                        $element_array = array('status' => 1, 'message' => $response);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'subscribtion already avilable for this date');
            }
        } else {
            $element_array = array('status' => 3, 'message' => 'No seats available');
        }
        //echo json_encode($element_array);
        return $element_array;
    }

    public function paytmwalletmoney_withdraw($order_id, $ssotoken, $cust_id, $mobile_no, $fare,$stopid_p,$stopid_d) {       
        $encdec = new EncdecPaytm();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_time = $getdate['cur_date_time']; //have to check $ssotoken is null or not,$cust_id is null or not
        $checkSum = "";
        $paramList = array();
        $paramList["MID"] = PAYTM_MID;
        $paramList["OrderId"] = $order_id;
        $paramList["IndustryType"] = PAYTM_INDUSTRY_TYPE_ID;
        $paramList["Channel"] = PAYTM_CHANNEL_ID_WEB;
        $paramList["TxnAmount"] = $fare;
        $paramList["AuthMode"] = "USRPWD";
        $paramList["ReqType"] = "WITHDRAW";
        $paramList["SSOToken"] = $ssotoken;
        $paramList["AppIP"] = $_SERVER["REMOTE_ADDR"];
        $paramList["Currency"] = "INR";
        $paramList["DeviceId"] = $mobile_no;
        $paramList["PaymentMode"] = "PPI";
        $paramList["CustId"] = $cust_id;
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CheckSum"] = $checkSum;
        $data = array('OrderId' => $order_id, 'CustId' => $cust_id, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => PAYTM_CHANNEL_ID_WEB, 'TxnAmount' => $fare, 'RequestType' => 'WITHDRAW', 'SsoToken' => $ssotoken,
            'CheckSumHash' => $checkSum, 'Currency' => 'INR', 'DeviceId' => $mobile_no, 'PaytmCustomerId' => $cust_id, 'AppIp' => $_SERVER["REMOTE_ADDR"], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
        $cnt = $dao->c_insert('shuttle_payment_request', $data);
        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_SERVER_MONEY_WITHDRAW, $paramList);            
            if (isset($resparr['ResponseCode']) && isset($resparr['ResponseMessage']) && isset($resparr['Status'])) {
                $txnid = $resparr['TxnId'];
                $orderid = $resparr['OrderId'];
                $txnamt = $resparr['TxnAmount'];
                $respdata = array('OrderId' => $orderid, 'TxnId' => $txnid, 'TxnAmount' => $resparr['TxnAmount'], 'BankTxnId' => $resparr['BankTxnId'],
                    'ResponseCode' => $resparr['ResponseCode'], 'ResponseMessage' => $resparr['ResponseMessage'], 'Status' => $resparr['Status'], 'PaymentMode' => $resparr['PaymentMode'],
                    'BankName' => $resparr['BankName'], 'CheckSumHash' => $resparr['CheckSum'], 'PaytmCustomerId' => $resparr['CustId'], 'Mbid' => $resparr['MBID'], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
                if ($cnt > 0 && $resparr['Status'] == 'TXN_SUCCESS' && $resparr['ResponseMessage'] == 'Txn Successful.' && $resparr['ResponseCode'] == '01') {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'S','UpdatedDatetime'=>$cur_time);
                    $dao->c_update('shuttle_booking', $data1, $where); 
                    $response = $this->paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no,$stopid_p,$stopid_d);                     
                } else {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'F','UpdatedDatetime'=>$cur_time);
                    $dao->c_update('shuttle_booking', $data1, $where);
                    $response = "Payment Failed";
                }
            } else {
                $respdata = array('OrderId' => $order_id, 'ResponseMessage' => $resparr['Error'],'CreatedTime' => $cur_time);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
                $response = "Payment Failed";
                //change
//                 $where = "PaymentNo='$order_id'";
//                    $data1 = array('PaymentStatus' => 'S','UpdatedDatetime'=>$cur_time);
//                    $dao->c_update('shuttle_booking', $data1, $where);
//                $response = $this->paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no,$stopid_p,$stopid_d);
                //
            }
        }
        return $response;
    }

    public function paytmwithdraw_response($ORDERID, $txnid, $txnamt, $mobile_no,$stopid_p,$stopid_d) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];
        $empid = "";
        $stime = "";
        $etime = "";
        $ppoint = "";
        $dpoint = "";
        $tmode = "";
        $pamt = 0;
        $route_id = 0;
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobile_no, AES_ENCRYPT_KEY);
        $row = $dao->getshuttle_bookingdetails($ENC_MOBILE, $ORDERID);       
        if ($row) {
            $stime = $row['StartTime'];
            $etime = $row['EndTime'];
            $ppoint = $row['PickupPoint'];
            $dpoint = $row['DropPoint'];
            $tmode = $row['TravelMode'];
            $pamt = $row['TotalPaidAmt'];
            $dist = $row['PackageKm'];
            $tripid_p = $row['PickupRosterId'];
            $tripid_d = $row['DropRosterId'];
            $empid = $row['EmployeeId'];
            $sdate = $row['StartDate'];
            $enddate = $row['EndDate'];
            $duration = $row['NoofDays'];
            $emp_id = $row['EmployeeId'];
            $noofrides = $row['NoofRides'];
            $ename = $dao->AES_DECRYPT($row['NAME'],AES_ENCRYPT_KEY);

            $msg="";
            if($duration==1)
            {
                $msg=" 1 day";
            }
            else
            {
                 $msg=$duration." days ". $noofrides." rides";
            }
            $smsmessage = "Dear $ename, Thanks for your subscriptions $msg plan.For any queries contact 04440003002, enjoy the ride!";
            $dao->insert_sms(1, 'TOPSCS', $mobile_no, $smsmessage);
               $date_to = $enddate;
            $date_from = strtotime($sdate);
            $date_to = strtotime($enddate);
            $message = $this->seats_confirmed_sts($tmode, $tripid_p, $tripid_d);
            if ($sdate >= $curdate && $enddate >= $curdate && $message == 'Confirmed') {

                for ($i = $date_from; $i <= $date_to; $i+=86400) {
                    if (date("w", strtotime(date("Y-m-d", $i))) == 6 || date("w", strtotime(date("Y-m-d", $i))) == 0) {
                        
                    } else {
                        if ($tmode == "B") {
                            $trip_fare = $pamt / 2;
                            
                            $seat_cost = $trip_fare / $duration;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id=$row['Vehicle_ID'];
                                $route_name=$row['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist/2),$vehicle_id,'P',$dpoint,$route_name);
                            }
                            $row1 = $dao->get_routeid_sheduletime($tripid_d);
                            if ($row1) {
                                $route_id = $row1['Schedule_Route_ID'];
                                $schedule_time = $row1['Schedule_Time'];
                                $vehicle_id=$row1['Vehicle_ID'];
                                $route_name=$row1['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_d, $ORDERID, $emp_id, round($dist/2),$vehicle_id,'D',$dpoint,$route_name);
                            }
                        }
                        else{
                            $seat_cost = $pamt / $duration;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id=$row['Vehicle_ID'];
                                $route_name=$row['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_p, $ORDERID, $emp_id, $dist,$vehicle_id,'P',$dpoint,$route_name);
                            }
                        }
//                        else {
//                            $seat_cost = $pamt / $duration;
//                            $row = $dao->get_routeid_sheduletime($tripid_d);
//                            if ($row) {
//                                $route_id = $row['Schedule_Route_ID'];
//                                $schedule_time = $row['Schedule_Time'];
//                                $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_d, $ORDERID, $emp_id, $dist);
//                            }
//                        }
                    }
                }
                // $this->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                $response_message = "Payment Success";
            } else {
                $this->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                $response_message = "No seats avilable.Your amount has been refunded";
            }
        }
        return $response_message;
    }

    public function paytmaddmoney_request($orderid, $custid, $channelid, $txnamount, $website, $reqtype, $ssotoken, $mobileno) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $data = array('OrderId' => $orderid, 'CustId' => $custid, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => $channelid, 'WebSite' => $website, 'TxnAmount' => $txnamount, 'RequestType' => $reqtype, 'SsoToken' => $ssotoken, 'CreatedBy' => $mobileno, 'CreatedTime' => $curdate_time);
        $dao->c_insert('shuttle_payment_request', $data);
    }

    public function paytmaddmoney_response($ORDERID, $TXNID, $BANKTXNID, $TXNAMOUNT, $GATEWAYNAME, $RESPCODE, $RESPMSG, $BANKNAME, $PAYMENTMODE, $TXNDATE, $STATUS, $checkcumvalid, $mobileno) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $data = array('OrderId' => $ORDERID, 'TxnId' => $TXNID, 'BankTxnId' => $BANKTXNID, 'BankName' => $BANKNAME, 'GateWay' => $GATEWAYNAME, 'TxnAmount' => $TXNAMOUNT, 'TxnType' => $TXNTYPE, 'ResponseCode' => $RESPCODE, 'ResponseMessage' => $RESPMSG,
            'PaymentMode' => $PAYMENTMODE, 'TxnDate' => $TXNDATE, 'Status' => $STATUS, 'RefundAmount' => $REFUNDAMT, 'CreatedBy' => $mobileno, 'CreatedTime' => $curdate_time, 'CheckSumValid' => $checkcumvalid);
        $dao->c_insert('shuttle_payment_response', $data);
    }

    public function paytmwalletmoney_refund($order_id, $txnid, $refundamt, $mobile_no) {
        //{"MID":"NTLIND32103984777778","ORDERID":"NTL37523","REFUNDAMOUNT":"1","TXNID":"********","TXNTYPE":"REFUND","REFID":"36581RFIDNTL","CHECKSUM":"3Mv3pNrMhdXEaUYveoVzwzOiDhQsXxEkkKPZi3tfJRff4FpTPPUiE26W1MNGEC0x+mF+QnNKjM13+eiyc0n/iLHxBscfNxOWzTemHDXHKvM="}
        //{"MID":"NTLIND24898271393596","ORDERID":"************","REFUNDAMOUNT":"1.00","TXNID":**********,"TXNTYPE":"REFUND","REFID":"36648RFIDNTL","CHECKSUM":"y4kepWEt2wfXyyN2VyvuiWuR5cL5IQTR2h6QaEaHiV\/RbVmIZkb\/B2x9A2I82dNcwi1c4rrbG06nmkMaVcQjiA+zGyDIwPHG\/6DgHznF62g="}
        $dao = new Shuttledao();
        $encdec = new EncdecPaytm();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
      
        $checkSum = "";
        $paramList = array();
        $paramList["MID"] = PAYTM_MID;
        $paramList["ORDERID"] = $order_id;
        $paramList["REFUNDAMOUNT"] = $refundamt;
        $paramList["TXNID"] = $txnid;
        $paramList["TXNTYPE"] = 'REFUND';
        $paramList["REFID"] = rand(10000, 100000) . "RFIDNTL";
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CHECKSUM"] = $checkSum;
        $data = array('OrderId' => $order_id, 'TxnId' => $txnid, 'RefundAmount' => $refundamt, 'RequestType' => 'REFUND', 'RefId' => $paramList["REFID"],
            'CheckSumHash' => $checkSum, 'CreatedBy' => $mobile_no, 'CreatedTime' => $curdate_time);
        // echo json_encode($paramList);
        $cnt = $dao->c_insert('shuttle_payment_request', $data);

        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_REFUND_URL, $paramList);
            // print_r($resparr);
            if (isset($resparr['RESPCODE']) && isset($resparr['RESPMSG'])) {
                $respdata = array('OrderId' => $resparr['ORDERID'], 'TxnId' => $resparr['TXNID'], 'TxnAmount' => $resparr['TXNAMOUNT'], 'RefundAmount' => $resparr['REFUNDAMOUNT'],
                    'ResponseCode' => $resparr['RESPCODE'], 'ResponseMessage' => $resparr['RESPMSG'], 'Status' => $resparr['STATUS'], 'RefundId' => $resparr['REFUNDID'],
                    'RefId' => $resparr['REFID'], 'GateWay' => $resparr['GATEWAY'], 'CardIssuer' => $resparr['CARD_ISSUER'],'CreatedTime' => $curdate_time,'CreatedBy' => $mobile_no,);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata); 
                if ($cnt > 0 && $resparr['RESPCODE'] == '10' && $resparr['RESPMSG'] == 'Refund Successful.') {
                $where = "PaymentNo='$order_id'";
                $this->db->set("RefundAmt", "if(RefundAmt is null,".$resparr['REFUNDAMOUNT'].",RefundAmt+".$resparr['REFUNDAMOUNT'].")", FALSE);
                $this->db->set("RefundReferanceId", "if(RefundReferanceId is null,'".$resparr['REFUNDID']."',CONCAT(RefundReferanceId,',','".$resparr['REFUNDID']."'))", FALSE);
                $this->db->set("Active", "if(NoofRides-NoofCancelRides=1,3,2)",FALSE);
                $this->db->set("NoofCancelRides", "NoofCancelRides+1", FALSE);
                $this->db->set("UpdatedDatetime", $curdate_time);
                $this->db->where($where);
                $this->db->update('shuttle_booking'); 
                //echo $this->db->last_query();
                }
            } else {
                $respdata = array('OrderId' => $order_id, 'ResponseCode' => $resparr['ErrorCode'], 'ResponseMessage' => $resparr['ErrorMsg'],'CreatedTime' => $curdate_time,'CreatedBy' => $mobile_no,);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
            }
        }
    }

    public function shuttle_payment($MID, $ORDERID, $TXNID, $BANKTXNID, $TXNAMOUNT, $TXNTYPE, $GATEWAYNAME, $RESPCODE, $RESPMSG, $BANKNAME, $PAYMENTMODE, $REFUNDAMT, $TXNDATE, $STATUS, $payment_id) {
        $response_message = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_time'];
        $curdate_time = $getdate['cur_date_time'];
        $avail_p = false;
        $avail_d = false;

        $where = "ORDERID='$ORDERID' and MID='$MID' and SNO='$payment_id'";
        $data = array('TXNID' => $TXNID, 'BANKTXNID' => $BANKTXNID, 'TXNAMOUNT' => $TXNAMOUNT, 'TXNTYPE' => $TXNTYPE, 'GATEWAYNAME' => $GATEWAYNAME,
            'RESPCODE' => $RESPCODE, 'RESMSG' => $RESPMSG, 'BANKNAME' => $BANKNAME, 'PAYMENTMODE' => $PAYMENTMODE, 'TXNDATE' => $TXNDATE, 'REFUNDAMT' => $REFUNDAMT, 'STATUS' => $STATUS, 'RESPONSED_TIME' => $curdate_time);
        $cnt = $dao->c_update('shuttle_payment_gateway', $data, $where);
        if ($cnt > 0) {
            if ($STATUS == "TXN_SUCCESS") {
                // if ($STATUS == "TXN_SUCCESS" && $TXNID!='' && $BANKTXNID!='' && $GATEWAYNAME!='' && $TXNDATE!='1970-01-01' && $RESPMSG=="Txn Successful." && $RESPCODE=="01") {                

                $empid = "";
                $stime = "";
                $etime = "";
                $ppoint = "";
                $dpoint = "";
                $tmode = "";
                $pamt = 0;
                $route_id = 0;
                $where = "PaymentNo='$ORDERID'";
                $row = $dao->c_selectrow('shuttle_booking', $where);
                if ($row) {
                    $Sno = $row['Sno'];
                    $stime = $row['StartTime'];
                    $etime = $row['EndTime'];
                    $ppoint = $row['PickupPoint'];
                    $dpoint = $row['DropPoint'];
                    $tmode = $row['TravelMode'];
                    $pamt = $row['TotalPaidAmt'];
                    $dist = $row['PackageKm'];
                    $rosterid_p = $row['PickupRosterId'];
                    $rosterid_d = $row['DropRosterId'];
                    $empid = $row['EmployeeId'];
                    $sdate = $row['StartDate'];
                    $enddate = $row['EndDate'];
                    $duration = $row['NoofDays'];
                    // $roster_id = 0;
                    if ($rosterid_p == 0) {
                        $roster_id = $rosterid_d;
                    } else {
                        $roster_id = $rosterid_p;
                    }
                    $row = $dao->check_seat_availability(0, $roster_id);
                    if ($row) {
                        $date_from = $sdate;
                        $date_from = strtotime($date_from);
                        if ($pamt == $TXNAMOUNT) {
                            $where1 = "PaymentNo='$ORDERID'";
                            $data1 = array('PaymentStatus' => 'S'
                            );
                            $ucnt = $dao->c_update('shuttle_booking', $data1, $where1);
                            if ($ucnt > 0) {
                                if ($duration == 1) {
                                    if ($tmode == "P") {
                                        $this->insert_roster_passenger($empid, $rosterid_p, $dist, $sdate . " " . $stime);
                                    } else if ($tmode == "R") {
                                        $this->insert_roster_passenger($empid, $rosterid_d, $dist, $sdate . " " . $stime);
                                    } else {
                                        $this->insert_roster_passenger($empid, $rosterid_p, $dist, $sdate . " " . $stime);
                                        $this->insert_roster_passenger($empid, $rosterid_d, $dist, $enddate . " " . $etime);
                                    }
                                }
                                $date_to = $enddate;
                                $date_to = strtotime($date_to);
                                $inc = 0;
                                if ($sdate >= $curdate && $enddate >= $curdate) {
                                    for ($i = $date_from; $i <= $date_to; $i+=86400) {
                                        if (date("w", strtotime(date("Y-m-d", $i))) == 6 || date("w", strtotime(date("Y-m-d", $i))) == 0) {
                                            
                                        } else {
                                            if ($tmode == "B") {
                                                for ($j = 0; $j < 2; $j++) {
                                                    if ($j == 0) {
                                                        $stime = $row['StartTime'];
                                                        $etime = "00:00:00";
                                                        $roster_id = $rosterid_p;
                                                    }
                                                    if ($j == 1) {
                                                        $stime = $row['EndTime'];
                                                        $etime = "00:00:00";
                                                        $roster_id = $rosterid_d;
                                                    }
                                                    $data = array('BookingMasterId' => $Sno, 'StartTime' => $stime, 'EndTime' => $etime, 'CreatedDatetime' => $cur_time,
                                                        'TravelDate' => date("Y-m-d", $i), 'RosterId' => $roster_id);
                                                    $dao->c_insert('shuttle_booking_trans', $data);
                                                    if ($inc == 0) {
                                                        $row1 = $dao->get_roster_details($roster_id, date("Y-m-d", $i));
                                                        $dao->update_passenger_count($row1['ROSTER_ID'], 'add');
                                                    }
                                                    //update passenger count here
                                                }
                                            } else {
                                                if ($rosterid_p != 0) {
                                                    $roster_id = $rosterid_p;
                                                } else {
                                                    $roster_id = $rosterid_d;
                                                }
                                                $data = array('BookingMasterId' => $Sno, 'StartTime' => $stime, 'EndTime' => $etime, 'CreatedDatetime' => $cur_time,
                                                    'TravelDate' => date("Y-m-d", $i), 'RosterId' => $roster_id);
                                                $dao->c_insert('shuttle_booking_trans', $data);
                                                //echo $this->db->last_query();
                                                //update passenger count here
                                                if ($inc == 0) {
                                                    $row1 = $dao->get_roster_details($roster_id, date("Y-m-d", $i));
                                                    $dao->update_passenger_count($row1['ROSTER_ID'], 'add');
                                                }
                                            }
                                        }
                                        $inc++;
                                    }
                                }
                            }
                            $response_message = array('status' => 1);
                        } else {
                            $response_message = array('status' => 0);
                        }
                    } else {
                        $response_message = array('status' => 1, 'message' => "payment return");
                    }
                } else {
                    $response_message = array('status' => 0);
                }
            } else {
                $where = "PaymentNo='$ORDERID'";
                $data1 = array('PaymentStatus' => 'F');
                $dao->c_update('shuttle_booking', $data1, $where);
                $response_message = array('status' => 1);
            }
        } else {
            $where = "PaymentNo='$ORDERID'";
            $data1 = array('PaymentStatus' => 'F');
            $dao->c_update('shuttle_booking', $data1, $where);
            $response_message = array('status' => 1);
        }
        return $response_message;
    }

    public function insert_roster_passenger($eid, $rosterid, $dist, $tripdate_time) {
        // $enddate = $sdate;  
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
        $data = array('EMPLOYEE_ID' => $eid, 'ROSTER_ID' => $rosterid, 'ESTIMATE_START_TIME' => $tripdate_time, 'ACTIVE' => 1, 'ROUTE_ORDER' => $dist,
            'CREATED_BY' => $eid, 'CREATED_DATE' => $curdate_time); //have to discuss otp_verification 
        $rpcnt = $dao->c_insert('roster_passengers', $data);
        $rpid = $this->db->insert_id();
        if ($rpcnt > 0) {
            $cnt = $dao->update_passenger_count($rosterid, 'add');
            $row = $dao->get_employee_details($eid);
            $data1 = array("MOBILE_NO" => $row['MOBILE'], 'ROSTER_PASSENGER_ID' => $rpid, 'OTP' => $OTP, 'VERIFIED_STATUS' => 0, 'OTP_CATEGORY' => 'Pickup', 'CREATED_DATE' => $curdate_time);
            $dao->c_insert('otp_verification', $data1);

            $tdate = date('Y-m-d', strtotime($tripdate_time));
            $ttime = date('H:i:s', strtotime($tripdate_time));
           // $message = "Greetings from CTS Transport,Your Pickup on $tdate at $ttime hrs Log in.for any help contact:9176797925";
            $data2 = array('BRANCH_ID' => $row['BRANCH_ID'], "ORIGINATOR" => 'CTSCKC', 'RECIPIENT' => $row['MOBILE'], 'MESSAGE' => $message, 'STATUS' => 'U', 'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdate_time);
            $dao->c_insert('sms', $data2);
        }
    }

    public function profile_update($emergencyno, $homeaddr, $homelat, $homelong, $eid, $officeaddr, $officelat, $officelong,$branch_id) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array = array();
        $data4 = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];

        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' =>$dao->AES_ENCRYPT($emergencyno, AES_ENCRYPT_KEY));
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        $data4 = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time);
        $data5 = array_merge($data2, $data3, $data4);
        $where = "EMPLOYEES_ID='$eid' and ACTIVE='1' and CATEGORY='Shuttle' and BRANCH_ID='$branch_id'";
        $cnt = $dao->c_update('employees', $data5, $where);
        //echo $this->db->last_query();
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 1, 'Message' => "Please try again");
        }
        return $element_array;
    }

    public function shuttleimage_upload($img) {
        $UPLOAD_DIR = "../shuttle_profile/";
        $data = base64_decode($img);
        $file = $UPLOAD_DIR . uniqid() . '.png';
        file_put_contents($file, $data);
        return $file;
    }

    public function forgot_password($mobile, $otpnew) {
        $smsmsg = "";
        $smssts = "";
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        if ($otpnew == "") {
            $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $ENC_MOBILE=$dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "MOBILE = '".$ENC_MOBILE."' AND ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $dao->c_selectrow('employees', $where);
            if ($row) {
                $ename = $dao->AES_DECRYPT($row['NAME'],AES_ENCRYPT_KEY);
                $eid = $row['EMPLOYEES_ID'];
                $smsmsg = "Dear $ename, Please use $otp as login OTP in ".SMS_TITLE;  
                $smssts = $this->sendsms($mobile, $smsmsg);
                $data1 = array('MOBILE_NO' => $mobile, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0,
                    'SMS_RESPONSE' => $smssts, 'OTP_CATEGORY' => 'Forgotpw', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime
                );
                $cnt = $dao->c_insert('otp_verification', $data1);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Please try again");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "Mobile no not registered");
            }
        } else {
            $where = "MOBILE_NO = '$mobile' and OTP='$otpnew' and OTP_CATEGORY='Forgotpw' and VERIFIED_STATUS=0 and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
            $data = array('VERIFIED_STATUS' => 1);
            $cnt = $dao->c_update('otp_verification', $data, $where);
            if ($cnt > 0) {
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid OTP");
            }
        }
        return $element_array;
    }

    public function generate_newpassword($mobile, $newpassword) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
       // $ENCRYP_PWD = $this->Encrypt_Script($newpassword, SECERT_KEY_NTL);
        $ENCRYP_PWD=$dao->AES_ENCRYPT($newpassword, AES_ENCRYPT_KEY);
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
        $data1 = array(
            'password' => $ENCRYP_PWD,
            'updated_at' => $curdatetime
        );
        $cnt = $dao->c_update('employees', $data1, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Invalid OTP");
        }
        return $element_array;
    }

    public function change_password($oldpass, $newpass, $mobile) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
      // echo "pw==". $ENCRYP_PWD=  $dao->AES_ENCRYPT($oldpass, AES_ENCRYPT_KEY);
        $ENCRYP_PWD_NEW=$dao->AES_ENCRYPT($newpass, AES_ENCRYPT_KEY);       
        $ENCRYP_PWD=$dao->AES_ENCRYPT($oldpass, AES_ENCRYPT_KEY);
        $ENC_MOBILE=$dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";        
        $data = array(
            'password' => $ENCRYP_PWD_NEW,
            'updated_at' => $curdatetime
        );             
        $cnt = $dao->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Your old password is wrong");
        }
        return $element_array;
    }

    public function payment_history($eid,$bid) {
        $element_arr = array();
        $dao = new Shuttledao();
        $psts = "Failed";
        $respdate = "";        
        $row1 = $dao->payment_history($eid,$bid);
        foreach ($row1 as $val) {
            $respcode = $val['ResponseCode'];
            $respmsg = $val['ResponseMessage'];
            $rsts = $val['Status'];
            $respdate = $val['CreatedTime'];            
            $payment_no=$val['PaymentNo'];
            $paidamt=$val['TotalPaidAmt'];        
            if ($respcode == "01" && $respmsg == "Txn Successful." && $rsts == "TXN_SUCCESS") {
                $psts = "Paid";                        
            } else if ($respcode == "10" && $respmsg == "Refund Successful." && $rsts == "TXN_SUCCESS") {
                $psts = "Refunded";
                $paidamt=$val['RefundAmt'];
                $payment_no=$val['RefundReferanceId'];
            } else {
                $psts = "Failed";
            }
            $element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
        }
        $element_array = array('payment_history' => $element_arr);
        return $element_array;
    }

    public function booking_details($eid,$bid) {
        $element_arr = array();        
        $startDate="";
        $endDate="";
        $working_days=0;
        $noof_rides=0;
        $used_rides=0;
        $noshow_ride=0;
        $cancel_ride=0;
        $addl_rides=0;
        $dao = new Shuttledao();
        $row1 = $dao->subscription_details($eid,$bid);          
        foreach ($row1 as $val) {  
                $bal_ride=0;
                $startDate=$val['StartDate'];
                $endDate=$val['EndDate'];
                $working_days=$this->getWorkingDays($startDate, $endDate);
                $noof_rides = $val['NoofRides'];
                $used_rides = $val['NoofRidesUsed'];
                $noshow_ride=$val['NoShowRides'];
                $cancel_ride=$val['NoofCancelRides'];
                $addl_rides=$working_days-$noof_rides;              
                if($cancel_ride >= $addl_rides)
                {
                    $bal_ride = $noof_rides - ($used_rides+$noshow_ride+($cancel_ride-$addl_rides));
                }
                else
                {
                    $bal_ride = $noof_rides - ($used_rides+$noshow_ride);
                } 
            $element_arr[] = array('Sno' => $val['PaymentNo'], 'TotalAmount' => $val['TotalPaidAmt'], 'NoofDays' => $val['NoofDays'], 'NoofRides' => $noof_rides, 'NoofRidesUsed' => $used_rides, 'ExpiryDate' => $endDate, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'PlanCategory' => $val['Tariff_Name'], 'RidesRemaining' => $bal_ride);
        }       
        $element_array = array('booking_details' => $element_arr);
        return $element_array;
    }

    public function booking_list($eid,$bid) {
        $element_arr = array();
        $element_arr1 = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time=$getdate['cur_date_time'];
        $curdate=$getdate['cur_date'];
       
        $noofdays = 0;
        $reshedulests = "false";
        $cancelsts="false";
        $spoint = "";
        $dpoint = "";
        $stime = "";
        $etime = "";
        $payment_no="";
        $n_payment_no="";
        $startDate="";
        $endDate="";
        $working_days=0;
        $cancel_ride=0;
        $noofrides=0;
        $noofridesused=0;
        $noshowrides=0;
        $addl_rides=0;

        $where="EmployeeId='$eid' and BranchId='$bid' and PaymentStatus='S'";        
        $row1=$dao->c_selectarray('shuttle_booking', $where);       
        foreach ($row1 as $val) {      
            $rosterid = 0;
            $cabid = 0;  
             $i=0;
            $payment_no=$val['PaymentNo'];           
            $noofrides= $val['NoofRides'];            
            $noofridesused = $val['NoofRidesUsed'];
            $noshowrides = $val['NoShowRides'];
            $cancel_ride=$val['NoofCancelRides'];
            $spoint = $val['PickupPoint'];
            $dpoint = $val['DropPoint'];
            $plat = $val['PickLatitude'];
            $plong = $val['PickLongitude'];
            $routetype = $val['RouteType'];
            $dlat = $val['DropLatitude'];
            $dlong = $val['DropLongitude'];
            $noofdays = $val['NoofDays'];
            $startDate=$val['StartDate'];
            $endDate=$val['EndDate'];
            if($noofdays > 1 && $cancel_ride > 0){
                $working_days=$this->getWorkingDays($startDate, $endDate);   
                $addl_rides=$working_days-$noofrides;
                if($cancel_ride < $addl_rides)
                {
                    $noofrides=$noofrides+$cancel_ride;
                }
                else
                {
                    $noofrides=$noofrides+$addl_rides;
                }
            }
            
            $row2=$dao->booking_list_dao($payment_no);
            foreach ($row2 as $val1) { 
                
            $traveldate = $val1['Schedule_Date'];
            $activests = $val1['status'];
            $tripid = $val1['Trip_ID'];
            $tripseatid = $val1['Trip_Seat_ID'];           
            $seat_status=$val1['Seat_status'];
            $route=$val1['Schedule_Route_ID'];
            $trip_id=$val1['Trip_ID'];
            $travel_type=$val1['Travel_Type'];
            $stime=$val1['Stop_Time'];
           
            if($i<$noofrides)
            {
                if ($activests == 2 && $seat_status==1) {
                    $route_id = "S" . $tripid;  
                    $result = $dao->getroster_details($eid, $route_id, $traveldate);
                    if ($result) { 
                        $rosterid = $result['ROSTER_ID'];
                        $rpsts = $result['ROSTER_PASSENGER_STATUS'];
                        $rsts = $result['tracksts'];
                        if (in_array($rpsts, unserialize(RP_PICKUP_DROP_NOSHOW))) {
                            $tripsts = "Cancelled";
                        } else {
                            if (in_array($rsts, unserialize(R_CAB_TRACKING))) {
                                $tripsts = "Upcoming";
                                $rosterid = $result['ROSTER_ID'];
                                $cabid = $result['CAB_ID'];
                            } else if (in_array($rsts, unserialize(R_TRIP_CLOSE_HISTORY))) {
                                $tripsts = "Completed";
                            } else {
                                $tripsts = "Upcoming";
                            }
                        }
                        
                    } else {
                        $tripsts = "Cancelled";
                    }
                }
                else
                {
                    if(strtotime($traveldate) <  strtotime($curdate) && ($seat_status==2 || $seat_status==1))
                    {
                        $tripsts="Completed";
                    }
                    else{
                        if($seat_status==0)
                        {
                            $tripsts="Noshow";//Noshow
                        }
                        else if($seat_status==3)
                        {
                            $tripsts="Cancelled";
                        }
                        else if($seat_status==1)
                        {
                            $tripsts="Upcoming";
                        }
//                        else{
//                            $tripsts = "Upcoming";
//                        }
                    }
                }            
                //&& $i <=$balride
                if ($tripsts == "Upcoming" ) {
                    //enable cancel and reschedule discuss with md
                    $traveldate_time=$traveldate." ".$stime;
                    $seconds = strtotime($traveldate_time) - strtotime($curdate_time);
                    $days = floor($seconds / 86400);
                    $hours = floor(($seconds - ($days * 86400)) / 3600);
                    $minutes = floor(($seconds - ($days * 86400) - ($hours * 3600)) / 60); 
                    if($noofdays == 1 && $days >= 1)
                    {
                        $cancelsts = "true";
                        $reshedulests='false';
                    }
                    //else if($noofdays == 1 && $days == 0 && $hours >=0 && $minutes >=5)
                    else if($noofdays == 1 && $days == 0 && $hours >=1)
                    {
                        $cancelsts = "true";
                        $reshedulests='false';
                    }
                    else if(($noofdays > 1 && $hours >= 6) || ($days >= 1))
                    {
                        if($days<=2)//reshedule limit
                        {
                            $reshedulests='true';
                        }
                        else
                        {
                            $reshedulests='false';
                        }
                        $cancelsts = "true";
                    }
                    else
                    {
                        $cancelsts = "false";
                        $reshedulests='false';
                    }
                    $element_arr[] = array('Sno'=>$i,'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $cancelsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype,'TravelType'=>$travel_type,'RouteNo'=>$route,'TripId'=>$trip_id);
                } else {
                    $element_arr1[] = array('Sno'=>$i,'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $tripsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype,'TravelType'=>$travel_type,'RouteNo'=>$route,'TripId'=>$trip_id);
                }
                }
                $i++;
            }
             
        }
        $c_array = $this->msort('desc',$element_arr1, array('TravelDate','StartTime'));
        $element_array = array('booking_list' => $element_arr, 'completed_list' => $c_array);
        return $element_array;
    }

    public function booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category) {
        $element_array = array();
        $branchid = 1;
        $days = 0;
        $hours = 0;
        $ret = 0;
        $canceldate_time = $traveldate . " " . $stime;
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        if ($noofdays == 1) {
            if (strtotime($canceldate_time) >= strtotime($curdate_time)) {
                $seconds = strtotime($canceldate_time) - strtotime($curdate_time);
                $days = floor($seconds / 86400);
                $hours = floor(($seconds - ($days * 86400)) / 3600);
                $minutes = floor(($seconds - ($days * 86400) - ($hours * 3600)) / 60); 
                if ($days >= 1) {
                    $hours=22;    
                    $ret = $this->property_check('CANCELLATION POLICY ONEDAY', $branchid, $hours);
                    $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_ONDAY, $category);
                } else {
                    if($minutes >=1 && $hours==1)
                    {
                       $hours=2; 
                    }else{
                        $hours=$hours; 
                    }                    
                    $ret = $this->property_check('CANCELLATION POLICY ONEDAY', $branchid, $hours);
                    $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_ONDAY, $category);
                }
            } else {
                $element_array = array('status' => 'false', 'message' => 'Invalid Date');
            }
        } else {
            $seconds = strtotime($canceldate_time) - strtotime($curdate_time);
            $days = floor($seconds / 86400);
            $hours = floor(($seconds - ($days * 86400)) / 3600);            
            if($days>=1)
            {
                $hours=22;
            }
            else
            {
               $hours=$hours; 
            }
            $ret = $this->property_check('CANCELLATION POLICY REGULAR', $branchid, $hours);
            $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_REGULAR, $category);
        }
        return $element_array;
    }

    private function property_check($property_name, $branch_id, $hours) {
        $ret = 0;
        $dao = new Shuttledao();
        //get current date time
        $getdate = $dao->get_datetime();
        $intime = $getdate['cur_time'];
        $where = "PROPERTIE_NAME='$property_name' and ACTIVE='1' and BRANCH_ID='$branch_id'";
        $row = $dao->c_selectrow('properties', $where);
        if ($row) {
            if ($property_name == "CANCELLATION POLICY ONEDAY") {
                $timevalue = unserialize($row['PROPERTIE_VALUE']);              
                foreach ($timevalue as $test) {
                    list($t1, $t2, $t3) = $test;
                    $ret1 = $this->check_time($t1, $t2, $hours) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $ret = $t3;
                    }
                }
            } else if ($property_name == "CANCELLATION POLICY REGULAR") {
                $timevalue = unserialize($row['PROPERTIE_VALUE']);
               //print_r($timevalue);
                foreach ($timevalue as $test) {
                    list($t1, $t2, $t3) = $test;
                    $ret1 = $this->check_time($t1, $t2, $hours) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $ret = $t3;
                    }
                }
            }
        }        
        return $ret;
    }

    public function cancellation_policy($transid, $eid, $retpercentage, $duration, $category) {
        $arr = array();
        $paidamount = 0;
        $refundamount = 0;
        $noof_rides = 0;
        $used_rides = 0;
        $refund=0;
        $working_days=0;
        $cancel_ride=0;
        $addl_rides=0;
        $startDate="";
        $endDate="";
        $dao = new Shuttledao();           
        if ($duration == SHUTTLE_TRIP_ONDAY) {
            $row = $dao->cancellation_policy($transid, $eid, $duration);            
            if ($row) {
                $paidamount = $row['paidamt'];
                $travelmode=$row['TravelMode'];
                $refundamount = ($paidamount * $retpercentage) / 100;                
                if($travelmode=='B')
                {
                    $refund=$refundamount/2;
                }
                else
                {
                    $refund=$refundamount;
                }
                $arr = array('status' => 'true', 'PaidAmount' => $paidamount, 'RefundAmount' => $refund, 'category' => $category);
            }
        } else {
            $row = $dao->cancellation_policy($transid, $eid, $duration);
            if ($row) {
                $startDate=$row['StartDate'];
                $endDate=$row['EndDate'];
                $working_days=$this->getWorkingDays($startDate, $endDate);
                $noof_rides = $row['NoofRides'];
                $used_rides = $row['NoofRidesUsed'];
                $noshow_ride=$row['NoShowRides'];
                $cancel_ride=$row['NoofCancelRides'];
                $addl_rides=$working_days-$noof_rides;
                if($cancel_ride >= $addl_rides)
                {
                    $bal_ride = $noof_rides - ($used_rides+$noshow_ride+($cancel_ride-$addl_rides));
                }
                else
                {
                    $bal_ride = $noof_rides - ($used_rides+$noshow_ride);
                }                
                $arr = array('status' => 'true', 'available_rides' => $bal_ride, 'ride_expiry_date' => $row['EndDate'], 'category' => $category);
            }
        }        
        return $arr;
    }

    public function booking_cancel($trans_id, $eid, $traveldate, $stime, $noofdays, $roster_id, $paidfare, $refundfare, $confirmsts, $emplat, $emplong,$mobileno) {
        $element_array = array();
        $dao = new Shuttledao();
        //get current date time
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $stsarr = array('confirm_status' => $confirmsts);
        switch ($confirmsts) {
            case 1:
                if ($noofdays > 1) {
                    $category = 2;//regular
                    $element_array = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category);
                } else {
                    $category = 1;//oneday
                    $element_array = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category);
                }                
                return array_merge($element_array, $stsarr);
                break;
            case 2:
                if ($noofdays > 1) {
                    $category = 2;//regular
                    $element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                } else {
                    $category = 1;//oneday
                   //payment return here
                    $paymentdet=$dao->getTxnid($trans_id);                   
                    if($paymentdet)
                    {
                        $orderid=$paymentdet['Payment_Token'];
                        $txnid=$paymentdet['TxnId'];   
                        $this->paytmwalletmoney_refund($orderid, $txnid, $refundfare, $mobile_no);
                    }                   
                    $element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                }
                return $element_array;
                break;
            default:
                $element_array = "False5";
                return $element_array;
                break;
        }
    }

    public function booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category) {
        $element_array = array();
        $dao = new Shuttledao();
        //get current date time

        $cnt = $dao->shuttle_booking_update($trans_id,$category,$roster_id,$eid,3);//cancel
        if ($cnt == 1) {
            $element_array = array('status' => 'true', 'confirm_status' => $confirmsts, 'category' => $category);
        } else {

            $element_array = array('status' => 'false', 'confirm_status' => $confirmsts, 'category' => $category);
        }
        return $element_array;

        //noshow in roster_passenger
        //cancel in shuttle_booking_trans
    }

    public function booking_reshedule($old_trip_id,$roster_id,$trans_id,$new_trip_id,$stop_id, $eid, $emplat, $emplong) {
        $dao = new Shuttledao();
        $sts = "false";
        $element_array = array();
        $category=1;
        //echo $old_trip_id,$roster_id,$trans_id,$new_trip_id,$stop_id, $eid, $emplat, $emplong;
        //exit;
        $ret=$dao->shuttle_booking_update($trans_id,$category,$roster_id,$eid,4);//4-'reschedule
       
        if($ret==1)
        {            
          $dao->getpayment_tokenno($new_trip_id,$trans_id,$stop_id,$eid);  
          $sts='true';
        } 
                
        $element_array = array('status' => $sts);
        return $element_array;
    }

    private function check_time($t1, $t2, $tn) {
        if ($t2 >= $t1) {
            return $t1 <= $tn && $tn <= $t2;
        } else {
            return !($t2 <= $tn && $tn <= $t1);
        }
    }

    public function reason($bid) {
        $arr = array();
        //creae object for driver dao class
        $dao = new Shuttledao();
        try {
            //get reason master values
            $where = "ACTIVE = '1' and BRANCH_ID='$bid' and CATEGORY='ShuttleFeedback'";
            $row = $dao->c_selectarray('reason_master', $where);
            foreach ($row as $val) {
                $arr[] = array('sno' => $val['REASON_ID'], 'reason' => $val['REASON'], 'rstatus' => $val['CATEGORY']);
            }
            $element_array = array('status' => 1, 'reason_list' => $arr);
            return $element_array; //return response to controller
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    public function shuttle_support() {
        $tmp = array();
        $dao = new Shuttledao();
        $where = "ACTIVE=1 and CATEGORY='Shuttle' and BRANCH_ID='1'";
        $row = $dao->c_selectarray('reason_master', $where);
        foreach ($row as $arg) {
            $tmp[$arg['SUPPORT_CATEGORY']][] = $arg['SUB_SUPPORT_CATEGORY'] . '@@#' . $arg['REASON'] . '@@#' . $arg['REASON_ID'];
        }
        $output = array();
        foreach ($tmp as $type => $labels) {
            $output[] = array(
                'category' => $type,
                'sub_category' => $labels
            );
        }
       // echo json_encode($output);
        return array('Apiresponse' => $output);
    }

    public function shuttle_support_log($bid, $empid, $reasonid, $remarks) {
        $element_array = array();
        $sts = "false";
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        $data = array('EMPLOYEE_ID' => $empid, 'REASON_ID' => $reasonid, 'REMARKS' => $remarks, 'PROCESS_TIME' => $curdatetime, 'BRANCH_ID' => $bid);
        $cnt = $dao->c_insert('support_log', $data);
        if ($cnt > 0) {
            $sts = "true";
        }
        $element_array = array('Apiresponse' => $sts);
        return $element_array;
    }

    public function shuttle_issue() {
        $element_arr = array(); //shuttle_issue
        $where = "Active=1";
        $dao = new Shuttledao();
        $row = $dao->c_selectarray('shuttle_issue', $where);
        foreach ($row as $val) {
            $element_arr[] = array('Sno' => $val['Sno'], 'IssueName' => $val['IssueName'], 'IssueSubName' => $val['IssueSubName'], 'IssueDetails' => $val['IssueDetails']);
        }
        $element_array = array('issue_det' => $element_arr);
        return $element_array;
    }

    public function emp_app_panic($empid, $cab_id, $roster_id, $lat, $lng, $location, $branch_id) {
        $element_array = array();
        //create object
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_time = $getdate['cur_date_time'];

        if ($roster_id == 0) {
            $data = array('EMPLOYEE_ID' => $empid, 'BRANCH_ID' => $branch_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        } else {
            $data = array('EMPLOYEE_ID' => $empid, 'BRANCH_ID' => $branch_id, 'ROSTER_ID' => $roster_id, 'CAB_ID' => $cab_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        }
        $cnt = $dao->c_insert('panic_alert', $data);
        if ($cnt > 0) {
            $element_array = array('status' => 'true');
        } else {
            $element_array = array('status' => 'false');
        }
        return $element_array;
    }

    public function insert_route_master($startpoint, $endpoint, $startlat, $startlong, $endlat, $endlong, $duration, $stime, $etime, $routepath, $route_type, $ct) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_time = $getdate['cur_date_time'];

        $tottime = $this->sec_to_time($duration);
        $this->load->model('ShuttleElasticmdl');
        $data = array('StartPoint' => $startpoint, 'EndPoint' => $endpoint, 'Start_Latitude' => $startlat, 'Start_Longitude' => $startlong, 'End_Latitude' => $endlat,
            'End_Longitude' => $endlong, 'StartTime' => $stime, 'EndTime' => $etime, 'Travel_Duration' => $tottime, 'Process_Datetime' => $cur_time);
        $cnt = $dao->c_insert('shuttle_route_master', $data);
        $id = $this->db->insert_id();
        if ($cnt > 0) {
            $userColData = json_decode($routepath, true);
            $path = $userColData['RoutePath'];
            $this->ShuttleElasticmdl->pathInsert($id, '1', $duration, $path, $route_type);
        }
    }

    public function shuttle_notification($eid, $bid) {
        $element_arr = array();
        $dao = new Shuttledao();

        $where = "emp_id='$eid' and branch_id='$bid'";
        $row = $dao->c_selectarray('shuttle_notification', $where);
        foreach ($row as $val) {
            $element_arr[] = array('Heading' => $val['heading'], 'Message' => $val['message'], 'DateTime' => $val['created_at']);
        }
        $element_array = array('notification' => $element_arr);
        return $element_array;
    }

    public function shuttle_invoice($bookingno) {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function shuttle_termscondition() {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function tracking($rosterid, $cabid, $eid, $gpsdate) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $cur_datetime = $getdate['cur_date_time'];
        $cur_date = $getdate['cur_date'];
        $data = array();
        $element_arr1 = array();
        $element_arr = array();
        $gps_det = array();
        $tripsts = "";
        $tripmsg = "";
        $drivername = "";
        $drivermobile = "";
        $vehno = "";
        $vehmod = "";
        //employee details
        $row1 = $dao->track1($rosterid, $cabid, $eid);
        if ($row1) {
            $cab_arrived_time = $row1['cab_arrived_time'];
            $boarded_time=$row1['droptime'];
            $trip_type=$row1['TRIP_TYPE'];
            
            $rsts = $row1['ROSTER_PASSENGER_STATUS'];
            if($trip_type==TRIP_P){
                if (is_null($cab_arrived_time)) {
                    $tripsts = "1";
                    $tripmsg = "Live Tracking";
                } else if ((!is_null($cab_arrived_time)) && (!in_array($rsts, unserialize(RP_PICKUP_DROP_OTP)))) {
                    $tripsts = "2";
                    $tripmsg = "OTP :" . $row1['OTP'] . " ";
                } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                    $tripsts = "3";
                    $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                }
            }
            else                
            {
                if (is_null($boarded_time) && (!in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                } 
                else if (!(is_null($boarded_time)) && (!in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                } 
                else if ((!is_null($boarded_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "1";
                    $tripmsg = " OTP :" . $row1['OTP'] ;
                } 
                else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                    $tripsts = "3";
                    $tripmsg = "You have reached your location!!!!";
                }
            }
            $order = floatval($row1['ROUTE_ORDER']);
            $waypoints = '';
            $row2 = $dao->track2($rosterid, $cabid, $order,$eid);
            if (count($row2) > 0) {
                foreach ($row2 as $val) {
                    $element_arr1[] = array('emplat' => $val['LATITUDE'], 'emplong' => $val['LONGITUDE'], 'pickuptime' => $val['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'],AES_ENCRYPT_KEY), 'emplocation' => $val['LOCATION_NAME']);
                    $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                }
            } else {
                $element_arr1[] = array('emplat' => $row1['LATITUDE'], 'emplong' => $row1['LONGITUDE'], 'pickuptime' => $row1['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'],AES_ENCRYPT_KEY), 'emplocation' => $row1['LOCATION_NAME']);
                $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
            }
            $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
            if ($gpsdate == "1900-01-01 00:00:00") {
                $driverdet = $dao->get_driver_details($cabid);
                if ($driverdet) {
                    $drivername = $driverdet['DRIVERS_NAME'];
                    $drivermobile = $driverdet['DRIVER_MOBILE'];
                    $vehno = $driverdet['VEHICLE_REG_NO'];
                    $driverimage = $driverdet['DRIVER_IMAGE'];
                    $vehmod = $driverdet['MODEL'];
                }
            }            
            $ret = $this->ShuttleElasticmdl->cabNavigation($cabid, '1900-01-01 00:00:00');
            $origin = $ret['RESULT'][0]['POSITION'];
            $driverimage = mysql_real_escape_string("https://mytransportportal.com/cog_track/img/driver_img/photo.jpg");
            $km = $this->calkm($origin, $destination, $waypoints, 'both', 'driving');
            $x = explode("-", $km);
            if ($x[1] > 0) {
                $hours = floor($x[1] / 3600);
                $mins = floor($x[1] / 60 % 60);
                if ($hours == 0) {
                    $minutes = $mins . " mins";
                } else {
                    $minutes = $hours . 'hrs -' . $mins . " mins";
                }
            } else {
                $minutes = "NA";
            }
            $data[] = array('DriverName' => $drivername,
                "DriverMobile" => $drivermobile,
                "CabNo" => $vehno,
                "cabLatLong" => $origin,
                "TripStatus" => $tripsts,
                "TripMsg" => $tripmsg,
                "DriverImage" => $driverimage,
                'CabModel' => $vehmod,
                "ETA" => $minutes);
            //}

            $element_arr = $this->ShuttleElasticmdl->cabNavigation($cabid, $gpsdate);
            //print_r($element_arr);
            $gps_det = $element_arr['RESULT'];
        }

        $element = array('DriverDetails' => $data, "details" => $gps_det, 'empdetails' => $element_arr1);
        //echo json_encode($element);

        return $element;
    }

    public function emp_logout($eid) {
        return $element_array = array('status' => 'true');
    }

    //get employee feedback 
    //insert feedback log table
    public function emp_feedback($roster_id, $emp_id, $feedback,$comments) {
        $ret = false;
        $dao = new Shuttledao();
        try {
            //get current date time
            $getdate = $dao->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            //$feedbackmsg = explode('|', $feedback);
            $data = array('ROSTER_ID' => $roster_id, 'EMPLOYEE_ID' => $emp_id, 'FEEDBACK1' => $feedback,'COMMENTS'=>$comments,'CREATED_DATE' => $curdatetime);
            $cnt = $dao->c_insert('feedback_log', $data); //insert feedback log table
            if ($cnt > 0) {
                $ret = true;
            }
            $element_array = array('status' => $ret);
            return $element_array; //return response to controller
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    
    //--new table search and booking below//
    
    public function shuttle_searchroute($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate,$reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category)
    {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";

       // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type,$resheduledate,$reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type,$resheduledate,$reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type,$resheduledate,$reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type,$resheduledate,$reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
//        print_r($pickarray);
//        print_r($droparray);
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }           
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id,$resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id=$row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];                        
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id=$val['PICK_STOP_ID'];                        
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);   
                        if($resheduledate!='0000-00-00')
                        {
                            $shuttletime="ANext"; 
                            $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                            $traveltime = $traveldist[1];
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                        }
                        else
                        {
                            if($shedule_date==$curdate && $stime > $curtime)
                            {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime="CMonday";
                                }
                                else
                                {
                                   $shuttletime="ANext";                                                               
                                }
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                                $traveltime = $traveldist[1];
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                            }                        
                            else if($shedule_date > $curdate)
                            {
                                if (date("w", strtotime($curdate))==6 || date("w", strtotime($curdate))== 5) {

                                    $shuttletime="CMonday";
                                }
                                else
                                {
                                   $shuttletime="BTomorrow";                                                               
                                }
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);                           
                                $traveltime = $traveldist[1];
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            } 
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {                    
                    $seatavial = $dao->get_seatavailability($route_id,$resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id=$row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];   
                        //$route_name=$row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id=$val['DROP_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if($resheduledate!='0000-00-00' && $stime > $curtime)
                        {
                            $shuttletime="ANext"; 
                            $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                            $traveltime = $traveldist[1];
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                        }
                        else
                        {
                            if($shedule_date==$curdate && $stime > $curtime)
                            {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime="CMonday";
                                }
                                else
                                {
                                   $shuttletime="ANext";                                                               
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                                $traveltime = $traveldist[1];
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            }                        
                            else if($shedule_date > $curdate)
                            {
                                if (date("w", strtotime($curdate))==6 || date("w", strtotime($curdate))== 5) {
                                    $shuttletime="CMonday";
                                }
                                else
                                {
                                   $shuttletime="BTomorrow";                                                               
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                                $traveltime = $traveldist[1]; 
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc',$element_arr, array('route_date','approxtime','shuttle_time'));
        $d_array = $this->msort('asc',$element_arr1, array('route_date','approxtime','shuttle_time'));
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }
    
    public function shuttle_booking_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
        $this->load->model('ShuttleElasticmdl');

        if($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0|| $dlat == 0 || $dlong == 0 || $ssotoken == 'null'){
             $element_array = array('status' => 0, 'message' => 'Subsciption Failed');
             return $element_array;
             exit;
        }
        if ($noofdays > 1) {
            $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            $enddate = date('Y-m-d', strtotime("+$noofdays day", strtotime($shuttledate)));
            $where = "EmployeeId='$eid' and '$shuttledate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $dao->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if ($ptag == "Next") {
                if ($ttype == "B" && $dtag == "Tomorrow") {
                    $shuttledate = $curdate;
                    $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                } else {
                    $shuttledate = $curdate;
                    $enddate = $curdate;
                }
            } else if ($ptag == "Tomorrow") {
                $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            } else {
                $shuttledate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
            }
            $subscribtionsts = 0;
        }
        $message=$this->seats_confirmed_sts($ttype, $tripid_p, $tripid_d);
        if ($message=="Confirmed") {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {
                $origin = $plat . "," . $plong;
                $destination = $dlat . "," . $dlong;
                $tdist = round($this->calkm($origin, $destination, $origin, 'km', 'driving'));
               //exit;
                $totdays = 0;
                $tfare = 0;
                $validtrip = 0;
                //if (count($tariff) > 0 && $noofdays > 0) {                    
                    if ($ttype == "B") {
                        $totdays = $noofdays;
                        $veh_id_p=$dao->getvehicleid($tripid_p);
                        $tariff_p = $this->tariff_cal($tdist, $ttype, $route_type,$veh_id_p, $noofdays);
                        $veh_id_d=$dao->getvehicleid($tripid_d);
                        $tariff_d = $this->tariff_cal($tdist, $ttype, $route_type,$veh_id_d, $noofdays);
                        if(count($tariff_p)>0 && count($tariff_d)>0)
                        {
                            $tfare = $tariff_p[0]['fare']+$tariff_d[0]['fare'];                       
                            $validtrip = $tariff_p[0]['validTrip'] * 2;
                            $totdist = $tdist * 2;
                            $tariff_id= $tariff_p[0]['tariff_id'];
                        }
                    } else {
                        $totdays = $noofdays; 
                        $veh_id_p=$dao->getvehicleid($tripid_p);
                        $tariff= $this->tariff_cal($tdist, $ttype, $route_type,$veh_id_p, $noofdays);    
                        $tfare = $tariff[0]['fare'];                        
                        //$tfare = $tariff[0]['fare'];
                        $validtrip = $tariff[0]['validTrip'];
                        $totdist = $tdist;
                        $tariff_id= $tariff[0]['tariff_id'];
                    }               
                $data = array('BranchId'=>$branch_id,'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => $ppoint, 'DropPoint' => $dpoint, 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $tfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $shuttledate, 'EndDate' => $enddate, 'NoofDays' => $totdays, 'NoofRides' => $validtrip, 'TotalPaidAmt' => $tfare, 'RouteType' => $route_type,'TariffId'=>$tariff_id);
                $cnt = $dao->c_insert('shuttle_booking', $data);
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $dao->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {
                        //$tfare=1;
                        $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $tfare,$stopid_p,$stopid_d);
                        //$response=$this->paytmwithdraw_response($paymentno, 0, 0, $mobileno,$stopid_p,$stopid_d);
                        $element_array = array('status' => 1, 'message' => $response);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscribtion already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
        $this->ShuttleElasticmdl->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id,json_encode($element_array));
        return $element_array;
    }

    public function seats_confirmed_sts($ttype,$tripid_p,$tripid_d)
    {
        $dao = new Shuttledao();
        $avail_p=false;
        $avail_d=false;
        $message="";
        if($ttype=="B")
        {
            $avail_p=$dao->get_seatcount($tripid_p);
            $avail_d=$dao->get_seatcount($tripid_d);
            if($avail_p==true && $avail_d==true)
            {
                $message="Confirmed";
            }
            else if($avail_p==true && $avail_d==false)
            {
                $message="Pickup routes only available";
            }
            else if($avail_p==false && $avail_d==true)
            {
                $message="Return routes only available";
            }
            else
            {
                $message="No seats available";
            }
        }
        else
        {
            $avail_p=$dao->get_seatcount($tripid_p);           
            if($avail_p==true)
            {
                $message="Confirmed";
            }
            else
            {
                 $message="No seats avialable";
            }
        }
        return $message;
    }
    
    public function tracking_updated($rosterid, $cabid, $eid, $gpsdate)
    {
         $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $cur_datetime = $getdate['cur_date_time'];
        $cur_date = $getdate['cur_date'];
        $data = array();
        $element_arr1 = array();
        $element_arr = array();
        $gps_det = array();
        $tripsts = "";
        $tripmsg = "";
        $drivername = "";
        $drivermobile = "";
        $vehno = "";
        $vehmod = "";
        $blat=0;$blong=0;
        $slat=0;$slong=0;
        //employee details
        echo "test" .$roster_id.
        $row1 = $dao->track1($rosterid, $cabid, $eid);
        if ($row1) {
            $cab_arrived_time = $row1['cab_arrived_time'];
            $boarded_time=$row1['droptime'];
            $trip_type=$row1['TRIP_TYPE'];
            $rsts = $row1['ROSTER_PASSENGER_STATUS'];
            $blat=$row1['blat'];
            $blong=$row1['blong'];
            $branchname=$row1['branch'];
            $slat=$row1['LATITUDE'] ;
            $slong=$row1['LONGITUDE'];
            print_r($row1);
            if($trip_type==TRIP_P){
//                    $order = "rp.ROUTE_ORDER>=".$row1['ROUTE_ORDER']."";
                    $order = $row1['ROUTE_ORDER'];
                    if (is_null($cab_arrived_time)) {
                        $tripsts = "1";
                        $tripmsg = "Live Tracking";
                        $tracking=1;
                    } else if ((!is_null($cab_arrived_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                        $tripsts = "2";
                        $tripmsg = "Cab reached your Location.OTP is " . $row1['OTP'] . " ";
                        $tracking=1;
                    } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                        $tripsts = "3";
                        $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                        $tracking=1;
                    }
                    
                    $slat=$row1['LATITUDE'] ;
                    $slong=$row1['LONGITUDE'];
                    $s_loc = $val['LOCATION_NAME'];
                    $blat=$row1['blat'];
                    $blong=$row1['blong'];
                    $b_loc =$row1['branch'];
                   
                }
                else
                {
                    $order = $row1['ROUTE_ORDER'];
                    if (is_null($boarded_time) && (in_array($rsts, unserialize(RP_CREATED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                    $tracking=1;                   
                    } 
                    else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_CREATED)))) {
                        $tripsts = "1";
                        $tripmsg = "Not Boarded";
                        $tracking=1;                       
                    } 
                    else if ((!is_null($boarded_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                        $tripsts = "2";
                        $tripmsg = " OTP :" . $row1['OTP'] ;
                        $tracking=1;                       
                    } 
                    else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP)))) {
                        $tripsts = "3";
                        $tripmsg = "You have reached your location!!!!";
                        $tracking=0;
                    }
                    $slat=$row1['blat'];
                    $slong=$row1['blong'];
                    $s_loc = $row1['branch'];
                    $blat=$row1['LATITUDE'] ;
                    $blong=$row1['LONGITUDE'];
                    $b_loc = $val['LOCATION_NAME'];

                }     
               
                if($tracking==1){
                $waypoints = '';
                $row2 = $dao->track2($rosterid, $cabid,  floatval($order),$eid); //get all employee details  for particular traking roster id                   
                $r_order=0;
                $sts="";                 
                $rowcnt=count($row2);
                if ($rowcnt > 0) {
                    foreach ($row2 as $val) {
                        $otpsts=$val['ROSTER_PASSENGER_STATUS'];
                        $r_order=$val['ROUTE_ORDER'];
                        
                        if($trip_type==TRIP_P){                            
                            if($route_order > $r_order)
                            {
                                $waypoints=$sts=="true" ? $val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|" : '';                               
                            }
                            else
                            { 
                                $element_arr1[] = array('slat' => $val['LATITUDE'], 'slong' => $val['LONGITUDE'], 'pickuptime' => $val['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'],AES_ENCRYPT_KEY),
                                    'emplocation' => $val['LOCATION_NAME'],'empid'=>$val['EMPLOYEE_ID'],'route_order'=>$val['ROUTE_ORDER'],'elat'=>$blat,'elong'=>$blong,'s_loc'=>$s_loc,'b_loc'=>$b_loc);
                                if(!in_array($otpsts,  unserialize(RP_PICKUP_DROP_OTP)))
                                {
                                    $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                                }
                                if($eid==$val['EMPLOYEE_ID'] && in_array($otpsts,  unserialize(RP_PICKUP_DROP_OTP)))
                                {
                                    $sts="true";
                                }
                            }
                        }
                        else
                        {
                            $element_arr1[] = array('slat' => $val['LATITUDE'], 'slong' => $val['LONGITUDE'], 'pickuptime' => $val['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'],AES_ENCRYPT_KEY),
                                'emplocation' => $val['LOCATION_NAME'],'empid'=>$val['EMPLOYEE_ID'],'route_order'=>$val['ROUTE_ORDER'],'elat'=>$blat,'elong'=>$blong,'s_loc'=>$s_loc,'b_loc'=>$b_loc);
                            if(!in_array($otpsts,  unserialize(RP_PICKUP_DROP_OTP)))
                            {
                                $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                            }
                        }                     
                    }
                } else {
                    $element_arr1[] = array('slat' => $row1['LATITUDE'], 'slong' => $row1['LONGITUDE'], 'pickuptime' => $row1['picktime'], 'empname' => $dao->AES_DECRYPT($row1['NAME'],AES_ENCRYPT_KEY),
                        'emplocation' => $row1['LOCATION_NAME'],'empid'=>$eid,'elat'=>$blat,'elong'=>$blong,'s_loc'=>$s_loc,'b_loc'=>$b_loc);
                    $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
                }
            $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
            if ($gpsdate == "1900-01-01 00:00:00") {
                $driverdet = $dao->get_driver_details($cabid);
                if ($driverdet) {
                    $drivername = $driverdet['DRIVERS_NAME'];
                    $drivermobile = $driverdet['DRIVER_MOBILE'];
                    $vehno = $driverdet['VEHICLE_REG_NO'];
                    $driverimage = $driverdet['DRIVER_IMAGE'];
                    $vehmod = $driverdet['MODEL'];
                }
            }
            //echo json_encode($element_arr1);
            $ret = $this->ShuttleElasticmdl->cabNavigation($cabid, '1900-01-01 00:00:00');
            $origin = $ret['RESULT'][0]['POSITION'];
            //$origin = $ret['RESULT'][0]['POSITION'];
            $gps_currdate=$ret['RESULT'][0]['GPS_DATE'];
            $driverimage = mysql_real_escape_string("https://mytransportportal.com/cog_track/img/driver_img/photo.jpg");
            if($sts=="true")
            {
                $waypoints.=$blat. ',' . $blong . "|";
                $destination = $blat. ',' . $blong ;
            }
            $km = $this->calkm($origin, $destination, $waypoints, 'both', 'driving');
            $x = explode("-", $km);
            if ($x[1] > 0) {
                $hours = floor($x[1] / 3600);
                $mins = floor($x[1] / 60 % 60);
                if ($hours == 0) {
                    $minutes = $mins . " mins";
                } else {
                    $minutes = $hours . 'hrs -' . $mins . " mins";
                }
            } else {
                $minutes = "NA";
            }
            $tsts_row=$dao->alert_signal_failure($rosterid, $cabid, $gps_currdate);
            if($tsts_row)
            {
                if($tsts_row['tripsts']=='signalfailed')
                {                 
                    $tripmsg="Alert signal failure";
                }

            }
            $data[] = array('DriverName' => $drivername,
                "DriverMobile" => $drivermobile,
                "CabNo" => $vehno,
                "cabLatLong" => $origin,
                "TripStatus" => $tripsts,
                "TripMsg" => $tripmsg,
                "DriverImage" => $driverimage,
                'CabModel' => $vehmod,
                "ETA" => $minutes);
            }

            $element_arr = $this->ShuttleElasticmdl->cabNavigation($cabid, $gpsdate);
            //print_r($element_arr);
            $gps_det = $element_arr['RESULT'];
        }
        $element = array('DriverDetails' => $data, "details" => $gps_det, 'empdetails' => $element_arr1);
        return $element;
    }
    
    public function c_insert($tablename, $values) {
        $this->db->set($values);
        $this->db->insert($tablename, $this);
        if ($this->db->affected_rows() > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function c_update($tablename, $values, $where) {
        $where1 = $where;
        if ($where1 != '') {
            $this->db->where($where1);
            $this->db->update($tablename, $values);
            if ($this->db->affected_rows() > 0) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public function c_selectrow($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            return $row1;
        }
    }

    public function c_selectarray($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->result_array();
            return $row1;
        }
    }

    public function output($element_array, $ct) {
        if ($ct == "ip") {
            return json_encode($element_array);
        } else {
            $string = json_encode($element_array);
            $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
            $valreturn = array('xyssdff' => $returnval);
            return json_encode($valreturn);
        }
    }

    function encrypt($message, $initialVector, $secretKey) {
        return base64_encode(
                mcrypt_encrypt(
                        MCRYPT_RIJNDAEL_128, md5($secretKey), $message, MCRYPT_MODE_CFB, $initialVector
                )
        );
    }

    function decrypt($data, $key, $secretKey) {
        $decode = base64_decode($data);
        return mcrypt_decrypt(
                MCRYPT_RIJNDAEL_128, md5($key), $decode, MCRYPT_MODE_CFB, $secretKey
        );
    }

    function msort($sorder, $array, $key, $sort_flags = SORT_REGULAR) {
        if (is_array($array) && count($array) > 0) {
            if (!empty($key)) {
                $mapping = array();
                foreach ($array as $k => $v) {
                    $sort_key = '';
                    if (!is_array($key)) {
                        $sort_key = $v[$key];
                    } else {
                        // @TODO This should be fixed, now it will be sorted as string
                        foreach ($key as $key_key) {
                            $sort_key .= $v[$key_key];
                        }
                        $sort_flags = SORT_STRING;
                    }
                    $mapping[$k] = $sort_key;
                }
                if($sorder=="asc"){
                    asort($mapping, $sort_flags);
                }
                else
                {
                    arsort($mapping, $sort_flags);
                }
                $sorted = array();
                foreach ($mapping as $k => $v) {
                    $sorted[] = $array[$k];
                }
                return $sorted;
            }
        }
        return $array;
    }

    private function record_sort($records, $field, $reverse = false) {
        $hash = array();

        foreach ($records as $record) {
            $hash[$record[$field]] = $record;
        }

        ($reverse) ? krsort($hash) : ksort($hash);

        $records = array();

        foreach ($hash as $record) {
            $records [] = $record;
        }

        return $records;
    }

    public function getrandomnumber() {
        $a = 0;
        for ($i = 0; $i < 6; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(100000, 999999);

        return $a;
    }

    public function getrandomnumber1() {
        $a = 0;
        for ($i = 0; $i < 4; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(1000, 9999);

        return $a;
    }

    function Encrypt_Script($string, $key) {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result.=$char;
        }
        return base64_encode($result);
    }

    function Decrypt_Script($string, $key) {
        $result = '';
        //$string = base64_decode($string);

        for ($i = 0; $i < strlen($string); $i++) {

            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) - ord($keychar));
            $result.=$char;
        }
        return $result;
    }

    function sec_to_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor($seconds % 3600 / 60);
        $seconds = $seconds % 60;

        return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
    }

    public function sendsms($mobile, $sms) {
        $msg = urlencode($sms);
        $urltopost = "http://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=ntltaxi1&send=TOPSCS&dest=$mobile&msg=" . $msg;
        $ch = curl_init($urltopost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //Sms_Response
        $returndata = curl_exec($ch);
        return $returndata;
    }

    function getAddress($latitude, $longitude) {
        if (!empty($latitude) && !empty($longitude)) {

            $url = $this->signUrl("http://maps.googleapis.com/maps/api/geocode/json?address=" . $latitude . "," . $longitude . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($ch);
            //echo $response;
            curl_close($ch);

            //  $geocodeFromLatLong = json_decode($response);

            $output = json_decode($response);
            $status = $output->status;
            //Get address from json data
            $address = ($status == "OK") ? $output->results[1]->formatted_address : '';
            //Return address of the given latitude and longitude
            if (!empty($address)) {
                return $address;
            } else {
                return false;
            }
        } else {
            return false;
        }
      
    }

    function encodeBase64UrlSafe($value) {
        return str_replace(array('+', '/'), array('-', '_'), base64_encode($value));
    }

    function decodeBase64UrlSafe($value) {
        return base64_decode(str_replace(array('-', '_'), array('+', '/'), $value));
    }

    function signUrl($myUrlToSign, $privateKey) {
        // parse the url
        $url = parse_url($myUrlToSign);
        $urlPartToSign = $url['path'] . "?" . $url['query'];

        // Decode the private key into its binary format
        $decodedKey = $this->decodeBase64UrlSafe($privateKey);

        // Create a signature using the private key and the URL-encoded
        // string using HMAC SHA1. This signature will be binary.
        $signature = hash_hmac("sha1", $urlPartToSign, $decodedKey, true);

        $encodedSignature = $this->encodeBase64UrlSafe($signature);

        return $myUrlToSign . "&signature=" . $encodedSignature;
    }

    function calkm($origin, $destination, $waypoints, $type, $mode) {
        if ($mode == "walk") {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
        } else {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        //echo $response;
        curl_close($ch);

        $data = json_decode($response);

        $km = 0;
        $tim = "";
        // If we got directions, output all of the HTML instructions
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                //foreach ($leg->steps as $step) {
                $i = 0;
                foreach ($leg->distance as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        $x = explode(" ", $val);
                        if ($x[1] == 'km') {
                            $km+=$x[0];
                        } else {
                            $km+=($x[0] * 0.001);
                        }
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        
                    } else {
                        $tim +=$val;
                    }
                }
            }
            if ($type == 'km') {
                return $km;
            } else {
                return $km . '-' . $tim;
            }
        }
    }
    
    public function getWorkingDays($startDate, $endDate)
    {
        $begin = strtotime($startDate);
        $end   = strtotime($endDate);
        if ($begin > $end) {

            return 0;
        } else {
            $no_days  = 0;
            while ($begin <= $end) {
                $what_day = date("N", $begin);
                if (!in_array($what_day, [6,7]) ) // 6 and 7 are weekend
                    $no_days++;
                $begin += 86400; // +1 day
            };

            return $no_days;
        }
}

}
