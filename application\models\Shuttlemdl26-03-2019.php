<?php

set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
include(APPPATH . 'libraries/Shuttledao.php');
include(APPPATH . 'libraries/EncdecPaytm.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttlemdl
 *
 * <AUTHOR>
 */
class Shuttlemdl extends CI_Model {

    function __construct() {
        parent::__construct();
        $this->load->model('ShuttleElasticmdl', 'semodel');
        $this->load->model('ShuttleCommonmdl', 'cmodel');
        $this->load->model('Shuttlemdl', 'smodel');
        //$this->load->model('ShuttleCommonmdl','cmodel');
    }

    public function ssvaluecheck($mobileno, $ssid, $ct) {
        $name = "";
        $ssval = "";
        $ret = "";
       // $dao = new Shuttledao();
       // log_message('custom', "\n------------ My var value is -----------\n" . $mobileno);
        try {
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
            $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $this->cmodel->c_selectrow('employees', $where);
            if ($row) {
                $name = $row['NAME'];
                $ssval = $row['SESSION_ID'];
                $privatekey = $row['PRIVATE_KEY'];
            }
            if ($ct == "ip" && $ssid == $ssval) {
                $ret = 'true,';
            } else {
                $rsa = new Crypt_RSA();
                $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
                $decoded = base64_decode($ssid);
                $rsa->loadKey($privatekey);
                $decrypted = $rsa->decrypt($decoded);
                $ret = $decrypted == $ssval ? 'true' : 'false';
            }
            return $ret;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function rsaencrypt($mobileno) {
        $rsa = new Crypt_RSA();
        try {
            extract($rsa->createKey());
            $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
            $element_array = str_ireplace('-----BEGIN PUBLIC KEY-----', '', $publickey);
            $elementrsaarray = trim(str_ireplace('-----END PUBLIC KEY-----', '', $element_array));

            $element_array1 = str_ireplace('-----BEGIN RSA PRIVATE KEY-----', '', $privatekey);
            $elementrsaarray1 = trim(str_ireplace('-----END RSA PRIVATE KEY-----', '', $element_array1));
            $data = array(
                'PUBLIC_KEY' => $elementrsaarray,
                'PRIVATE_KEY' => $elementrsaarray1
            );

           // $dao = new Shuttledao();
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
            $where1 = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
            $cnt = $this->cmodel->c_update('employees', $data, $where1);
            if ($cnt > 0) {
                return $elementrsaarray;
            } else {
                return $element_array = 'False';
            }
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_registraton($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct) {
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        try {
            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $element_array = array();
            $ENC_EMAIL = $dao->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "(EMAIL = '" . $ENC_EMAIL . "' || MOBILE = '" . $ENC_MOBILE . "') AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row = $this->c_selectrow('employees', $where);
            if ($row) {
                $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
            } else {
                // $eid = rand(10000, 100000);
                $eid = mt_rand(100000, 999999);
                $ENC_NAME = $dao->AES_ENCRYPT($name, AES_ENCRYPT_KEY);
                $data = array('BRANCH_ID' => 1, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                    'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
                $cnt = $this->c_insert('employees', $data);
                if ($cnt > 0) {
                    $message = "Thank you for registering with " . SMS_TITLE . ".enjoy the ride!";
                    $dao->insert_sms(1, 'TOPSCS', $mobile, $message);
                    $data1 = array('branch_id' => 1, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                    $this->c_insert('shuttle_notification', $data1);
                    $element_array = array('status' => 1, 'Message' => 'Successfully registered');
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Registration failed');
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_otpcreation($mobile, $password, $ct) {
        $smssts = "";
        $smsmsg = "";
        $element_array = array();
       // $dao = new Shuttledao();
        try {
            $getdate = $this->cmodel->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);

            $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $this->cmodel->c_selectrow('employees', $where);
            if ($row) {
                $ename = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                $eid = $row['EMPLOYEES_ID'];
                //Thank you for registering with {V}. enjoy the ride!
                $otp = $mobile == "9176797925" && $ct == "ip" ? '1111' : $otp;

                $smsmsg = "Dear $ename, Please use $otp as login OTP in " . SMS_TITLE;
                $smssts = $this->cmodel->sendsms($mobile, $smsmsg);
                $data = array('MOBILE_NO' => $mobile, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0, 'SMS_RESPONSE' => $smssts,
                    'OTP_CATEGORY' => 'ShuttleLogin', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime);
                $cnt = $this->cmodel->c_insert('otp_verification', $data);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_otpresend($mobile, $ct) {
        $smssts = "";
        $smsmsg = "";
        $empid = "";
        $branch_id = 0;
        $element_array = array();
       // $dao = new Shuttledao();
        try {
            $getdate = $this->cmodel->get_datetime();
            $curdatetime = $getdate['cur_date_time'];

          //  $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            // $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
            $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $this->cmodel->c_selectrow('employees', $where);
            if ($row) {
                $ename = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                $empid = $row['EMPLOYEES_ID'];
                $branch_id = $row['BRANCH_ID'];
                $where1 = " VERIFIED_STATUS='0' and MOBILE_NO='$mobile' and OTP_CATEGORY='ShuttleLogin' and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
                $row1 = $this->cmodel->c_selectrow('otp_verification', $where1);
                if ($row1) {
                    $otp = $row1['OTP'];
                }
                //TOPSCS
                $otp = $mobile == "9176797925" && $ct == "ip" ? '1111' : $otp;
                $smsmsg = "Dear $ename, Please use $otp as login OTP in " . SMS_TITLE;

                $data2 = array('BRANCH_ID' => $branch_id, "ORIGINATOR" => 'TOPSCS', 'RECIPIENT' => $mobile, 'MESSAGE' => $smsmsg, 'STATUS' => 'U', 'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_BY' => $empid, 'CREATED_DATE' => $curdatetime);
                $this->cmodel->c_insert('sms', $data2);
                // $smssts = $this->sendsms($mobile, $smsmsg);
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
            }
            return $element_array;
        } catch (Exception $e) {
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_otpverified($mobile, $password, $otp, $deviceinfo, $deviceid, $fcmid, $appversion, $ct) {
        $sessionEncrypt = "";
        $encrypted = "";
        $officeaddr = "--";
        $officelat = 0;
        $officelong = 0;
        $element_array = array();
        $comp_id = 0;
        $auto_id = 0;
        try {
            $dao = new Shuttledao();
            $getdate = $this->cmodel->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $otpsts = $dao->otp_verification($mobile, $otp, 'ShuttleLogin');
            if ($otpsts == 'true') {
                $where1 = "OTP='$otp' and VERIFIED_STATUS='0' and MOBILE_NO='$mobile' and OTP_CATEGORY='ShuttleLogin' and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
                $value = array('VERIFIED_STATUS' => 1);
                $cnt = $this->cmodel->c_update('otp_verification', $value, $where1);
                if ($cnt > 0) {
                    $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
                    $row = $this->cmodel->c_selectrow('employees', $where);
                    //echo $this->db->last_query();
                    if ($row) {
                        $name = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                        $gender = $row['GENDER'];
                        $email = $this->cmodel->AES_DECRYPT($row['EMAIL'], AES_ENCRYPT_KEY);
                        $address = $row['ADDRESS'];
                        $lat = $row['LATITUDE'];
                        $long = $row['LONGITUDE'];
                        $category = $row['CATEGORY'];
                        $bid = $row['BRANCH_ID'];
                        $eid = $row['EMPLOYEES_ID'];
                        $emergency_no = $row['EMERGENCY_CONTACT_NO'];
                        $comp_id = $row['SHUTTLE_COMPANY_ID'];
                        $auto_id = $row['id'];
                        $corporate_booking_status=$row['CORPORATE_BOOKING_STATUS'];
                        if (!(is_null($emergency_no))) {
                            $emergency_no = $this->cmodel->AES_DECRYPT($emergency_no, AES_ENCRYPT_KEY);
                        } else {
                            $emergency_no = 0;
                        }
                        $mcat = $ct == 'ip' ? 'iOS' : 'ANDROID';
                        
                        $address_property=$this->property_check('OFFICE_ADDRESS', $bid, 0);
                        if($address_property=='Branch'){
                            $where1 = "BRANCH_ID = '$bid ' AND ACTIVE = '1'";
                            $row1 = $this->cmodel->c_selectrow('branch', $where1);
                            if ($row1) {
                                $officeaddr = $row1['LOCATION'];
                                $officelat = $row1['LAT'];
                                $officelong = $row1['LONG'];
                            }   
                        }
                        else
                        {
                            $where1 = "BRANCH_ID = '$bid ' AND COMPANY_ID='$comp_id' AND ACTIVE = '1'";
                            $row1 = $this->cmodel->c_selectrow('shuttle_company_master', $where1);
                            if ($row1) {
                                $officeaddr = $row1['LOCATION'];
                                $officelat = $row1['LAT'];
                                $officelong = $row1['LONG'];
                            }   
                        }

                        $sessionid = rand(10000, 100000);
                        $sql_encryt = $this->cmodel->AES_ENCRYPT($sessionid, AES_ENCRYPT_KEY);
                        $data1 = array(
                            'SESSION_ID' => base64_encode($sql_encryt),
                            'MOBILE_GCM' => $fcmid,
                            'DEVICE_INFO' => $deviceinfo,
                            'DEVICE_ID' => $deviceid,
                            'APP_VERSION' => $appversion,
                            'MOBILE_CATEGORY' => $mcat
                        );
                        $this->cmodel->c_update("employees", $data1, $where);
                        $sessionEncrypt = base64_encode($sql_encryt);
                        $encrypted = $this->rsaencrypt($mobile);
                        if ($encrypted != 'False') {
                            $element_array = array('status' => 1, 'Message' => 'success', 'branch_id' => $bid, 'employee_id' => $eid, 'mobile' => $mobile, 'emergency_contact_no' => $emergency_no, 'name' => $name, 'gender' => $gender, 'email' => $email,
                                'homeaddress' => $address, 'homelat' => $lat, 'homelong' => $long, 'officeaddress' => $officeaddr, 'officelat' => $officelat, 'officelong' => $officelong,'corporate_booking_status'=>$corporate_booking_status,
                                'category' => $category, 'sessionid' => $sessionEncrypt, 'publickey' => $encrypted, 'company_id' => $comp_id, 'auto_id' => $auto_id, 'PAYTM_MID' => PAYTM_MID, 'PAYTM_WEBSITE' => PAYTM_WEBSITE_APP,
                                'PAYTM_INDUSTRY_TYPE_ID' => PAYTM_INDUSTRY_TYPE_ID, 'PAYTM_CHANNEL_ID_APP' => PAYTM_CHANNEL_ID_APP, 'PAYTM_CLIENT_ID' => PAYTM_CLIENT_ID, 'PAYTM_CLIENT_SECRET' => PAYTM_CLIENT_SECRET, 'GOOGLE_MAP_API_KEY' => GOOGLE_MAP_API_KEY);
                        } else {
                            $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                        }
                    } else {
                        $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                    }
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                }
            } else {
                $element_array = array('status' => 0, 'Message' => 'OTP Expired');
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function appversion($eid, $bid, $appversion, $ct) {
        $dao = new Shuttledao();
        $element_array = array();
        $property_array = array();
        $roster_id = 0;
        $roster_passenger_id = 0;
        $cab_id = 0;
        $feedback_roster_id = 0;
        $sloc = "";
        $eloc = "";
        $emploc = "";
        $trip_type = "";
        $tsloc = "";
        $teloc = "";
        $ttrip_type = "";
        $temploc = "";
        $cabno = "";
        $stime = "";
        $feedbackstime = "";
        $place_picker_sts = 0;
        try {
            if ($appversion != 0) {
                $where1 = "EMPLOYEES_ID = '" . $eid . "' AND BRANCH_ID = '$bid' and ACTIVE=1 and CATEGORY='Shuttle'";
                $data1 = array(
                    'APP_VERSION' => $appversion
                );
                $this->cmodel->c_update("employees", $data1, $where1);
            }
            $where1 = "EMPLOYEES_ID = '" . $eid . "' AND BRANCH_ID = '$bid' and ACTIVE=1 and CATEGORY='Shuttle'";
            $emp = $this->cmodel->c_selectrow('employees', $where1); // //check employee PLACE_PICKER_STATUS
            $place_picker_sts = $emp['PLACE_PICKER_STATUS'];
            $rate_sts=$emp['RATE_STATUS'];
            $rate_date=$emp['RATE_DATE'];
            $auto_id=$emp['id'];
            $corporate_booking_status=$emp['CORPORATE_BOOKING_STATUS'];
            
            $rate=$dao->getrating_sts($eid,$bid);          
            foreach ($rate as $value) {
                $rate_sts=intval($value['ratests']);                
                $rating+=$rate_sts;
            }            
            $rating_value=$rating==5?0:1;//if rating value is zero rating window is open in mobile app --get last 5 rating is 4 or 5
            
            $where = "ACTIVE=1 and PROPERTIE_CATEGORY='ShuttleApp' and BRANCH_ID='$bid'";
            $row = $this->cmodel->c_selectarray('properties', $where); // //check employee app property  
            foreach ($row as $val) {
                $property_array[] = array('property_name' => $val['PROPERTIE_NAME'], 'property_value' => $val['PROPERTIE_VALUE']);
            }
            $track = $dao->get_current_tracking_details($eid); //get traking details
            if ($track) {
                $roster_id = $track['ROSTER_ID'];
                $cab_id = $track['CAB_ID'];
                $tsloc = $track['START_LOCATION'];
                $teloc = $track['END_LOCATION'];
                $ttrip_type = $track['TRIP_TYPE'];
                $temploc = $track['LOCATION_NAME'];
                $cabno = $track['VEHICLE_REG_NO'];
                $stime = $track['ESTIMATE_START_TIME'];
                $roster_passenger_id = $track['ROSTER_PASSENGER_ID'];
                if ($ttrip_type == "P") {
                    $tsloc = $temploc;
                    $teloc = $teloc;
                } else {
                    $tsloc = $tsloc;
                    $teloc = $temploc;
                }
            }
            if ($ct != 'ip') {
                $feedback = $dao->get_current_feedback_details($eid, $ct); //get feedback deatils
                if ($feedback) {
                    $sloc = $feedback['START_LOCATION'];
                    $eloc = $feedback['END_LOCATION'];
                    $trip_type = $feedback['TRIP_TYPE'];
                    $emploc = $feedback['LOCATION_NAME'];
                    $feedback_roster_id = $feedback['ROSTER_ID'];
					//$feedback_roster_id = "0";
                    $feedbackstime = $feedback['ESTIMATE_START_TIME'];
                }
            }
            $element_array = array('status' => 1, 'GOOGLE_MAP_API_KEY' => GOOGLE_MAP_API_KEY, 'property_details' => $property_array, 'track_roster_id' => $roster_id, 'track_passenger_id' => $roster_passenger_id, 'track_cab_id' => $cab_id, 'track_sloc' => $tsloc, 'track_eloc' => $teloc, 'track_trip_type' => $ttrip_type, 'track_emp_loc' => $temploc, 'track_cab_no' => $cabno, 'track_start_time' => $stime, 'feedback_roster_id' => $feedback_roster_id, 'sloc' => $sloc, 'eloc' => $eloc, 'trip_type' => $trip_type, 'emp_loc' => $emploc, 
                'trip_time' => $feedbackstime, 'place_picker_status' => $place_picker_sts,'rating_value'=>$rating_value,'auto_id'=>$auto_id,'corporate_booking_status'=>$corporate_booking_status);
            return $element_array; //return response to controller    
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function tariff_cal($dist, $ttype, $route_type, $vehicle_id, $noofdays, $branchid, $tariff_type, $shedule_date) {
        if ($dist == 0) {
            $dist = 1;
        }
        $condition = "";
        if ($noofdays > 0) {
            // $condition = "and if(Tariff_Calculation_Name!='Corp',NumberofDays='$noofdays',if($noofdays=1,NumberofDays='$noofdays',NumberofDays>1))";
            $condition = "and NumberofDays='$noofdays'";
        }
        // echo "tariff type==".$tariff_type;
        $oneday_fixed_price = 0;
        $tariff_cal_days = 0;
        $tariff_cal_name = "";
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $tomorrow = date('Y-m-d', strtotime($curdate . ' +1 day'));
        try {
            $element_array = array();
            $vehicle_id = 1;
            if (strtolower($route_type) == strtolower("Km") && (strtolower($tariff_type) == strtolower("Km") || empty($tariff_type))) {

                $where = "BranchId='$branchid' and Active ='1' and  '$dist'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and Tariff_Type='km' and VehicleId='$vehicle_id' $condition order by NumberofDays";
                $row = $this->cmodel->c_selectarray('shuttle_tariff', $where);
                // echo $this->db->last_query();
                foreach ($row as $val) {
                    $tariffId = $val['TariffId'];
                    $validDays = $val['NumberofDays'];
                    $validTrip = $val['NumberofTrips'];
                    $baseKm = $val['Tariff_Base_Km'];
                    $basCost = $val['Tariff_Base_Cost'];
                    $extKmCost = $val['Tariff_Ext_Km_Cost'];
                    $flatCost = $val['Tariff_Flat_Cost'];
                    $percentage = $val['Percentage'];
                    $oneday_fixed_price = $val['Oneday_Fixed_Cost'];
                    $tariff_name = $val['Tariff_Name'];
                    $tariff_cal_name = $val['Tariff_Calculation_Name'];
                    // $lastDayThisMonth = date("Y-m-t",strtotime($tomorrow));
                    if ($validDays == 1) {
                        $sdate = $shedule_date;
                        $edate = $shedule_date;
                    } else {
                        $sdate = $tomorrow;
                        $days = $validDays - 1;
                        $edate = date('Y-m-d', strtotime($sdate . "  +$days day"));
                    }
                    if ($flatCost == 0) {
                        $extKmAmt = 0;
                        $exkm = 0;
                        $fare = 0;
                        $percentAmt = 0;
                        $exkm = $dist - $baseKm;
                        if ($exkm > 0) {
                            $extKmAmt = $exkm * $extKmCost;
                        }
                        if ($percentage == 0) {
                            $fare = ($basCost + $extKmAmt) * $validTrip;
                        } else {
                            $percentAmt = ($basCost + $extKmAmt) * $percentage / 100;
                            $fare = ($basCost + $extKmAmt + $percentAmt) * $validTrip;
                        }
                        $actual_fare = ($basCost + $extKmAmt) * $validTrip;
                    } else {
                        if ($percentage == 0) {
                            $fare = $flatCost * $validTrip;
                        } else {
                            $percentAmt = ($flatCost) * $percentage / 100;
                            $fare = ($flatCost + $percentAmt) * $validTrip;
                        }
                        $actual_fare = $flatCost * $validTrip;
                    }

                    $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'actual_fare' => round($actual_fare), 'fare' => round($fare), 'validDays' => $validDays, 'validTrip' => $validTrip, 'onedayFixedprice' => $oneday_fixed_price, 'tariff_cal_name' => $tariff_cal_name, 'percentage' => abs($percentage), 'start_date' => $sdate, 'end_date' => $edate, 'validity_days' => $validDays);
                }
            } else if ((strtolower($route_type) == strtolower("Fixed")) && ((strtolower($tariff_type) == strtolower("Fixed")) || empty($tariff_type))) {
                //and  '$dist'BETWEEN Tariff_St_Km AND Tariff_Cl_Km
                $where = "BranchId='$branchid' and Active ='1' and Tariff_Type='Fixed' and Tariff_Calculation_Name is null and VehicleId='$vehicle_id' $condition order by NumberofDays";
                $row = $this->cmodel->c_selectarray('shuttle_tariff', $where);
                foreach ($row as $val) {
                    $tariffId = $val['TariffId'];
                    $validDays = $val['NumberofDays'];
                    $validTrip = $val['NumberofTrips'];
                    $flatCost = $val['Tariff_Flat_Cost'];
                    $tariff_name = $val['Tariff_Name'];
                    $percentage = $val['Percentage'];
                    if ($validDays == 1) {
                        $sdate = $shedule_date;
                        $edate = $shedule_date;
                    }
                    $actual_fare = $flatCost * $validTrip;
                    $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'actual_fare' => round($actual_fare), 'fare' => $actual_fare, 'validDays' => $validDays, 'validTrip' => $validTrip, 'onedayFixedprice' => $oneday_fixed_price, 'tariff_cal_name' => $tariff_cal_name, 'percentage' => abs($percentage), 'start_date' => $sdate, 'end_date' => $edate, 'validity_days' => $validDays);
                }
            }
            // else if((strtolower($route_type) == strtolower("Fixed")) && ((strtolower($tariff_type)==strtolower("Monthly")) ))
            else {
                $lastDayThisMonth = date("Y-m-t", strtotime($tomorrow));
                if (date("w", strtotime($tomorrow)) == 6 || date("w", strtotime($tomorrow)) == 5) {
                    $sdate = date('Y-m-d', strtotime('next monday', strtotime($tomorrow)));
                    $edate = date("Y-m-t", strtotime($sdate));
                } else {
                    $sdate = $tomorrow;
                    $edate = $lastDayThisMonth;
                }
//                $days=strtotime($edate)-strtotime($sdate);
//                $validDays = (floor($days / (60*60*24) ))+1;                
//                if($validDays <= 5)
//                {
//                    $condition="NumberofDays='30' ";
//                    $sdate = date('Y-m-d',strtotime('first day of +1 month', strtotime($sdate)));
//                    $edate = date("Y-m-t",strtotime($sdate));
//                    $days=strtotime($edate)-strtotime($sdate);
//                    $validDays_Trip = (floor($days / (60*60*24) ))+1;
//                    $noof_trips=$this->cmodel->getWorkingDays($sdate, $edate,$branchid);
//                }
//                else if($validDays > 5 && $validDays < 15)
//                {
//                    $condition="NumberofDays between 1 and 15";
//                    $validDays_Trip = $validDays;
//                    $noof_trips=$this->cmodel->getWorkingDays($sdate, $edate,$branchid);
//                }
//                else
//                {
//                   // $condition="(NumberofDays='1' || NumberofDays='30')";
//                    $condition="NumberofDays between '1' and '30'";
//                    $validDays_Trip = $validDays;
//                    $noof_trips=$this->cmodel->getWorkingDays($sdate, $edate,$branchid);
//                }
//                if ((strtolower($route_type) == strtolower("Km"))) {
//                    $where1 = "BranchId='$branchid' and Active ='1' and Tariff_Type='Fixed' and Tariff_Calculation_Name='Monthly' and  '$dist'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and VehicleId='$vehicle_id' and NumberofDays='1'  order by NumberofDays";
//                } else {
//                    $where1 = "BranchId='$branchid' and Active ='1' and Tariff_Type='Fixed' and Tariff_Calculation_Name='Monthly' and  '$dist'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and VehicleId='$vehicle_id'  order by NumberofDays";
//                }
                $where1 = "BranchId='$branchid' and Active ='1' and Tariff_Type='Fixed' and Tariff_Calculation_Name='Monthly' and  '$dist'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and VehicleId='$vehicle_id'  order by NumberofDays";
                $row1 = $this->cmodel->c_selectarray('shuttle_tariff', $where1);
                foreach ($row1 as $val1) {
                    $tariffId = $val1['TariffId'];
                    $flatCost = $val1['Tariff_Flat_Cost'];
                    $tariff_name = $val1['Tariff_Name'];
                    $tarffcal_days = $val1['NumberofDays'];
                    $tariff_cal_name = $val1['Tariff_Calculation_Name'];
                    $baseKm = $val1['Tariff_Base_Km'];
                    $basCost = $val1['Tariff_Base_Cost'];
                    $extKmCost = $val1['Tariff_Ext_Km_Cost'];
                    $percentage = $val1['Percentage'];
                    $validTrip = $val1['NumberofTrips'];
                    if ($tarffcal_days == 1) {
                        $validDays_Trip = 1;
                        $noof_trips = 1;
                        $sdate = $shedule_date;
                        $edate = $shedule_date;
                        $validDays = 1;
                    } else {
                        $days1=strtotime($shedule_date)-strtotime($curdate);
                        $diffDays = (floor($days1 / (60*60*24) ));                               
                        $sdate=($diffDays==0||$diffDays==1)?$tomorrow:$shedule_date;
                       // $sdate = $tomorrow;//$tomorrow;$shedule_date
                        $days = $tarffcal_days - 1;
                        $edate = $tarffcal_days == 15 ? date('Y-m-d', strtotime($sdate . "  +$days day")) : date("Y-m-t", strtotime($sdate));
                        $noof_trips = $this->cmodel->getWorkingDays($sdate, $edate, $branchid);
                        $validDays_Trip = $noof_trips;
                        $diff = strtotime($edate) - strtotime($sdate);
                        $validDays = (floor($diff / (60 * 60 * 24))) + 1;
                    }

                    if ($flatCost == 0) {
                        $extKmAmt = 0;
                        $exkm = 0;
                        $fare = 0;
                        $percentAmt = 0;
                        $exkm = $dist - $baseKm;
                        if ($exkm > 0) {
                            $extKmAmt = $exkm * $extKmCost;
                        }
                        if ($percentage == 0) {
                            $fare = ($basCost + $extKmAmt) * $validTrip;
                        } else {
                            $percentAmt = ($basCost + $extKmAmt) * $percentage / 100;
                            $fare = ($basCost + $extKmAmt + $percentAmt) * $validTrip;
                        }
                        $actual_fare = ($basCost + $extKmAmt) * $validTrip;
                    } else {
                        if ($percentage == 0) {
                            $fare = $flatCost * $validTrip;
                        } else {
                            $percentAmt = ($flatCost) * $percentage / 100;
                            $fare = ($flatCost + $percentAmt) * $validTrip;
                        }
                        $actual_fare = $flatCost * $validTrip;
                    }
                    if ($branchid != 1) {
                        $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'actual_fare' => round($actual_fare), 'fare' => round($fare), 'validDays' => $noof_trips, 'validTrip' => $noof_trips, 'onedayFixedprice' => $oneday_fixed_price, 'tariff_cal_name' => $tariff_cal_name, 'percentage' => abs($percentage), 'start_date' => $sdate, 'end_date' => $edate, 'validity_days' => $validDays);
                    } else {
                        $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'actual_fare' => round($actual_fare), 'fare' => round($fare), 'validDays' => $validDays_Trip, 'validTrip' => $noof_trips, 'onedayFixedprice' => $oneday_fixed_price, 'tariff_cal_name' => $tariff_cal_name, 'percentage' => abs($percentage), 'start_date' => $sdate, 'end_date' => $edate, 'validity_days' => $validDays);
                    }
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_routepath($routeid, $approxptime, $approxdtime, $ct) {
        $element_array = array();
        //$where = "RouteId='$routeid' and Duration between '$approxptime' and '$approxdtime' order by Sno";
        $where = "RouteId='$routeid' order by Sno";
        $row = $this->cmodel->c_selectarray('shuttle_route_path', $where);
        if (count($row) > 0) {
            foreach ($row as $val) {
                $element_array[] = array('plat' => $val['Latitude'], 'plong' => $val['Longitude']);
            }
        }
        $element_arr = array('routepath' => $element_array);
        echo $this->output($element_arr, $ct);
    }

    public function paytmwalletmoney_withdraw($order_id, $ssotoken, $cust_id, $mobile_no, $fare, $stopid_p, $stopid_d) {
        $encdec = new EncdecPaytm();
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $cur_time = $getdate['cur_date_time']; //have to check $ssotoken is null or not,$cust_id is null or not
        $checkSum = "";
        $paramList = array();
        $retparamList=array();
        
//        $where = "PaymentNo='$order_id'";
//        $data1 = array('PaymentStatus' => 'S', 'UpdatedDatetime' => $cur_time);
//        $this->cmodel->c_update('shuttle_booking', $data1, $where);
//        echo $response = $this->paytmwithdraw_response($order_id, 0, $txnamt, $mobile_no, $stopid_p, $stopid_d);//        
//        exit;
        
        $paramList["MID"] = PAYTM_MID;
        $paramList["OrderId"] = $order_id;
        $paramList["IndustryType"] = PAYTM_INDUSTRY_TYPE_ID;
        $paramList["Channel"] = PAYTM_CHANNEL_ID_WEB;
        $paramList["TxnAmount"] = $fare;
        $paramList["AuthMode"] = "USRPWD";
        $paramList["ReqType"] = "WITHDRAW";
        $paramList["SSOToken"] = $ssotoken;
        $paramList["AppIP"] = $_SERVER["REMOTE_ADDR"];
        $paramList["Currency"] = "INR";
        $paramList["DeviceId"] = $mobile_no;
        $paramList["PaymentMode"] = "PPI";
        $paramList["CustId"] = $cust_id;
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CheckSum"] = $checkSum;
        $data = array('OrderId' => $order_id, 'CustId' => $cust_id, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => PAYTM_CHANNEL_ID_WEB, 'TxnAmount' => $fare, 'RequestType' => 'WITHDRAW', 'SsoToken' => $ssotoken,
            'CheckSumHash' => $checkSum, 'Currency' => 'INR', 'DeviceId' => $mobile_no, 'PaytmCustomerId' => $cust_id, 'AppIp' => $_SERVER["REMOTE_ADDR"], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
        $cnt = $this->cmodel->c_insert('shuttle_payment_request', $data);
        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_SERVER_MONEY_WITHDRAW, $paramList);
           // print_r($resparr);
            if (isset($resparr['ResponseCode']) && isset($resparr['ResponseMessage']) && isset($resparr['Status'])) {
                $txnid = $resparr['TxnId'];
                $orderid = $resparr['OrderId'];
                $txnamt = $resparr['TxnAmount'];
                $banktxnid=$resparr['BankTxnId'];
                $respcode=$resparr['ResponseCode'];
                $respmsg=$resparr['ResponseMessage'];
                $respsts=$resparr['Status'];
                $paymentmode=$resparr['PaymentMode'];
                $bankname=$resparr['BankName'];
                $respchecksum=$resparr['CheckSum'];
                $respcustid=$resparr['CustId'];
                $mbid=$resparr['MBID'];
                $respdata = array('OrderId' => $orderid, 'TxnId' => $txnid, 'TxnAmount' => $txnamt, 'BankTxnId' => $banktxnid,
                    'ResponseCode' => $respcode, 'ResponseMessage' => $respmsg, 'Status' => $respsts, 'PaymentMode' => $paymentmode,
                    'BankName' => $bankname, 'CheckSumHash' => $respchecksum, 'PaytmCustomerId' => $respcustid, 'Mbid' => $mbid, 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
                $cnt = $this->cmodel->c_insert('shuttle_payment_response', $respdata);
                $stsarr = array();
                $stsarr['MID'] = PAYTM_MID;
                $stsarr['ORDERID'] = $orderid;
                //$respconfirm = $encdec->callAPI(PAYTM_TXNSTATUS_URL,$stsarr);   
                
                //verify response check sum 
                $retparamList['TxnId']=$txnid;
                $retparamList['MID']=$resparr['MID'];
                $retparamList['OrderId']=$orderid;
                $retparamList['TxnAmount']=$txnamt;
                $retparamList['BankTxnId']=$banktxnid;
                $retparamList['ResponseCode']=$respcode;
                $retparamList['ResponseMessage']=$respmsg;
                $retparamList['Status']=$respsts;
                $retparamList['PaymentMode']=$paymentmode;
                $retparamList['BankName']=$bankname;
                $retparamList['CustId']=$respcustid;
                $retparamList['MBID']=$mbid;
                $isValidChecksum = $encdec->verifychecksum_e($retparamList, PAYTM_MERCHANT_KEY, $respchecksum);
                //echo "check sum ==".$isValidChecksum;
                
                if ($cnt > 0 && $isValidChecksum == "TRUE" && $respsts == 'TXN_SUCCESS' && $respcode == '01') {
                    // if ($cnt > 0 && $respconfirm['STATUS'] == 'TXN_SUCCESS' && $respconfirm['RESPMSG'] == 'Txn Successful.' && $respconfirm['RESPCODE'] == '01') {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'S', 'UpdatedDatetime' => $cur_time);
                    $this->cmodel->c_update('shuttle_booking', $data1, $where);
                    $response = $this->paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no, $stopid_p, $stopid_d);
                } else {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'F', 'UpdatedDatetime' => $cur_time);
                    $this->cmodel->c_update('shuttle_booking', $data1, $where);
                    $response = "Payment Failed";
                }
            } else {
                $respdata = array('OrderId' => $order_id, 'ResponseMessage' => $resparr['Error'], 'CreatedTime' => $cur_time);
                $cnt = $this->cmodel->c_insert('shuttle_payment_response', $respdata);
                $response = "Payment Failed";
                //change
            }
        }
        return $response;
    }

    public function paytmwithdraw_response($ORDERID, $txnid, $txnamt, $mobile_no, $stopid_p, $stopid_d) {
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];
        $empid = "";
        $stime = "";
        $etime = "";
        $ppoint = "";
        $dpoint = "";
        $tmode = "";
        $pamt = 0;
        $route_id = 0;
        $duration = 0;
        $branch_id = 0;
        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile_no, AES_ENCRYPT_KEY);
        $row = $dao->getshuttle_bookingdetails($ENC_MOBILE, $ORDERID);       
        if ($row) {
            $branch_id = $row['branchid'];
            $stime = $row['StartTime'];
            $etime = $row['EndTime'];
            $ppoint = $row['PickupPoint'];
            $dpoint = $row['DropPoint'];
            $tmode = $row['TravelMode'];
            $pamt = $row['TotalPaidAmt'];
            $dist = $row['PackageKm'];
            $tripid_p = $row['PickupRosterId'];
            $tripid_d = $row['DropRosterId'];
            $empid = $row['EmployeeId'];
            $sdate = $row['StartDate'];
            $enddate = $row['EndDate'];
            $duration = $row['NoofDays'];
            $emp_id = $row['EmployeeId'];
            $noofrides = $row['NoofRides'];
            $email = $this->cmodel->AES_DECRYPT($row['EMAIL'], AES_ENCRYPT_KEY);
            $ename = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
            $msg = $duration == 1 ? " 1 day" : $duration . " days " . $noofrides . " rides";
            $smsmessage = "Dear $ename, Thanks for your subscriptions $msg plan.For any queries contact 04440003002, enjoy the ride!";
            $this->cmodel->insert_sms($branch_id, 'TOPSCS', $mobile_no, $smsmessage);
            $ret = ($duration > 1) ? $this->shuttle_subscription_mail($ORDERID, $ename, $email, $empid, $mobile_no) : '';
            $date_to = $enddate;
            $date_from = strtotime($sdate);
            $date_to = strtotime($enddate);
            $message = $this->seats_confirmed_sts($tmode, $tripid_p, $tripid_d);
            if ($sdate >= $curdate && $enddate >= $curdate && $message == 'Confirmed') {

                // for ($i = $date_from; $i <= $date_to; $i+=86400) {
                for ($i = 0; $i < $duration; $i++) {
                    $shuttledate = date('Y-m-d', strtotime("+$i day", strtotime($sdate)));
                    if (date("w", strtotime($shuttledate)) == 6 || date("w", strtotime($shuttledate)) == 0) {
                        
                    } else {
                        if ($tmode == "B") {
                            $trip_fare = $pamt / 2;

                            //$seat_cost = $trip_fare / $noofrides;
                            $seat_cost = $pamt / $noofrides;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id = $row['Vehicle_ID'];
                                $route_name = $row['Route_Name'];
                                // $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist/2),$vehicle_id,'P',$dpoint,$route_name);
                                $dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'P', $dpoint, $route_name,$branch_id);
                            }
                            $row1 = $dao->get_routeid_sheduletime($tripid_d);
                            //print_r($row1);
                            if ($row1) {
                                $route_id = $row1['Schedule_Route_ID'];
                                $schedule_time = $row1['Schedule_Time'];
                                $vehicle_id = $row1['Vehicle_ID'];
                                $route_name = $row1['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_d, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'D', $dpoint, $route_name,$branch_id);
                            }
                        } else {
                            $seat_cost = $pamt / $noofrides;
                            $row = $dao->get_routeid_sheduletime($tripid_p);                           
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id = $row['Vehicle_ID'];
                                $route_name = $row['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, $dist, $vehicle_id, 'P', $dpoint, $route_name,$branch_id);
                            }
                        }
                    }
                }
                // $this->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                // $resp = "Payment Success.Your ride start from 1st Jan 2018 to 31st Jan 2018";
                $resp = "Payment Success";
                $response_message = ((intval(date('m', strtotime($curdate)))) == 12 && (intval(date('Y', strtotime($curdate)))) == 2017 && $branch_id != 1) ? $resp : "Payment Success";
            } else {
                $this->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                $response_message = "No seats avilable.Your amount has been refunded";
            }
        }
        return $response_message;
    }

    public function paytmaddmoney_request($orderid, $custid, $channelid, $txnamount, $website, $reqtype, $ssotoken, $mobileno) {
        //$dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $data = array('OrderId' => $orderid, 'CustId' => $custid, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => $channelid, 'WebSite' => $website, 'TxnAmount' => $txnamount, 'RequestType' => $reqtype, 'SsoToken' => $ssotoken, 'CreatedBy' => $mobileno, 'CreatedTime' => $curdate_time);
        $this->cmodel->c_insert('shuttle_payment_request', $data);
    }

    public function paytmaddmoney_response($ORDERID, $TXNID, $BANKTXNID, $TXNAMOUNT, $GATEWAYNAME, $RESPCODE, $RESPMSG, $BANKNAME, $PAYMENTMODE, $TXNDATE, $STATUS, $checkcumvalid, $mobileno) {
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $data = array('OrderId' => $ORDERID, 'TxnId' => $TXNID, 'BankTxnId' => $BANKTXNID, 'BankName' => $BANKNAME, 'GateWay' => $GATEWAYNAME, 'TxnAmount' => $TXNAMOUNT, 'TxnType' => $TXNTYPE, 'ResponseCode' => $RESPCODE, 'ResponseMessage' => $RESPMSG,
            'PaymentMode' => $PAYMENTMODE, 'TxnDate' => $TXNDATE, 'Status' => $STATUS, 'RefundAmount' => $REFUNDAMT, 'CreatedBy' => $mobileno, 'CreatedTime' => $curdate_time, 'CheckSumValid' => $checkcumvalid);
        $this->cmodel->c_insert('shuttle_payment_response', $data);
    }

    public function paytmwalletmoney_refund($order_id, $txnid, $refundamt, $mobile_no) {
        //{"MID":"NTLIND32103984777778","ORDERID":"NTL37523","REFUNDAMOUNT":"1","TXNID":"********","TXNTYPE":"REFUND","REFID":"36581RFIDNTL","CHECKSUM":"3Mv3pNrMhdXEaUYveoVzwzOiDhQsXxEkkKPZi3tfJRff4FpTPPUiE26W1MNGEC0x+mF+QnNKjM13+eiyc0n/iLHxBscfNxOWzTemHDXHKvM="}
        //{"MID":"NTLIND24898271393596","ORDERID":"************","REFUNDAMOUNT":"1.00","TXNID":**********,"TXNTYPE":"REFUND","REFID":"36648RFIDNTL","CHECKSUM":"y4kepWEt2wfXyyN2VyvuiWuR5cL5IQTR2h6QaEaHiV\/RbVmIZkb\/B2x9A2I82dNcwi1c4rrbG06nmkMaVcQjiA+zGyDIwPHG\/6DgHznF62g="}
       // $dao = new Shuttledao();
        $encdec = new EncdecPaytm();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];

        $checkSum = "";
        $paramList = array();
        $paramList["MID"] = PAYTM_MID;
        $paramList["ORDERID"] = $order_id;
        $paramList["REFUNDAMOUNT"] = $refundamt;
        $paramList["TXNID"] = $txnid;
        $paramList["TXNTYPE"] = 'REFUND';
        $paramList["REFID"] = rand(10000, 100000) . "RFIDNTL";
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CHECKSUM"] = $checkSum;
        $data = array('OrderId' => $order_id, 'TxnId' => $txnid, 'RefundAmount' => $refundamt, 'RequestType' => 'REFUND', 'RefId' => $paramList["REFID"],
            'CheckSumHash' => $checkSum, 'CreatedBy' => $mobile_no, 'CreatedTime' => $curdate_time);
        // echo json_encode($paramList);
        $cnt = $this->cmodel->c_insert('shuttle_payment_request', $data);

        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_REFUND_URL, $paramList);
            // print_r($resparr);
            if (isset($resparr['RESPCODE']) && isset($resparr['RESPMSG'])) {
                $respdata = array('OrderId' => $resparr['ORDERID'], 'TxnId' => $resparr['TXNID'], 'TxnAmount' => $resparr['TXNAMOUNT'], 'RefundAmount' => $resparr['REFUNDAMOUNT'],
                    'ResponseCode' => $resparr['RESPCODE'], 'ResponseMessage' => $resparr['RESPMSG'], 'Status' => $resparr['STATUS'], 'RefundId' => $resparr['REFUNDID'],
                    'RefId' => $resparr['REFID'], 'GateWay' => $resparr['GATEWAY'], 'CardIssuer' => $resparr['CARD_ISSUER'], 'CreatedTime' => $curdate_time, 'CreatedBy' => $mobile_no,);
                $cnt = $this->cmodel->c_insert('shuttle_payment_response', $respdata);
                // $confirmsts = $encdec->callAPI(PAYTM_REFUND_STS_URL, $paramList);
                $stsarr = array();
                $stsarr['MID'] = PAYTM_MID;
                $stsarr['ORDERID'] = $order_id;
                $stsarr['REFID'] = $paramList["REFID"];
                $respconfirm = $encdec->callAPI(PAYTM_REFUND_STS_URL, $stsarr);
                if ($cnt > 0 && $resparr['RESPCODE'] == '10' && $resparr['STATUS'] == 'TXN_SUCCESS') {
                    // if ($cnt > 0 && $respconfirm['RESPCODE'] == '10' && $respconfirm['RESPMSG'] == 'Refund Successful.' && $respconfirm['STATUS '] == 'TXN_SUCCESS') {
                    $where = "PaymentNo='$order_id'";
                    $this->db->set("RefundAmt", "if(RefundAmt is null," . $resparr['REFUNDAMOUNT'] . ",RefundAmt+" . $resparr['REFUNDAMOUNT'] . ")", FALSE);
                    $this->db->set("RefundReferanceId", "if(RefundReferanceId is null,'" . $resparr['REFUNDID'] . "',CONCAT(RefundReferanceId,',','" . $resparr['REFUNDID'] . "'))", FALSE);
                    $this->db->set("Active", "if(NoofRides-NoofCancelRides=1,3,2)", FALSE);
                    $this->db->set("NoofCancelRides", "NoofCancelRides+1", FALSE);
                    $this->db->set("UpdatedDatetime", $curdate_time);
                   // $this->db->set("Remarks", "Tariff amount revised excess amount refund");
                    $this->db->where($where);
                    $this->db->update('shuttle_booking');
                    //echo $this->db->last_query();
                }
            } else {
                $respdata = array('OrderId' => $order_id, 'ResponseCode' => $resparr['ErrorCode'], 'ResponseMessage' => $resparr['ErrorMsg'], 'CreatedTime' => $curdate_time, 'CreatedBy' => $mobile_no,);
                $cnt = $this->cmodel->c_insert('shuttle_payment_response', $respdata);
            }
        }
    }

    public function profile_update($emergencyno, $homeaddr, $homelat, $homelong, $eid, $officeaddr, $officelat, $officelong, $branch_id) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array = array();
        $data4 = array();
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];

        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' => $this->cmodel->AES_ENCRYPT($emergencyno, AES_ENCRYPT_KEY));
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        $data4 = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time);
        $data5 = array_merge($data2, $data3, $data4);
        $where = "EMPLOYEES_ID='$eid' and ACTIVE='1' and CATEGORY='Shuttle' and BRANCH_ID='$branch_id'";
        $cnt = $this->cmodel->c_update('employees', $data5, $where);
        //echo $this->db->last_query();
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 1, 'Message' => "Please try again");
        }
        return $element_array;
    }

    public function shuttleimage_upload($img) {
        $UPLOAD_DIR = "../shuttle_profile/";
        $data = base64_decode($img);
        $file = $UPLOAD_DIR . uniqid() . '.png';
        file_put_contents($file, $data);
        return $file;
    }

    public function forgot_password($mobile, $otpnew) {
        $smsmsg = "";
        $smssts = "";
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        if ($otpnew == "") {
            $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "MOBILE = '" . $ENC_MOBILE . "' AND ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $this->cmodel->c_selectrow('employees', $where);
            if ($row) {
                $ename = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                $eid = $row['EMPLOYEES_ID'];
                $branch_id = $row['BRANCH_ID'];
                $smsmsg = "Dear $ename, Please use $otp as login OTP in " . SMS_TITLE;
                //$smssts = $this->sendsms($mobile, $smsmsg);
                $data2 = array('BRANCH_ID' => $branch_id, "ORIGINATOR" => 'TOPSCS', 'RECIPIENT' => $mobile, 'MESSAGE' => $smsmsg, 'STATUS' => 'U', 'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime);
                $this->cmodel->c_insert('sms', $data2);

                $data1 = array('MOBILE_NO' => $mobile, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0,
                    'SMS_RESPONSE' => $smssts, 'OTP_CATEGORY' => 'Forgotpw', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime
                );
                $cnt = $this->cmodel->c_insert('otp_verification', $data1);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Please try again");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "Mobile no not registered");
            }
        } else {
            $otpsts = $dao->otp_verification($mobile, $otpnew, 'Forgotpw');
            if ($otpsts == 'true') {
                $where = "MOBILE_NO = '$mobile' and OTP='$otpnew' and OTP_CATEGORY='Forgotpw' and VERIFIED_STATUS=0 and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
                $data = array('VERIFIED_STATUS' => 1);
                $cnt = $this->cmodel->c_update('otp_verification', $data, $where);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Invalid OTP");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "OTP Expired");
            }
        }
        return $element_array;
    }

    public function generate_newpassword($mobile, $newpassword) {
        $element_array = array();
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        // $ENCRYP_PWD = $this->Encrypt_Script($newpassword, SECERT_KEY_NTL);
        $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($newpassword, AES_ENCRYPT_KEY);
        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
        $data1 = array(
            'password' => $ENCRYP_PWD,
            'updated_at' => $curdatetime
        );
        $cnt = $this->cmodel->c_update('employees', $data1, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Invalid OTP");
        }
        return $element_array;
    }

    public function change_password($oldpass, $newpass, $mobile) {
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        // echo "pw==". $ENCRYP_PWD=  $dao->AES_ENCRYPT($oldpass, AES_ENCRYPT_KEY);
        $ENCRYP_PWD_NEW = $this->cmodel->AES_ENCRYPT($newpass, AES_ENCRYPT_KEY);
        $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($oldpass, AES_ENCRYPT_KEY);
        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
        $data = array(
            'password' => $ENCRYP_PWD_NEW,
            'updated_at' => $curdatetime
        );
        $cnt = $this->cmodel->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Your old password is wrong");
        }
        return $element_array;
    }

    public function payment_history($eid, $bid) {
        $element_arr = array();
        $dao = new Shuttledao();
        $psts = "Failed";
        $respdate = "";
        $row1 = $dao->payment_history($eid, $bid);
        foreach ($row1 as $val) {
            $paidamt = 0;
            $respcode = $val['ResponseCode'];
            $respmsg = $val['ResponseMessage'];
            $rsts = $val['Status'];
            $respdate = $val['CreatedTime'];
            $payment_no = $val['PaymentNo'];
            $paidamt = $val['TotalPaidAmt'];
            //&& $respmsg == "Txn Successful."
            //&& $respmsg == "Refund Successful."
            if ($respcode == "01"  && $rsts == "TXN_SUCCESS") {
                $psts = "Paid";
                // $element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
            } else if ($respcode == "10"  && $rsts == "TXN_SUCCESS") {
                $psts = "Refunded";
                $paidamt = $val['RefundAmount'];
                // $refundamt=$val['RefundAmount'];
                $payment_no = $val['RefundReferanceId'];               
            } else {
                $psts = "Failed";
                //$element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
            }
            $element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
        }
        $element_array = array('payment_history' => $element_arr);
        return $element_array;
    }

    public function booking_details($eid, $bid) {
        $element_arr = array();
        $startDate = "";
        $endDate = "";
        $working_days = 0;
        $noof_rides = 0;
        $used_rides = 0;
        $noshow_ride = 0;
        $cancel_ride = 0;
        $addl_rides = 0;
        $extra_days = 0;
        $dao = new Shuttledao();
        $row1 = $dao->subscription_details($eid, $bid);
        foreach ($row1 as $val) {
            $bal_ride = 0;
            $startDate = $val['StartDate'];
            $endDate = $val['EndDate'];
            $travel_mode = $val['TravelMode'];
            $working_days = $this->getWorkingDays($startDate, $endDate);
            $noof_rides = $val['NoofRides'];
            $used_rides = $val['NoofRidesUsed'];
            $noshow_ride = $val['NoShowRides'];
            $cancel_ride = $val['NoofCancelRides'];
            $extra_days = $travel_mode == 'B' ? $working_days * 2 : $working_days;
            $addl_rides = $extra_days - $noof_rides;

            if ($cancel_ride >= $addl_rides && $cancel_ride > 0) {
                $bal_ride = $noof_rides - ($used_rides + $noshow_ride + ($cancel_ride - $addl_rides));
            } else {
                $bal_ride = $noof_rides - ($used_rides + $noshow_ride);
            }
            $element_arr[] = array('Sno' => $val['PaymentNo'], 'TotalAmount' => $val['TotalPaidAmt'], 'NoofDays' => $val['NoofDays'], 'NoofRides' => $noof_rides, 'NoofRidesUsed' => $used_rides, 'ExpiryDate' => $endDate, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'PlanCategory' => $val['Tariff_Name'], 'RidesRemaining' => $bal_ride);
        }
        $element_array = array('booking_details' => $element_arr);
        return $element_array;
    }

    public function booking_list($eid, $bid) {
        $element_arr = array();
        $element_arr1 = array();
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];

        $noofdays = 0;
        $reshedulests = "false";
        $cancelsts = "false";
        $spoint = "";
        $dpoint = "";
        $stime = "";
        $etime = "";
        $payment_no = "";
        $n_payment_no = "";
        $startDate = "";
        $endDate = "";
        $working_days = 0;
        $cancel_ride = 0;
        $noofrides = 0;
        $noofridesused = 0;
        $noshowrides = 0;
        $addl_rides = 0;
        $seat_status = 0;

        $cancelsts_property=$this->property_check('CANCEL_STATUS', $bid, 0);
        $obj = json_decode($cancelsts_property);
                       
        //UPCOMING
        $where = "EmployeeId='$eid' and BranchId='$bid' and PaymentStatus='S' and Active!=3 and EndDate >= '$curdate'";
        $row1 = $this->cmodel->c_selectarray('shuttle_booking', $where);
       
        foreach ($row1 as $val) {
            $rosterid = 0;
            $cabid = 0;
            $i = 0;
            $payment_no = $val['PaymentNo'];
            $noofrides = $val['NoofRides'];
            $noofridesused = $val['NoofRidesUsed'];
            $noshowrides = $val['NoShowRides'];
            $cancel_ride = $val['NoofCancelRides'];
            $spoint = $val['PickupPoint'];
            $dpoint = $val['DropPoint'];
            $plat = $val['PickLatitude'];
            $plong = $val['PickLongitude'];
            $routetype = $val['RouteType'];
            $dlat = $val['DropLatitude'];
            $dlong = $val['DropLongitude'];
            $noofdays = $val['NoofDays'];
            $startDate = $val['StartDate'];
            $endDate = $val['EndDate'];
            if ($noofdays > 1 && $cancel_ride > 0) {
                $working_days = $this->getWorkingDays($startDate, $endDate);
                $addl_rides = $working_days - $noofrides;
                $noofrides = $cancel_ride < $addl_rides ? $noofrides + $cancel_ride : $noofrides + $addl_rides;
            }
            $row2 = $dao->booking_list_dao($payment_no, '>=', 'sts.Seat_status!=4', 'shuttle_trip_seat');
            foreach ($row2 as $val1) {
                $traveldate = $val1['Schedule_Date'];
                $activests = $val1['status'];
                $tripid = $val1['Trip_ID'];
                $tripseatid = $val1['Trip_Seat_ID'];
                $seat_status = $val1['Seat_status'];
                $route = $val1['Schedule_Route_ID'];
                $trip_id = $val1['Trip_ID'];
                $travel_type = $val1['Travel_Type'];
                $stime = $val1['Stop_Time'];
                $rpsts = 0;
                $traveldate_time = $traveldate . " " . $stime;

                if ($i < $noofrides) {

                    if ($activests == 2 && $seat_status == 1 && strtotime($traveldate) == strtotime($curdate)) {
                        $route_id = "S" . $tripid;
                        $rosterrow = $dao->getroster_details($eid, $route_id, $traveldate,$bid);
                        foreach ($rosterrow as $result) {
                            $rosterid = $result['ROSTER_ID'];
                            $rpsts = $result['ROSTER_PASSENGER_STATUS'];
                            $rsts = $result['tracksts'];
                            $execute_time = $result['ACTUAL_START_TIME'];
                            $trip_time = date("Y-m-d H:i:s", strtotime("+" . TRIP_TIME . " hours", strtotime($execute_time)));
                            if (strtotime($curdate_time) <= strtotime($trip_time) || is_null($execute_time)) {
                                if (in_array($rpsts, unserialize(RP_PICKUP_DROP_NOSHOW))) {
                                    $tripsts = "Cancelled";
                                } else {
                                    if (in_array($rsts, unserialize(R_CAB_TRACKING))) {
                                        $tripsts = "Upcoming";
                                        $rosterid = $result['ROSTER_ID'];
                                        $cabid = $result['CAB_ID'];
                                    } else if (in_array($rsts, unserialize(R_TRIP_CLOSE_HISTORY)) || $rsts > 1000) {
                                        $tripsts = "Completed";
                                        $rosterid = 0;
                                    } else {
                                        $tripsts = "Upcoming";
                                        $rosterid = $rosterid;
                                    }
                                }
                            } else {
                                $tripsts = (in_array($rpsts, unserialize(RP_PICKUP_DROP_NOSHOW))) ? 'Cancelled' : 'Completed';
                                $rosterid = 0;
                            }
                        }
                    } else {
                        $rosterid = 0;
                        //echo "trip seat id==".$tripseatid."seat sts==".$seat_status."travel date==".$traveldate."<br>";
                        if (((strtotime($curdate_time) > strtotime($traveldate_time)) && $seat_status == 1)) {
                            $tripsts = "Completed";
                        } else {
                            if ($seat_status == 0) {
                                $tripsts = "Noshow"; //Noshow
                            } else if ($seat_status == 2) {
                                $tripsts = "Completed";
                            } else if ($seat_status == 3) {
                                $tripsts = "Cancelled";
                            } else if ($seat_status == 1) {
                                $tripsts = "Upcoming";
                            } else {
                                $tripsts = "Reshedule";
                            }
                        }
                    }

                    $seconds = strtotime($traveldate_time) - strtotime($curdate_time);
                    $days = floor($seconds / 86400);
                    $hours = floor(($seconds - ($days * 86400)) / 3600);
                    $minutes = floor(($seconds - ($days * 86400) - ($hours * 3600)) / 60);
                    if ($tripsts == "Upcoming" && $seat_status == 1) {
                        //enable cancel and reschedule discuss with md     
                        if($obj->status==1){
                            $enable_days=$obj->days;
                            $enable_hrs=$obj->hours;
                            if ($noofdays == 1 && $days >= $enable_days) {
                                $cancelsts = "true";
                                $reshedulests = 'false';                                
                            }
                            //else if($noofdays == 1 && $days == 0 && $hours >=1)                    
                            else if (($noofdays == 1 && $days == 0 && $hours >= 0 ) && (in_array($rpsts, unserialize(RP_CREATED)) || $rpsts == 0)) {
                                $cancelsts = "true";                               
                                $reshedulests = 'false';
                            }
                            // else if(($noofdays > 1 && $hours >= 6) || ($days >= 1))
                            else if ($noofdays > 1 && $days >= 0) {
                                $reshedulests = ($days == 0 && $hours >= 6) ? 'true' : ($days > 0 && $days < 2) ? 'true' : 'false';
                                $cancelsts = "true";                                
                            } else {
                                $cancelsts = "false";
                                $reshedulests = 'false';
                            }
                        }
                        else
                        {
                            $cancelsts = "false";
                            $reshedulests = 'false';
                        }

                        $element_arr[] = array('Sno' => $i, 'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $cancelsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype, 'TravelType' => $travel_type, 'RouteNo' => $route, 'TripId' => $trip_id);
                    } else if ($tripsts != 'Reshedule') {
                        // echo "else travel date==".$traveldate_time=$traveldate." ".$stime;     
                        $element_arr1[] = array('Sno' => $i, 'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $tripsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype, 'TravelType' => $travel_type, 'RouteNo' => $route, 'TripId' => $trip_id);
                    }
                    $i++;
                }
            }
        }

        $where = "EmployeeId='$eid' and BranchId='$bid' and PaymentStatus='S' and Active!=3 and EndDate < '$curdate'";
        $this->db->limit(5);
        $this->db->order_by("Sno desc");
        $row1 = $this->cmodel->c_selectarray('shuttle_booking', $where);
        // echo $this->db->last_query();
        foreach ($row1 as $val) {
            $rosterid = 0;
            $cabid = 0;
            $i = 0;
            $payment_no = $val['PaymentNo'];
            $noofrides = $val['NoofRides'];
            $noofridesused = $val['NoofRidesUsed'];
            $noshowrides = $val['NoShowRides'];
            $cancel_ride = $val['NoofCancelRides'];
            $spoint = $val['PickupPoint'];
            $dpoint = $val['DropPoint'];
            $plat = $val['PickLatitude'];
            $plong = $val['PickLongitude'];
            $routetype = $val['RouteType'];
            $dlat = $val['DropLatitude'];
            $dlong = $val['DropLongitude'];
            $noofdays = $val['NoofDays'];
            $startDate = $val['StartDate'];
            $endDate = $val['EndDate'];
            $row2 = $dao->booking_list_dao($payment_no, '<', 'sts.Seat_status!=4', 'shuttle_trip_seat'); //shuttle_trip_seat_history
            foreach ($row2 as $val1) {
                $traveldate = $val1['Schedule_Date'];
                $activests = $val1['status'];
                $tripid = $val1['Trip_ID'];
                $tripseatid = $val1['Trip_Seat_ID'];
                $seat_status = $val1['Seat_status'];
                $route = $val1['Schedule_Route_ID'];
                $trip_id = $val1['Trip_ID'];
                $travel_type = $val1['Travel_Type'];
                $stime = $val1['Stop_Time'];
                $rpsts = 0;
                $traveldate_time = $traveldate . " " . $stime;
                if ((strtotime($curdate_time) > strtotime($traveldate_time)) && $seat_status == 1) {
                    $tripsts = "Completed";
                } else if ($seat_status == 2) {
                    $tripsts = "Completed";
                } else if ($seat_status == 0) {
                    $tripsts = "Noshow";
                } else if ($seat_status == 3) {
                    $tripsts = "Cancelled";
                }
                $element_arr1[] = array('Sno' => $i, 'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $tripsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype, 'TravelType' => $travel_type, 'RouteNo' => $route, 'TripId' => $trip_id);
            }
        }
        $b_array = $this->msort('asc', $element_arr, array('TravelDate', 'StartTime'));
        $c_array = $this->msort('desc', $element_arr1, array('TravelDate', 'StartTime'));
        $element_array = array('booking_list' => $b_array, 'completed_list' => $c_array);
        return $element_array;
    }

    public function booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category, $branchid) {
        $element_array = array();
        // $branchid = 1;
        $days = 0;
        $hours = 0;
        $ret = 0;
        $hrs = 0;
        $canceldate_time = $traveldate . " " . $stime;
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        if ($noofdays == 1) {
            if (strtotime($canceldate_time) >= strtotime($curdate_time)) {
                $seconds = strtotime($canceldate_time) - strtotime($curdate_time);
                $days = floor($seconds / 86400);
                $hours = floor(($seconds - ($days * 86400)) / 3600);
                $minutes = floor(($seconds - ($days * 86400) - ($hours * 3600)) / 60);
                if ($days >= 1) {
                    $hours = 22;
                    $ret = $this->property_check('CANCELLATION POLICY ONEDAY', $branchid, $hours);
                    $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_ONDAY, $category);
                } else {
                    $hrs = $hours == 0 && $minutes < 60 ? 0 : $hours;
                    $ret = $this->property_check('CANCELLATION POLICY ONEDAY', $branchid, $hrs);
                    $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_ONDAY, $category);
                }
            } else {
                $element_array = array('status' => 'false', 'message' => 'Invalid Date');
            }
        } else {
            $seconds = strtotime($canceldate_time) - strtotime($curdate_time);
            $days = floor($seconds / 86400);
            $hours = floor(($seconds - ($days * 86400)) / 3600);
            $hrs = $days >= 1 ? 22 : $hours;
            $ret = $this->property_check('CANCELLATION POLICY REGULAR', $branchid, $hrs);
            $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_REGULAR, $category);
        }
        return $element_array;
    }

    private function property_check($property_name, $branch_id, $hours) {
        $ret = 0;
        $dao = new Shuttledao();
        //get current date time
        $getdate = $this->cmodel->get_datetime();
        $intime = $getdate['cur_time'];
        $where = "PROPERTIE_NAME='$property_name' and ACTIVE='1' and BRANCH_ID='$branch_id'";
        $row = $this->cmodel->c_selectrow('properties', $where);
        if ($row) {
            if ($property_name == "CANCELLATION POLICY ONEDAY") {
                $timevalue = unserialize($row['PROPERTIE_VALUE']);
                foreach ($timevalue as $test) {
                    list($t1, $t2, $t3) = $test;
                    $ret1 = $this->check_time($t1, $t2, $hours) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $ret = $t3;
                    }
                }
            } else if ($property_name == "CANCELLATION POLICY REGULAR") {
                $timevalue = unserialize($row['PROPERTIE_VALUE']);
                foreach ($timevalue as $test) {
                    list($t1, $t2, $t3) = $test;
                    $ret1 = $this->check_time($t1, $t2, $hours) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $ret = $t3;
                    }
                }
            }
            else if ($property_name == "BOOKING_TIME_DIFFERENCE") {
                $ret = $row['PROPERTIE_VALUE'];               
            }
            else if ($property_name == "OFFICE_ADDRESS") {
                $ret = $row['PROPERTIE_VALUE'];               
            }
            else if ($property_name == "CANCEL_STATUS") {
                $ret = $row['PROPERTIE_VALUE'];               
            }
        }
        return $ret;
    }

    public function cancellation_policy($transid, $eid, $retpercentage, $duration, $category) {
        $arr = array();
        $paidamount = 0;
        $refundamount = 0;
        $noof_rides = 0;
        $used_rides = 0;
        $refund = 0;
        $working_days = 0;
        $cancel_ride = 0;
        $addl_rides = 0;
        $startDate = "";
        $endDate = "";
        $dao = new Shuttledao();
        if ($duration == SHUTTLE_TRIP_ONDAY) {
            $row = $dao->cancellation_policy($transid, $eid, $duration);
            if ($row) {
                $paidamount = $row['paidamt'];
                $travelmode = $row['TravelMode'];
                $refundamount = ($paidamount * $retpercentage) / 100;
                $refund = $travelmode == 'B' ? $refundamount / 2 : $refundamount;
                $arr = array('status' => 'true', 'PaidAmount' => $paidamount, 'RefundAmount' => $refund, 'category' => $category);
            }
        } else {
            $row = $dao->cancellation_policy($transid, $eid, $duration);
            if ($row) {
                $startDate = $row['StartDate'];
                $endDate = $row['EndDate'];
                $working_days = $this->getWorkingDays($startDate, $endDate);
                $noof_rides = $row['NoofRides'];
                $used_rides = $row['NoofRidesUsed'];
                $noshow_ride = $row['NoShowRides'];
                $cancel_ride = $row['NoofCancelRides'];
                $addl_rides = $working_days - $noof_rides;
                if ($cancel_ride >= $addl_rides) {
                    $bal_ride = $noof_rides - ($used_rides + $noshow_ride + ($cancel_ride - $addl_rides));
                } else {
                    $bal_ride = $noof_rides - ($used_rides + $noshow_ride);
                }
                $arr = array('status' => 'true', 'available_rides' => $bal_ride, 'ride_expiry_date' => $row['EndDate'], 'category' => $category);
            }
        }
        return $arr;
    }

    //#395167@@#94715@@#2017-07-03@@#10:22:10@@#30@@#24601@@#0@@#0@@#2@@#0.0@@#0.0

    public function booking_cancel($trans_id, $eid, $traveldate, $stime, $noofdays, $roster_id, $paidfare, $refundfare, $confirmsts, $emplat, $emplong, $branch_id, $mobileno) {
        $element_array = array();
        $dao = new Shuttledao();
        //get current date time
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $stsarr = array('confirm_status' => $confirmsts);
        switch ($confirmsts) {
            case 1:
                $bid= empty($branch_id)?1:$branch_id;
                if ($noofdays > 1) {
                    $category = 2; //regular
                    $element_array = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category, $bid);
                } else {
                    $category = 1; //oneday
                    $element_array = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category, $bid);
                }
                return array_merge($element_array, $stsarr);
                break;
            case 2:
                if ($noofdays > 1) {
                    $category = 2; //regular
                    $element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                } else {
                    $category = 1; //oneday
                    //payment return here
                    $element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                    $paymentdet = $dao->getTxnid($trans_id);
                    if ($paymentdet && $element_array['status'] == 'true') {
                        $orderid = $paymentdet['Payment_Token'];
                        $txnid = $paymentdet['TxnId'];
                        $this->paytmwalletmoney_refund($orderid, $txnid, $refundfare, $mobile_no);
                    }
                    //$element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                }
                return $element_array;
                break;
            default:
                $element_array = "False5";
                return $element_array;
                break;
        }
    }

    public function booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category) {
        $element_array = array();
        $dao = new Shuttledao();
        //get current date time

        $cnt = $dao->shuttle_booking_update($trans_id, $category, $roster_id, $eid, 3); //cancel
        if ($cnt == 1) {
            $element_array = array('status' => 'true', 'confirm_status' => $confirmsts, 'category' => $category);
        } else {

            $element_array = array('status' => 'false', 'confirm_status' => $confirmsts, 'category' => $category);
        }
        return $element_array;

        //noshow in roster_passenger
        //cancel in shuttle_booking_trans
    }

//14203@@#21813@@#354804@@#14378@@#109@@#36183@@#0.0@@#0.0"
    public function booking_reshedule($old_trip_id, $roster_id, $trans_id, $new_trip_id, $stop_id, $eid, $emplat, $emplong) {
        $dao = new Shuttledao();
        $sts = "false";
        $element_array = array();
        $category = 1;
        //echo $old_trip_id,$roster_id,$trans_id,$new_trip_id,$stop_id, $eid, $emplat, $emplong;
        //exit;
        $ret = $dao->shuttle_booking_update($trans_id, $category, $roster_id, $eid, 4); //4-'reschedule

        if ($ret == 1) {
            $dao->getpayment_tokenno($new_trip_id, $trans_id, $stop_id, $eid);
            $sts = 'true';
        }

        $element_array = array('status' => $sts);
        return $element_array;
    }

    private function check_time($t1, $t2, $tn) {
        if ($t2 >= $t1) {
            return $t1 <= $tn && $tn <= $t2;
        } else {
            return !($t2 <= $tn && $tn <= $t1);
        }
    }

    public function reason($bid) {
        $arr = array();
        //creae object for driver dao class
       // $dao = new Shuttledao();
        try {
            //get reason master values
            $where = "ACTIVE = '1' and BRANCH_ID='$bid' and CATEGORY='ShuttleFeedback'";
            $row = $this->cmodel->c_selectarray('reason_master', $where);
            foreach ($row as $val) {
                $arr[] = array('sno' => $val['REASON_ID'], 'reason' => $val['REASON'], 'rstatus' => $val['CATEGORY']);
            }
            $element_array = array('status' => 1, 'reason_list' => $arr);
            return $element_array; //return response to controller
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

      
    public function shuttle_support() {
        $tmp = array();
       // $dao = new Shuttledao();
        $where = "ACTIVE=1 and CATEGORY='Shuttle' and BRANCH_ID='1'";
        $row = $this->cmodel->c_selectarray('reason_master', $where);
        foreach ($row as $arg) {
            $tmp[$arg['SUPPORT_CATEGORY']][] = $arg['SUB_SUPPORT_CATEGORY'] . '@@#' . $arg['REASON'] . '@@#' . $arg['REASON_ID'];
        }
        $output = array();
        foreach ($tmp as $type => $labels) {
            $output[] = array(
                'category' => $type,
                'sub_category' => $labels
            );
        }
        // echo json_encode($output);
        return array('Apiresponse' => $output);
    }

    public function shuttle_support_log($bid, $empid, $reasonid, $remarks) {
        $element_array = array();
        $sts = "false";
      //  $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        $data = array('EMPLOYEE_ID' => $empid, 'REASON_ID' => $reasonid, 'REMARKS' => $remarks, 'PROCESS_TIME' => $curdatetime, 'BRANCH_ID' => $bid);
        $cnt = $this->cmodel->c_insert('support_log', $data);
        if ($cnt > 0) {
            $sts = "true";
        }
        $element_array = array('Apiresponse' => $sts,'message'=>'Your request has been received.We will get back to you within two working days');
        return $element_array;
    }

    public function shuttle_issue() {
        $element_arr = array(); //shuttle_issue
        $where = "Active=1";
       // $dao = new Shuttledao();
        $row = $this->cmodel->c_selectarray('shuttle_issue', $where);
        foreach ($row as $val) {
            $element_arr[] = array('Sno' => $val['Sno'], 'IssueName' => $val['IssueName'], 'IssueSubName' => $val['IssueSubName'], 'IssueDetails' => $val['IssueDetails']);
        }
        $element_array = array('issue_det' => $element_arr);
        return $element_array;
    }

    public function emp_app_panic($empid, $cab_id, $roster_id, $lat, $lng, $location, $branch_id) {
        $element_array = array();
        //create object
        //$dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $cur_time = $getdate['cur_date_time'];

        if ($roster_id == 0) {
            $data = array('EMPLOYEE_ID' => $empid, 'BRANCH_ID' => $branch_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        } else {
            $data = array('EMPLOYEE_ID' => $empid, 'BRANCH_ID' => $branch_id, 'ROSTER_ID' => $roster_id, 'CAB_ID' => $cab_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        }
        $cnt = $this->cmodel->c_insert('panic_alert', $data);
        if ($cnt > 0) {
            $element_array = array('status' => 'true');
        } else {
            $element_array = array('status' => 'false');
        }
        return $element_array;
    }

    public function insert_route_master($duration, $routepath, $route_type, $routeid, $branchid, $travel_type, $route_name, $ct) {
       // $dao = new Shuttledao();
       // $getdate = $this->cmodel->get_datetime();
       // $cur_time = $getdate['cur_date_time'];
       // $this->load->model('ShuttleElasticmdl');
        /* $tottime = $this->sec_to_time($duration);
          $this->load->model('ShuttleElasticmdl');
          $data = array('StartPoint' => $startpoint, 'EndPoint' => $endpoint, 'Start_Latitude' => $startlat, 'Start_Longitude' => $startlong, 'End_Latitude' => $endlat,
          'End_Longitude' => $endlong, 'StartTime' => $stime, 'EndTime' => $etime, 'Travel_Duration' => $tottime, 'Process_Datetime' => $cur_time);
          $cnt = $dao->c_insert('shuttle_route_master', $data);
          $id = $this->db->insert_id();
          if ($cnt > 0) {
          $userColData = json_decode($routepath, true);
          $path = $userColData['RoutePath'];
          $this->ShuttleElasticmdl->pathInsert($id, '1', 4107, $path, $route_type);
          } */
        $userColData = json_decode($routepath, true);
        $path = $userColData['RoutePath'];
        $this->semodel->pathInsert($routeid, $branchid, $duration, $path, $route_type, $travel_type, $route_name);
    }
    
    public function insert_route_master_driver($duration, $routepath, $route_type, $routeid, $branchid,$travel_type, $ct)
    {
        $userColData = json_decode($routepath, true);
        $path = $userColData['RoutePath'];
        $this->semodel->pathInsert_Driver($routeid, $branchid, $duration, $path, $route_type, $travel_type);
        
    }

    public function shuttle_notification($eid, $bid) {
        $element_arr = array();
        $dao = new Shuttledao();
        $row = $dao->notification($eid, $bid);
        foreach ($row as $val) {
            $element_arr[] = array('Heading' => $val['heading'], 'Message' => $val['message'], 'DateTime' => $val['created_at']);
        }
        $element_array = array('notification' => $element_arr);
        return $element_array;
    }

    public function shuttle_invoice($bookingno) {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function shuttle_termscondition() {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function emp_logout($eid) {
        return $element_array = array('status' => 'true');
    }

    //get employee feedback 
    //insert feedback log table
    public function emp_feedback($roster_id, $emp_id, $feedback, $comments) {
        $ret = false;
        try {
            //get current date time
            $getdate = $this->cmodel->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            //$feedbackmsg = explode('|', $feedback);
            $data = array('ROSTER_ID' => $roster_id, 'EMPLOYEE_ID' => $emp_id, 'FEEDBACK1' => $feedback, 'COMMENTS' => $comments, 'CREATED_DATE' => $curdatetime);
            $cnt = $this->cmodel->c_insert('feedback_log', $data); //insert feedback log table
            if ($cnt > 0) {
                $ret = true;
            }
            $element_array = array('status' => $ret);
            return $element_array; //return response to controller
        } catch (Exception $e) {
            // echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    //--new table search and booking below//
    
    //cts with google api

    public function shuttle_searchroute_dlf($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
        //print_r($pickarray);
        //print_r($droparray);
        //exit;
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            $routeid_name = "";
            $travel_type = "";
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate, $branchid);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $routeid_name = $row['Route_ID'];
                        $travel_type = $row['Travel_Type'];
                        $tariff_type = $row['Tariff_Type'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
                            $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                            $traveltime = $traveldist[1];
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                        } else {
                            // if($shedule_date==$curdate && $stime > $curtime)
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $sts = "";                                
                                if ($route_name == TRIP_P) {
                                    $sts = ($a_p_picktime <= $seat_count_time && $seats_used > 0) ? 'true' :
                                            ($a_p_picktime > $seat_count_time && $seats_used >= 0) ? 'true' : 'false';
                                } else {
                                    $sts = 'true';
                                }
                                $shuttletime = "ANext";                               
                                if ($sts == 'true') 
                                {
                                    $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    $traveldist = $this->GetDrivingKm($origin, $destination);
                                    $package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                    $traveltime = $traveldist['time'];
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                        'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                                }
                            } else if ($shedule_date > $curdate) {
                                $days=strtotime($shedule_date)-strtotime($curdate);
                                $diffDays = (floor($days / (60*60*24) )); 
                                $shuttletime=$diffDays==0?'ANext':$diffDays==1?'BTomorrow':'C'.date('l', strtotime($shedule_date));
                                $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = $this->GetDrivingKm($origin, $destination);
                                $package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                $traveltime = $traveldist['time'];
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate, $branchid);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $routeid_name = $row['Route_ID'];
                        $travel_type = $row['Travel_Type'];
                        $tariff_type = $row['Tariff_Type'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
                            $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                            $traveltime = $traveldist[1];
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                        } else {
                            if ($shedule_date == $curdate && $stime > $curtime) {
                                $shuttletime="ANext";
                                $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                $traveltime = $traveldist[1];
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            } else if ($shedule_date > $curdate) {
                                $days=strtotime($shedule_date)-strtotime($curdate);
                                $diffDays = (floor($days / (60*60*24) )); 
                                $shuttletime=$diffDays==0?'ANext':$diffDays==1?'BTomorrow':'C'.date('l', strtotime($shedule_date));
                               
                                $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                $traveltime = $traveldist[1];
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        $respmsg="Currently we are not operating between your searched place and we will try to start our shuttle service for your request";
        $resptitle="No rides available";
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => $respmsg, 'title'=>$resptitle);
                //$element = array('status' => 0, 'Message' => 'No rides avia');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                //$element = array('status' => 0, 'Message' => 'No routes');
                $element = array('status' => 0, 'Message' => $respmsg, 'title'=>$resptitle);
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }
    
     public function shuttle_searchroute($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        
//        if($branchid==1)
//        {
//            $this->shuttle_searchroute_dlf($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category);
//        }
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->semodel->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->semodel->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->semodel->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->semodel->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
//        print_r($pickarray);
//        print_r($droparray);
//        exit;
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            $routeid_name = "";
            $travel_type = "";
            $diffDays=0;
//           echo json_encode($output);
//          exit;
            foreach ($output as $val) {
                $p_time = $this->cmodel->sec_to_time($val['APROX_PICK']);
                $d_time = $this->cmodel->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d=$val['DROP_STOP_ID'];
               
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate, $branchid);                    
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                       // $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $routeid_name = $row['Route_ID'];
                        $travel_type = $row['Travel_Type'];
                        $tariff_type = $row['Tariff_Type'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $p_order = $dao->get_route_order($route_id,$stop_id);
                        $d_order = $dao->get_route_order($route_id,$stop_id_d);
                        $p_route_order=$p_order['Route_Order'];
                        $d_route_order=$d_order['Route_Order'];
                        $p_time=$p_order['Stop_Time'];
                        $d_time=$d_order['Stop_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                       
                        //$booking_time=$this->property_check('BOOKING_TIME_DIFFERENCE', $branchid, 0);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));                       
                       // $seat_count_time = $this->cmodel->sum_the_time($curtime,$booking_time); 
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
//                            $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
//                            $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk'); 
                            $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                            $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                            $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                            $tdist=round($traveldist['dist']);
                            $package = $this->tariff_cal($tdist, $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                            $traveltime=$dao->calculate_time($route_id, $p_route_order, $d_route_order);                             
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $tdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                        } else {                             
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $shuttletime = "ANext";
                                if ($route_name == TRIP_P) {
                                    $sts = ($a_p_picktime <= $seat_count_time && $seats_used > 0) ? 'true' :
                                            ($a_p_picktime > $seat_count_time && $seats_used >= 0) ? 'true' : 'false';
                                } else {
                                    $sts = 'true';
                                }                                   
                                if ($sts == 'true') {
//                                    $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
//                                    $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                                    $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                    $tdist=round($traveldist['dist']);
                                    $package = $this->tariff_cal($tdist, $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                    $traveltime=$dao->calculate_time($route_id, $p_route_order, $d_route_order);                                    
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                        'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $tdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                                }
                            } else if ($shedule_date > $curdate) {
                                $days=strtotime($shedule_date)-strtotime($curdate);
                                $diffDays = (floor($days / (60*60*24) )); 
                                
                                $provinces = array (
                                    0 => 'ANext',
                                    1 => 'BTomorrow',
                                    2 => 'C'.date('l', strtotime($shedule_date))
                                );

                                $shuttletime = isset($provinces[$diffDays]) ? $provinces[$diffDays] : 'D'.date("jS M Y", strtotime($shedule_date));
                               
                                //if($branchid==1){ 
                              //  $shuttletime=($diffDays==0)?'ANext':($diffDays==1)?'BTomorrow':($diffDays==2)?'C'.date('l', strtotime($shedule_date)):date("jS M Y", strtotime($shedule_date));
                              //  $shuttletime=($diffDays==0)?'ANext':($diffDays==1)?'BTomorrow':($diffDays > 2)?date("jS M Y", strtotime($shedule_date)):'C'.date('l', strtotime($shedule_date));
//                                }
//                                else{
//                                    $shuttletime='B2 Apr 2018';
//                                }
//                                $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
//                                $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                                $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                $tdist=round($traveldist['dist']);
                                $package = $this->tariff_cal($tdist, $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                $traveltime=$dao->calculate_time($route_id, $p_route_order, $d_route_order);
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $tdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->cmodel->sec_to_time($val['APROX_PICK']);
                $d_time = $this->cmodel->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d=$val['DROP_STOP_ID'];                
               
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate, $branchid);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                       // $stop_id = $val['PICK_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $routeid_name = $row['Route_ID'];
                        $travel_type = $row['Travel_Type'];
                        $tariff_type = $row['Tariff_Type'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];                        
                        $p_order = $dao->get_route_order($route_id,$stop_id);
                        $d_order = $dao->get_route_order($route_id,$stop_id_d);
                        $p_route_order=$p_order['Route_Order'];
                        $d_route_order=$d_order['Route_Order'];
                        $p_time=$p_order['Stop_Time'];
                        $d_time=$d_order['Stop_Time'];
                        
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
//                            $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
//                            $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                            $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                            $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                            $tdist=round($traveldist['dist']);
                            $package = $this->tariff_cal($tdist, $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                            $traveltime=$dao->calculate_time($route_id, $p_route_order, $d_route_order);                             
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $tdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                        } else {                          
                           
                            if ($shedule_date == $curdate && $stime > $curtime) {   
                                $shuttletime='ANext';
//                                $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
//                                $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                                $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                $tdist=round($traveldist['dist']);
                                $package = $this->tariff_cal($tdist, $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                $traveltime=$dao->calculate_time($route_id, $p_route_order, $d_route_order);                                
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $tdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            } else if ($shedule_date > $curdate) {
                                $days=strtotime($shedule_date)-strtotime($curdate);
                                $diffDays = (floor($days / (60*60*24) )); 
                               // if($branchid==1){                                
                                 $provinces = array (
                                    0 => 'ANext',
                                    1 => 'BTomorrow',
                                    2 => 'C'.date('l', strtotime($shedule_date))
                                 );
                                $shuttletime = isset($provinces[$diffDays]) ? $provinces[$diffDays] : 'D'.date("jS M Y", strtotime($shedule_date));
//                              
//                                $dist = $this->cmodel->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
//                                $dist1 = $this->cmodel->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                                $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                $tdist=round($traveldist['dist']);
                                $package = $this->tariff_cal($tdist, $ttype, $route_type, $vehicle_id, 0, $branchid, $tariff_type, $shedule_date);
                                $traveltime=$dao->calculate_time($route_id, $p_route_order, $d_route_order);                                 
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $d_time, 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                                    'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $tdist, 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeid_name' => $routeid_name, 'travel_type' => $travel_type, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        $respmsg="Currently we are not operating between your searched place and we will try to start our shuttle service for your request";
        $resptitle="No rides available";
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->semodel->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => $respmsg, 'title'=>$resptitle);
            }
        } else {
            if (count($element_arr) == 0) {
                $this->semodel->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => $respmsg, 'title'=>$resptitle);
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }


    public function shuttle_booking_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, $noride, $ffare, $distance) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
        $tariff_cal_name = "";
       // $this->load->model('ShuttleElasticmdl');

        if ($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0 || $dlat == 0 || $dlong == 0 || $ssotoken == 'null') {
            $element_array = array('status' => 0, 'message' => 'Subsciption Failed');
            return $element_array;
            exit;
        }
        if ($noofdays == 1 && $branch_id != 1) {
            $element_array = array('status' => 0, 'message' => 'Subsciption only allowed');
            return $element_array;
            exit;
        }
         if ($noofdays > 1 && $branch_id != 1) {
            $element_array = array('status' => 0, 'message' => 'Payment start from 10 Jan');
            return $element_array;
            exit;
        }
        if ($noofdays > 1) {
            $tomorrow = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            $shuttledate = (date("w", strtotime($tomorrow)) == 6 || date("w", strtotime($tomorrow)) == 5) ? date('Y-m-d', strtotime('next monday', strtotime($tomorrow))) : $tomorrow;
            $totdays = $noofdays - 1;
            $enddate = date('Y-m-d', strtotime("+$totdays day", strtotime($shuttledate)));
            //$enddate = date('Y-m-d', strtotime("+$noofdays day", strtotime($shuttledate)));
            $where = "EmployeeId='$eid' and '$shuttledate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $this->cmodel->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if ($ptag == "Next") {
                if ($ttype == "B" && $dtag == "Tomorrow") {
                    $shuttledate = $curdate;
                    $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                } else {
                    $shuttledate = $curdate;
                    $enddate = $curdate;
                }
            } else if ($ptag == "Tomorrow") {
                $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            } else {
                $shuttledate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $i = $dao->check_noservice_date($shuttledate);
                if ($i == 0) {
                    $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($shuttledate)));
                    $enddate = $shuttledate;
                }
            }
            if (date("w", strtotime($shuttledate)) == 6 || date("w", strtotime($shuttledate)) == 0) {
                $element_array = array('status' => 0, 'message' => 'There is no shuttle service for this date');
                return $element_array;
                exit;
            }
            $subscribtionsts = 0;
        }
        $message = $this->seats_confirmed_sts($ttype, $tripid_p, $tripid_d);
        if ($message == "Confirmed") {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {
                $origin = $plat . "," . $plong;
                $destination = $dlat . "," . $dlong;

                //$tdist = round($this->distance($plat,$plong,$dlat,$dlong,'K')*1.5);//(without google key distance cal)
                //exit;
                $km = $this->cmodel->GetDrivingKm($origin, $destination);
                $tdist=round($km['distance']);
                //$tdist = $km;
                $totdays = 0;
                $tfare = 0;
                $validtrip = 0;
                //if (count($tariff) > 0 && $noofdays > 0) {                    
                if ($ttype == "B") {
                    $totdays = $noofdays;
                    $veh_id_p = $dao->getvehicleid($tripid_p);
                    $tariff_p = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_p, $noofdays, $branch_id);
                    $veh_id_d = $dao->getvehicleid($tripid_d);
                    $tariff_d = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_d, $noofdays, $branch_id);
                    if (count($tariff_p) > 0 && count($tariff_d) > 0) {
                        $tfare = $tariff_p[0]['fare'] + $tariff_d[0]['fare'];
                        $validtrip = $tariff_p[0]['validTrip'] * 2;
                        $totdist = $tdist * 2;
                        $tariff_id = $tariff_p[0]['tariff_id'];
                        $tariff_cal_name = $tariff_p[0]['tariff_cal_name'];
                    }
                } else {
                    $totdays = $noofdays;
                    $veh_id_p = $dao->getvehicleid($tripid_p);
                    $tariff = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_p, $noofdays, $branch_id);
                    $tfare = $tariff[0]['fare'];
                    //$tfare = $tariff[0]['fare'];
                    $validtrip = $tariff[0]['validTrip'];
                    $totdist = $tdist;
                    $tariff_id = $tariff[0]['tariff_id'];
                    $tariff_cal_name = $tariff[0]['tariff_cal_name'];
                }
                // $enddate=$tariff_cal_name=='Corp'? date("Y-m-t",strtotime($shuttledate)):$enddate; 

                if ((intval(date('m', strtotime($shuttledate)))) == 12 && (intval(date('Y', strtotime($shuttledate)))) == 2017 && $tariff_cal_name == 'Corp' && $branch_id != 1) {
                    $shuttledate = date('Y-m-d', strtotime('first day of +1 month', strtotime($shuttledate))); //date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                    $enddate = ($noofdays == 1) ? $shuttledate : date("Y-m-t", strtotime($shuttledate));
                    if ($subs_confirm_sts == 0) {
                        $element_array = array('status' => 2, 'message' => 'Subscribtion start from 1st jan 2018.Do you want to continue');
                        return $element_array;
                        exit;
                    }
                }

                $data = array('BranchId' => $branch_id, 'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => trim($ppoint), 'DropPoint' => trim($dpoint), 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $tfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $shuttledate, 'EndDate' => $enddate, 'NoofDays' => $totdays, 'NoofRides' => $validtrip, 'TotalPaidAmt' => $tfare, 'RouteType' => $route_type, 'TariffId' => $tariff_id);
                $cnt = $this->cmodel->c_insert('shuttle_booking', $data);
                //echo $this->db->last_query();
                // exit;
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $this->cmodel->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {
                        //$tfare=1;                       
                        if ($tfare > 0) {
                            $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $tfare, $stopid_p, $stopid_d);
                            $element_array = array('status' => 1, 'message' => $response);
                        } else {
                            $element_array = array('status' => 0, 'message' => 'Payment service is temporarily stopped');
                        }
                        // $response=$this->paytmwithdraw_response($paymentno, 0, 0, $mobileno,$stopid_p,$stopid_d);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscribtion already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
        $this->semodel->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, json_encode($element_array));
        return $element_array;
    }

    public function withoutgooglekey_search($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        // $seat_count_time= date("H:i:s", strtotime('+2 hours', strtotime($curtime)));
        // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
//        print_r($pickarray);
//        print_r($droparray);
//        exit;
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate, $branchid);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
                            //$dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            //$dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            //$traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));                            
                            //$package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                            //$traveltime = $traveldist[1];
                            // echo $plat, $plong, $dlat, $dlong;
                            $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                            $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                            $traveldist = $this->distance($plat, $plong, $dlat, $dlong, 'K');
                            $package = $this->tariff_cal(round($traveldist * $km_deviation), $ttype, $route_type, $vehicle_id, 0, $branchid);
                            $traveltime = round($traveldist * $km_deviation * $time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist * $km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            // $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                        } else {
                            // if($shedule_date==$curdate && $stime > $curtime)
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $sts = "";
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime = "CMonday";
                                    $sts = 'true';
                                } else {
                                    if ($route_name == TRIP_P) {
                                        if ($a_p_picktime <= $seat_count_time && $seats_used > 0) {
                                            $sts = 'true';
                                        } else if ($a_p_picktime > $seat_count_time && $seats_used >= 0) {
                                            $sts = 'true';
                                        } else {
                                            $sts = 'false';
                                        }
                                    } else {
                                        $sts = 'true';
                                    }
                                    $shuttletime = "ANext";
                                }
                                if ($sts == 'true') {

                                    // $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    // $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    //$traveldist = $this->GetDrivingKm($origin, $destination);
//                                print_r($traveldist);
//                                exit;
                                    //$package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type,$vehicle_id, 0);
                                    //$traveltime = $traveldist['time'];   
                                    $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                                    $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                                    $traveldist = $this->distance($plat, $plong, $dlat, $dlong, 'K');
                                    $package = $this->tariff_cal(round($traveldist * $km_deviation), $ttype, $route_type, $vehicle_id, 0, $branchid);
                                    $traveltime = round($traveldist * $km_deviation * $time_deviation);
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist * $km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);

                                    //                               $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                                }
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {

                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                //$dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                //$dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                //$traveldist = $this->GetDrivingKm($origin, $destination);
                                // print_r($traveldist);
                                //$package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type,$vehicle_id, 0);
                                //$traveltime = $traveldist['time'];  
                                $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $plat, $plong, 'K');
                                $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                                $traveldist = $this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist * $km_deviation), $ttype, $route_type, $vehicle_id, 0, $branchid);
                                $traveltime = round($traveldist * $km_deviation * $time_deviation);
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist * $km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
//                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate, $branchid);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
                            // $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            //$dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            //$traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            //$package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                            //$traveltime = $traveldist[1];
                            $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $plat, $plong, 'K');
                            $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $dlat, $dlong, 'K');
                            $traveldist = $this->distance($plat, $plong, $dlat, $dlong, 'K');
                            $package = $this->tariff_cal(round($traveldist * $km_deviation), $ttype, $route_type, $vehicle_id, 0, $branchid);
                            $traveltime = round($traveldist * $km_deviation * $time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist * $km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            ////                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
////                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            //                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                        } else {
                            if ($shedule_date == $curdate && $stime > $curtime) {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "ANext";
                                }
                                //  $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                //  $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                //  $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                //  $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                                //  $traveltime = $traveldist[1];
                                $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $plat, $plong, 'K');
                                $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $dlat, $dlong, 'K');
                                $traveldist = $this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist * $km_deviation), $ttype, $route_type, $vehicle_id, 0, $branchid);
                                $traveltime = round($traveldist * $km_deviation * $time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist * $km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
//                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                // $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                // $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                // $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                //  $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                                //  $traveltime = $traveldist[1]; 
                                $dist = $route_type == 'Fixed' ? 0.2 : $this->distance($endlat, $endlong, $plat, $plong, 'K');
                                $dist1 = $route_type == 'Fixed' ? 0.2 : $this->distance($startlat, $startlong, $dlat, $dlong, 'K');
                                $traveldist = $this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist * $km_deviation), $ttype, $route_type, $vehicle_id, 0, $branchid);
                                $traveltime = round($traveldist * $km_deviation * $time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist * $km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
//                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }

    public function seats_confirmed_sts($ttype, $tripid_p, $tripid_d) {
        $dao = new Shuttledao();
        $avail_p = false;
        $avail_d = false;
        $message = "";
        if ($ttype == "B") {
            $avail_p = $dao->get_seatcount($tripid_p);
            $avail_d = $dao->get_seatcount($tripid_d);
            if ($avail_p == true && $avail_d == true) {
                $message = "Confirmed";
            } else if ($avail_p == true && $avail_d == false) {
                $message = "Pickup routes only available";
            } else if ($avail_p == false && $avail_d == true) {
                $message = "Return routes only available";
            } else {
                $message = "No seats available";
            }
        } else {
            $avail_p = $dao->get_seatcount($tripid_p);
            if ($avail_p == true) {
                $message = "Confirmed";
            } else {
                $message = "No seats avialable";
            }
        }
        return $message;
    }

    public function tracking_updated($rosterid, $cabid, $eid, $gpsdate) {
        $dao = new Shuttledao();
       // $this->load->model('ShuttleElasticmdl');
        $getdate = $this->cmodel->get_datetime();
        $cur_datetime = $getdate['cur_date_time'];
        $cur_date = $getdate['cur_date'];
        $data = array();
        $element_arr1 = array();
        $element_arr = array();
        $gps_det = array();
        $tripsts = "";
        $tripmsg = "";
        $drivername = "";
        $drivermobile = "";
        $vehno = "";
        $vehmod = "";
        $blat = 0;
        $blong = 0;
        $slat = 0;
        $slong = 0;
        $sloc = "";
        $eloc = "";
        //employee details
        $row1 = $dao->track1($rosterid, $cabid, $eid);
        if ($row1) {
            $cab_arrived_time = $row1['cab_arrived_time'];
            $boarded_time = $row1['droptime'];
            $trip_type = $row1['TRIP_TYPE'];
            $rsts = $row1['ROSTER_PASSENGER_STATUS'];
            $blat = $row1['blat'];
            $blong = $row1['blong'];
            $slat = $row1['LATITUDE'];
            $slong = $row1['LONGITUDE'];
            $route_order = $row1['ROUTE_ORDER'];
            $sloc = $row1['ADDRESS'];
            $eloc = $row1['bloc'];
            $name=$this->cmodel->AES_DECRYPT($row1['NAME'], AES_ENCRYPT_KEY);
            if ($trip_type == TRIP_P) {
//                    $order = "rp.ROUTE_ORDER>=".$row1['ROUTE_ORDER']."";
                $order = $row1['ROUTE_ORDER'];
                if (is_null($cab_arrived_time)) {
                    $tripsts = "1";
                    $tripmsg = "Live Tracking";
                    $tracking = 1;
                } else if ((!is_null($cab_arrived_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "2";
                    $tripmsg = "Cab reached your Location.OTP is " . $row1['OTP'] . " ";
                    $tracking = 1;
                } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                    $tripsts = "3";
                    $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                    $tracking = 1;
                }
                $blat = $row1['blat'];
                $blong = $row1['blong'];
                $slat = $row1['LATITUDE'];
                $slong = $row1['LONGITUDE'];
                $sloc = $sloc;
                $eloc = $eloc;
            } else {
                $order = $row1['ROUTE_ORDER'];
                if (is_null($boarded_time) && (in_array($rsts, unserialize(RP_CREATED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                    $tracking = 1;
                } else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_CREATED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                    $tracking = 1;
                } else if ((!is_null($boarded_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "3";
                    $tripmsg = " OTP :" . $row1['OTP'];
                    $tracking = 1;
                } else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP)))) {
                    $tripsts = "3";
                   // $tripmsg = "You have reached your location!!!!";
                    $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                    $tracking = 1;
                }
                else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_DROPPED)))) {
                    $tripsts = "3";
                    $tripmsg = "You have reached your location!!!!";
                   // $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                    $tracking = 0;
                }
                $slat = $row1['blat'];
                $slong = $row1['blong'];
                $blat = $row1['LATITUDE'];
                $blong = $row1['LONGITUDE'];
                $sloc = $row1['bloc'];
                $eloc = $row1['ADDRESS'];
            }

            if ($tracking == 1) {
                $waypoints = '';
                $row2 = $dao->track2($rosterid, $cabid, floatval($order), $eid); //get all employee details  for particular traking roster id                   
                $r_order = 0;
                $sts = "";
                $rowcnt = count($row2);
                if ($rowcnt > 0) {
                    foreach ($row2 as $val) {
                        $otpsts = $val['ROSTER_PASSENGER_STATUS'];
                        $r_order = $val['ROUTE_ORDER'];
                        $empname=$this->cmodel->AES_DECRYPT($val['NAME'], AES_ENCRYPT_KEY);
                        if ($trip_type == TRIP_P) {
                            if ($route_order > $r_order) {
                                $waypoints = $sts == "true" ? $val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|" : '';
                            } else {
                                $element_arr1[] = array('slat' => $slat, 'slong' => $slong, 'pickuptime' => $val['picktime'], 'empname' => $empname,
                                    'sloc' => $sloc, 'eloc' => $eloc, 'empid' => $val['EMPLOYEE_ID'], 'route_order' => $val['ROUTE_ORDER'], 'elat' => $blat, 'elong' => $blong, 'trip_type' => $trip_type);
                                if (!in_array($otpsts, unserialize(RP_PICKUP_DROP_OTP))) {
                                    $waypoints .= $val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                                }
                                if ($eid == $val['EMPLOYEE_ID'] && in_array($otpsts, unserialize(RP_PICKUP_DROP_OTP))) {
                                    $sts = "true";
                                }
                            }
                        } else {
//echo "otp sts==".$otpsts;
                            $element_arr1[] = array('slat' => $slat, 'slong' => $slong, 'pickuptime' => $val['picktime'], 'empname' => $empname,
                                'sloc' => $sloc, 'eloc' => $eloc, 'empid' => $val['EMPLOYEE_ID'], 'route_order' => $val['ROUTE_ORDER'], 'elat' => $blat, 'elong' => $blong, 'trip_type' => $trip_type);
                            if (in_array($otpsts, unserialize(RP_CREATED))) {
                                $sts = "true";
                            } else if (in_array($otpsts, unserialize(RP_ARRIVED_BOARDED)) && !in_array($otpsts, unserialize(RP_PICKUP_DROP_OTP))) {
                                $waypoints .= $val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                            }
                        }
                    }
                } else {
                    $element_arr1[] = array('slat' => $slat, 'slong' => $slong, 'pickuptime' => $row1['picktime'], 'empname' => $name,
                        'sloc' => $sloc, 'eloc' => $eloc, 'empid' => $eid, 'elat' => $blat, 'elong' => $blong, 'trip_type' => $trip_type);
                    $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
                }
//                echo json_encode($element_arr1);
//                exit;
                $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
                $dlat = $row1['LATITUDE'];
                $dlong = $row1['LONGITUDE'];
                if ($gpsdate == "1900-01-01 00:00:00") {
                    $driverdet = $dao->get_driver_details($cabid);
                    if ($driverdet) {
                        $drivername = $driverdet['DRIVERS_NAME'];
                        $drivermobile = $driverdet['DRIVER_MOBILE'];
                        $vehno = $driverdet['VEHICLE_REG_NO'];
                        $driverimage = $driverdet['DRIVER_IMAGE'];
                        $vehmod = $driverdet['MODEL'];
                    }
                }
                //echo json_encode($element_arr1);
                $ret = $this->semodel->cabNavigation($cabid, '1900-01-01 00:00:00');
                $origin = $ret['RESULT'][0]['POSITION'];
                $spos = explode(',', $origin);
                $slat = $spos[0];
                $slong = $spos[1];

                $gps_currdate = $ret['RESULT'][0]['GPS_DATE'];
                // $driverimage = mysql_real_escape_string("https://mytransportportal.com/cog_track/img/driver_img/photo.jpg");

                if ($sts == "true") {
                    $waypoints .= $blat . ',' . $blong . "|";
                    $destination = $blat . ',' . $blong;
                    $dlat = $blat;
                    $dlong = $blong;
                }
                $km_deviation = 1.4;
                $time_deviation = 180;

                //google api is not working below code is used
                /* $km = $this->distance($slat,$slong,$dlat,$dlong,'K');
                  $traveltime=$km*$km_deviation*$time_deviation;
                  $hours = floor($traveltime / 3600);
                  $mins = floor($traveltime / 60 % 60);
                  if ($hours == 0) {
                  $minutes = $mins . " mins";
                  } else {
                  $minutes = $hours . 'hrs -' . $mins . " mins";
                  } */
                $km = $this->cmodel->calkm($origin, $destination, $waypoints, 'both', 'driving');
                $x = explode("-", $km);
                if ($x[1] > 0) {
                    $hours = floor($x[1] / 3600);
                    $mins = floor($x[1] / 60 % 60);
                    if ($hours == 0) {
                        $minutes = $mins . " mins";
                    } else {
                        $minutes = $hours . 'hrs -' . $mins . " mins";
                    }
                } else {
                    $minutes = "NA";
                }
                $tsts_row = $dao->alert_signal_failure($rosterid, $cabid, $gps_currdate);
                if ($tsts_row) {
                    if ($tsts_row['tripsts'] == 'signalfailed') {
                        $tripmsg = "Alert signal failure";
                    }
                }
                $data[] = array('DriverName' => $drivername,
                    "DriverMobile" => $drivermobile,
                    "CabNo" => $vehno,
                    "cabLatLong" => $origin,
                    "TripStatus" => $tripsts,
                    "TripMsg" => $tripmsg,
                    "DriverImage" => $driverimage,
                    'CabModel' => $vehmod,
                    "ETA" => $minutes);
            }

            $element_arr = $this->semodel->cabNavigation($cabid, $gpsdate);
            //print_r($element_arr);
            $driver_arr = array();
            $gps_det = count($data) == 0 ? $driver_arr : $element_arr['RESULT'];
        }
        $element = array('DriverDetails' => $data, "details" => $gps_det, 'empdetails' => $element_arr1);
        return $element;
    }

    public function getlocation() {
        $tmp = array();
        $dao = new Shuttledao();
        $row = $dao->getcompany_location();
        $sub_cat=array();
        
        foreach ($row as $arg) {
            $tmp[$arg['sitename']][] = array('site_location' => $arg['siteloc'], 'branch_name' => $arg['branchname'], 'branch_id' => $arg['branchid'], 'company_id' => $arg['COMPANY_ID']);
        }
        
        $output = array();
        foreach ($tmp as $type => $labels) {    
                if($labels[0]['branch_id']==1){
                    $site_location=$labels[0]['site_location'];
                    $sub_cat[]=array('site_location' => $site_location, 'branch_name' => "Others", 'branch_id' => "1", 'company_id' => "");
                    $output[] = array(
                        'category' => $type,
                        'sub_category' => array_merge($labels,$sub_cat)
                    );
                }
                else
                {
                    $output[] = array(
                    'category' => $type,
                    'sub_category' => $labels
                    );
                }
        }        
        return array('Apiresponse' => $output);
    }

    public function search_group($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        // $seat_count_time= date("H:i:s", strtotime('+2 hours', strtotime($curtime)));
        // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
//        print_r($pickarray);
//        print_r($droparray);
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            $return_route = 0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $return_route = $row['Return_Route_No'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                            $traveltime = $traveldist[1];
                            // echo $plat, $plong, $dlat, $dlong;
//                            $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                        } else {
                            // if($shedule_date==$curdate && $stime > $curtime)
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $sts = "";
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime = "CMonday";
                                    $sts = 'true';
                                } else {
                                    if ($route_name == TRIP_P) {
                                        if ($a_p_picktime <= $seat_count_time && $seats_used > 0) {
                                            $sts = 'true';
                                        } else if ($a_p_picktime > $seat_count_time && $seats_used >= 0) {
                                            $sts = 'true';
                                        } else {
                                            $sts = 'false';
                                        }
                                    } else {
                                        $sts = 'true';
                                    }
                                    $shuttletime = "ANext";
                                }
                                if ($sts == 'true') {
                                    $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                    $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                    $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                                
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                                }
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {

                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                              
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $return_route = $row['Return_Route_No'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                            $traveltime = $traveldist[1];
//                            $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                        } else {
                            if ($shedule_date == $curdate && $stime > $curtime) {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "ANext";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $traveldist[1];
//                                 $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
//        $p_array = $this->msort('asc',$element_arr, array('route_date','approxtime','shuttle_time'));
//        $d_array = $this->msort('asc',$element_arr1, array('route_date','approxtime','shuttle_time'));
        $p_array = $this->msort('asc', $this->arraygroup($element_arr), array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $this->arraygroup($element_arr1), array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        // echo json_encode($this->arraygroup($p_array));
        return $element;
    }

    public function c_insert($tablename, $values) {
        $this->db->set($values);
        $this->db->insert($tablename, $this);
        if ($this->db->affected_rows() > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function c_update($tablename, $values, $where) {
        $where1 = $where;
        if ($where1 != '') {
            $this->db->where($where1);
            $this->db->update($tablename, $values);
            if ($this->db->affected_rows() > 0) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public function c_selectrow($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            return $row1;
        }
    }

    public function c_selectarray($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->result_array();
            return $row1;
        }
    }

    public function output($element_array, $ct) {
        if ($ct == "ip") {
            return json_encode($element_array);
        } else {
            $string = json_encode($element_array);
            $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
            $valreturn = array('xyssdff' => $returnval);
            return json_encode($valreturn);
        }
    }

    function msort($sorder, $array, $key, $sort_flags = SORT_REGULAR) {
        if (is_array($array) && count($array) > 0) {
            if (!empty($key)) {
                $mapping = array();
                foreach ($array as $k => $v) {
                    $sort_key = '';
                    if (!is_array($key)) {
                        $sort_key = $v[$key];
                    } else {
                        // @TODO This should be fixed, now it will be sorted as string
                        foreach ($key as $key_key) {
                            $sort_key .= $v[$key_key];
                        }
                        $sort_flags = SORT_STRING;
                    }
                    $mapping[$k] = $sort_key;
                }
                if ($sorder == "asc") {
                    asort($mapping, $sort_flags);
                } else {
                    arsort($mapping, $sort_flags);
                }
                $sorted = array();
                foreach ($mapping as $k => $v) {
                    $sorted[] = $array[$k];
                }
                return $sorted;
            }
        }
        return $array;
    }

    private function record_sort($records, $field, $reverse = false) {
        $hash = array();

        foreach ($records as $record) {
            $hash[$record[$field]] = $record;
        }

        ($reverse) ? krsort($hash) : ksort($hash);

        $records = array();

        foreach ($hash as $record) {
            $records [] = $record;
        }

        return $records;
    }

    public function getrandomnumber() {
        $a = 0;
        for ($i = 0; $i < 6; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(100000, 999999);

        return $a;
    }

    public function getrandomnumber1() {
        $a = 0;
        for ($i = 0; $i < 4; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(1000, 9999);

        return $a;
    }

    function sec_to_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor($seconds % 3600 / 60);
        $seconds = $seconds % 60;

        return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
    }

    public function sendsms($mobile, $sms) {
        $msg = urlencode($sms);
        $urltopost = "http://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=ntltaxi1&send=TOPSCS&dest=$mobile&msg=" . $msg;
        $ch = curl_init($urltopost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //Sms_Response
        $returndata = curl_exec($ch);
        return $returndata;
    }

    function getAddress($latitude, $longitude) {
        if (!empty($latitude) && !empty($longitude)) {

            $url = $this->signUrl("http://maps.googleapis.com/maps/api/geocode/json?address=" . $latitude . "," . $longitude . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($ch);
            //echo $response;
            curl_close($ch);

            //  $geocodeFromLatLong = json_decode($response);

            $output = json_decode($response);
            $status = $output->status;
            //Get address from json data
            $address = ($status == "OK") ? $output->results[1]->formatted_address : '';
            //Return address of the given latitude and longitude
            if (!empty($address)) {
                return $address;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    function encodeBase64UrlSafe($value) {
        return str_replace(array('+', '/'), array('-', '_'), base64_encode($value));
    }

    function decodeBase64UrlSafe($value) {
        return base64_decode(str_replace(array('-', '_'), array('+', '/'), $value));
    }

    function signUrl($myUrlToSign, $privateKey) {
        // parse the url
        $url = parse_url($myUrlToSign);
        $urlPartToSign = $url['path'] . "?" . $url['query'];

        // Decode the private key into its binary format
        $decodedKey = $this->decodeBase64UrlSafe($privateKey);

        // Create a signature using the private key and the URL-encoded
        // string using HMAC SHA1. This signature will be binary.
        $signature = hash_hmac("sha1", $urlPartToSign, $decodedKey, true);

        $encodedSignature = $this->encodeBase64UrlSafe($signature);

        return $myUrlToSign . "&signature=" . $encodedSignature;
    }

    function calkm($origin, $destination, $waypoints, $type, $mode) {
        if ($mode == "walk") {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //$url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking";
        } else {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //  $url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false";
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        //echo $response;
        curl_close($ch);

        $data = json_decode($response);

        $km = 0;
        $tim = "";
        // If we got directions, output all of the HTML instructions
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                //foreach ($leg->steps as $step) {
                $i = 0;
                foreach ($leg->distance as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        $x = explode(" ", $val);
                        if ($x[1] == 'km') {
                            $km += $x[0];
                        } else {
                            $km += ($x[0] * 0.001);
                        }
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        
                    } else {
                        $tim += $val;
                    }
                }
            }
            if ($type == 'km') {
                return $km;
            } else {
                return $km . '-' . $tim;
            }
        }
    }

    public function getWorkingDays($startDate, $endDate) {
        $begin = strtotime($startDate);
        $end = strtotime($endDate);

        if ($begin > $end) {
            return 0;
        } else {
            $no_days = 0;
            while ($begin <= $end) {
                $what_day = date("N", $begin);
                if (!in_array($what_day, [6, 7])) // 6 and 7 are weekend
                    $no_days++;
                $begin += 86400; // +1 day
            };
            return $no_days;
        }
    }

    function distance($lat1, $lon1, $lat2, $lon2, $unit) {
        //  echo $lat1, $lon1, $lat2, $lon2, $unit;
        $rtn = 0;
        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit = strtoupper($unit);

        if ($unit == "K") {
            $rtn = $miles * 1.609344;
        } else if ($unit == "N") {
            $rtn = $miles * 0.8684;
        } else {
            $rtn = $miles;
        }
        if (is_nan($miles)) {
            return 0;
        } else {
            return round($rtn, 2);
        }
    }

    function arraygroup($data) {
        $final_arr = array();
        foreach ($data as $key => $value) {
            if (!array_key_exists($value['routeno'], $final_arr)) {
                $final_arr[$value['routeno']] = $value;
                unset($final_arr[$value['routeno']]['approxtime']);
                $final_arr[$value['routeno']]['approxtime'] = array();
                $time_array = array('time' => $value['approxtime'], 'roster_id' => $value['roster_id'], 'shuttle_time' => $value['shuttle_time']);
                array_push($final_arr[$value['routeno']]['approxtime'], $time_array);
            } else {
                $time_array = array('time' => $value['approxtime'], 'roster_id' => $value['roster_id'], 'shuttle_time' => $value['shuttle_time']);
                array_push($final_arr[$value['routeno']]['approxtime'], $time_array);
            }
        }
//       print_r($final_arr);
//       exit;
        return $final_arr;
    }

    function GetDrivingKm($lat, $long) {
        $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $lat . "&destination=" . $long . "&sensor=false&units=metric&mode=driving&alternatives=true&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPTSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPTSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        curl_close($ch);


        $data = json_decode($response);
        // print_r($data);
        $km = 0;
        $km1 = 0;
        $km2 = 0;
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {

                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km = $x[0];
                    } else {
                        $km = ($x[0] * 0.001);
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $tim = $val;
                }
            }

            $route = $data->routes[1];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {
                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km1 = $x[0];
                    } else {
                        $km1 = ($x[0] * 0.001);
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $tim1 = $val;
                }
            }

            $route = $data->routes[2];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {
                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km2 = $x[0];
                    } else {
                        $km2 = ($x[0] * 0.001);
                    }
                }

                foreach ($leg->duration as $key => $val) {
                    $tim2 = $val;
                }
            }
        }
//echo $km.'|'.$km1.'|'.$km2;
        if ($km2 == 0) {
            if ($km1 == 0) {
                $kms = $km;
            } else {
                $kms = min(array($km, $km1));
            }
        } else if ($km1 == 0) {
            $kms = $km;
        } else {
            $kms = min(array($km, $km1, $km2));
        }


        $time = 0;
        if ($kms == $km) {
            $time = $tim;
        }
        if ($kms == $km1) {
            $time = $tim1;
        }
        if ($kms == $km2) {
            $time = $tim2;
        }
        return array('distance' => $kms, 'time' => $time, 'time_mins' => $time);
    }

    function GetTransitKm($origin, $destination) {
        $url = $this->signUrl("https://maps.googleapis.com/maps/api/distancematrix/json?origins=" . $origin . "&destinations=" . $destination . "&sensor=false&units=metric&mode=transit&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
        $json = json_decode(file_get_contents($url, null), true);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPTSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPTSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        curl_close($ch);
        $data = json_decode($response);
//        print_r($json);
//        exit();
        return $data;
    }
    
    function jiomoney_getcustomerinfo($mobile_no) {
       // $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "GET_CUSTOMER_INFO";
        $checksumSeed = JM_CHECKSUM_SEED;
        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('mobile_no' => $mobile_no, 'merchant_id' => $merchant_id);

        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);
        
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'FirstName' => $fname, 'LastName' => $lname, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $this->cmodel->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_registration($mobile_no, $fname, $lname) {
       // $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "REGISTER_USER";
        $checksumSeed = JM_CHECKSUM_SEED;
        $ovd_type="";
        $ovd_number="";
        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('first_name' => $fname, 'last_name' => $lname, 'mobile_no' => $mobile_no, 'merchant_id' => $merchant_id,
            'ovd_type'=>$ovd_type,'ovd_number'=>$ovd_number);

        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);       
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'FirstName' => $fname, 'LastName' => $lname, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $this->cmodel->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_registration_otpverification($mobile_no, $otp) {
      //  $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "REGISTER_USER_VERIFY_OTP";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('mobile_no' => $mobile_no, 'otp' => $otp, 'merchant_id' => $merchant_id);
        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);       
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $this->cmodel->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_checkbalance($mobile_no) {
      //  $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "FETCH_JM_BALANCE";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('mobile_no' => $mobile_no, 'merchant_id' => $merchant_id);
        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);      
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $this->cmodel->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_debitbalance($mobile_no, $t_amount) {
      //  $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "DEBIT_JM_BALANCE";
        $tran_ref_no = "NTL" . rand(1111111111, 9999999999);
        $txn_amount = number_format($t_amount, 2);
        $capture_flag = 'N';
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('merchant_id' => $merchant_id, 'tran_ref_no' => $tran_ref_no, 'txn_amount' => $txn_amount, 'capture_flag' => $capture_flag, 'mobile_no' => $mobile_no);

        $auth_jm_tran_ref_no = "";
        $auth_code = "";
        $auth_timestamp = "";
        $product_description = "";
        $udf1 = "";
        $udf2 = "";
        $udf3 = "";
        $udf4 = "";
        $udf5 = "";

        $data = "$merchant_id|$api_name|$cur_datetime|$tran_ref_no|$txn_amount|$auth_jm_tran_ref_no|$auth_code|$auth_timestamp|$capture_flag|$mobile_no|$product_description|$udf1|$udf2|$udf3|$udf4|$udf5";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);
        $data_string = json_encode($request);

        $ch = curl_init(JM_BALANCE_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'TransRefNo' => $tran_ref_no, 'TxnAmount' => $txn_amount, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $this->cmodel->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_debitbalanceotp($tran_ref_no, $otp, $mobile_no) {
        ///tran_ref_no ==>debit balance response (tran_ref_no) value
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "DEBIT_JM_BALANCE_VERIFY_OTP";
        $product_description = "withdraw";
        $udf1 = "123";
        $udf2 = "123";
        $udf3 = "123";
        $udf4 = "123";
        $udf5 = "123";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('merchant_id' => $merchant_id, 'tran_ref_no' => $tran_ref_no, 'otp' => $otp, 'mobile_no' => $mobile_no, 'product_description' => $product_description, 'udf1' => $udf1, 'udf2' => $udf2, 'udf3' => $udf3, 'udf4' => $udf4, 'udf5' => $udf5);

        $data = "$merchant_id|$api_name|$cur_datetime|$tran_ref_no|$otp|$mobile_no|$product_description|$udf1|$udf2|$udf3|$udf4|$udf5";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);
        $ch = curl_init(JM_BALANCE_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);

        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'TransRefNo' => $tran_ref_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $this->cmodel->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    public function shuttle_registraton_new($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct, $branch_id, $comp_id, $comp_name) {
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        try {
            $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $element_array = array();
            $ENC_EMAIL = $this->cmodel->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "(EMAIL = '" . $ENC_EMAIL . "' || MOBILE = '" . $ENC_MOBILE . "') AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row = $this->cmodel->c_selectrow('employees', $where);
            if ($row) {
                $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
            } else {
                //$eid = rand(10000, 100000);
                //echo "comp name==".$comp_name;
                $eid = mt_rand(100000, 999999);
                if ($comp_name != "0" && $branch_id == 1) {
                    $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdatetime);
                    $this->cmodel->c_insert('shuttle_company_master', $data1);
                    $comp_id = $this->db->insert_id();
                } else if ($comp_name != "0" && $branch_id != 1) {
                    $element_array = array('status' => 0, 'Message' => 'Other Company Registration Not Allowed');
                    return $element_array;
                    exit;
                }
                $na = str_replace('+', ' ', $name);
                $ENC_NAME = $this->cmodel->AES_ENCRYPT($na, AES_ENCRYPT_KEY);
                $data = array('BRANCH_ID' => $branch_id, 'SHUTTLE_COMPANY_ID' => $comp_id, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                    'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
                $cnt = $this->cmodel->c_insert('employees', $data);
                if ($cnt > 0) {
                    $message = "Thank you for registering with " . SMS_TITLE . ".enjoy the ride!";
                    $this->cmodel->insert_sms($branch_id, 'TOPSCS', $mobile, $message);
                    $data1 = array('branch_id' => $branch_id, 'shuttle_company_id' => $comp_id, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                    $this->cmodel->c_insert('shuttle_notification', $data1);
                    $element_array = array('status' => 1, 'Message' => 'Successfully registered');
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Registration failed');
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function profile_update_new($emergencyno, $homeaddr, $homelat, $homelong, $eid, $officeaddr, $officelat, $officelong, $branch_id, $comp_id, $comp_name, $auto_id, $mobileno) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array = array();
        $data4 = array();
        //$dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        if ($comp_name != "0") {
            $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdate_time);
            $this->cmodel->c_insert('shuttle_company_master', $data1);
            $comp_id = $this->db->insert_id();
            //$data6 = array('SHUTTLE_COMPANY_ID' => $comp_id);
        }

        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' => $this->cmodel->AES_ENCRYPT($emergencyno, AES_ENCRYPT_KEY));
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        $data4 = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time, 'SHUTTLE_COMPANY_ID' => $comp_id);
        $data5 = array_merge($data2, $data3, $data4);
        //$where = "EMPLOYEES_ID='$eid' and ACTIVE='1' and CATEGORY='Shuttle' and BRANCH_ID='$branch_id' and SHUTTLE_COMPANY_ID='$comp_id'";
        //$where="id='$auto_id'";
        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE='1' and CATEGORY='Shuttle'";
        $cnt = $this->cmodel->c_update('employees', $data5, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True", 'company_id' => $comp_id);
        } else {
            $element_array = array('status' => 1, 'Message' => "Please try again");
        }
        return $element_array;
    }

    public function profile_update_v1($emergencyno, $homeaddr, $homelat, $homelong, $eid, $officeaddr, $officelat, $officelong, $branch_id, $comp_id, $comp_name, $auto_id, $mobileno, $email, $otp) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array = array();
        $data4 = array();
        $data6 = array();
        $data7 = array();
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $sts = 'false';
        if ($otp != 0) {
            $otpsts = $dao->otp_verification($mobileno, $otp, 'ShuttleLogin');
            if ($otpsts == 'true') {
                $where1 = "OTP='$otp' and VERIFIED_STATUS='0' and MOBILE_NO='$mobileno' and OTP_CATEGORY='ShuttleLogin' and date(CREATED_DATE)='$curdate'";
                $value = array('VERIFIED_STATUS' => 1);
                $cnt1 = $this->cmodel->c_update('otp_verification', $value, $where1);
                if ($cnt1 > 0) {
                    $sts = 'true';
                } else {
                    $sts = 'false';
                    return $element_array = array('status' => 0, 'Message' => "Invalid Otp");
                    exit;
                }
            } else {
                $sts = 'false';
                return $element_array = array('status' => 0, 'Message' => "Otp Expired");
                exit;
            }
        }

        if ($comp_name != "0") {
            $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdate_time);
            $this->cmodel->c_insert('shuttle_company_master', $data1);
            $comp_id = $this->db->insert_id();
        }

        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' => $this->cmodel->AES_ENCRYPT($emergencyno, AES_ENCRYPT_KEY));
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        if ($mobileno != "0") {
            $enc_mob = $this->cmodel->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
            $ret = $dao->profile_verification("MOBILE", $enc_mob);
            if ($ret == 0) {
                $data6 = array('MOBILE' => $enc_mob);
            } else {
                return $element_array = array('status' => 0, 'Message' => "Mobile No already exist");
                exit;
            }
        }
        if ($email != "0") {
            $enc_mail = $this->cmodel->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ret = $dao->profile_verification("EMAIL", $enc_mail);
            if ($ret == 0) {
                $data7 = array('EMAIL' => $enc_mail);
            } else {
                return $element_array = array('status' => 0, 'Message' => "E-mail already exist");
                exit;
            }
        }
        $data4 = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time, 'SHUTTLE_COMPANY_ID' => $comp_id);
        $data5 = array_merge($data2, $data3, $data4, $data6, $data7);
        $where = "id='$auto_id'";
        $cnt = $this->cmodel->c_update('employees', $data5, $where);
        // echo $this->db->last_query();
        // exit;
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True", 'company_id' => $comp_id);
        } else {
            $element_array = array('status' => 0, 'Message' => "Please try again");
        }
        return $element_array;
    }

    public function mobilenoverification($branchid, $mobileno, $eid, $sts) {
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);

        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' AND CATEGORY='Shuttle' AND ACTIVE=1";
        $row = $this->cmodel->c_selectrow('employees', $where);
        // echo $this->db->last_query();
       // exit;
        if ($row) {
            $element_array = array('status' => 0, 'Message' => "Mobile No already exist");
        } else {
            if ($sts == 'resend') {
                $where1 = " VERIFIED_STATUS='0' and MOBILE_NO='$mobileno' and OTP_CATEGORY='ShuttleLogin' and CREATED_DATE between subtime('$curdate_time','00:15:00') and '$curdate_time'";
                $row1 = $this->cmodel->c_selectrow('otp_verification', $where1);
                if ($row1) {
                    $otp = $row1['OTP'];
                }
            }
            $where1 = "EMPLOYEES_ID = '$eid' AND BRANCH_ID='$branchid' AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row1 = $this->cmodel->c_selectrow('employees', $where1);
            if ($row1) {
                $ename= $this->cmodel->AES_DECRYPT($row1['NAME'], AES_ENCRYPT_KEY);
            }
           // Dear {Manikandan},Please use {1234} as {verification} OTP in TOPSA Corporate Share Ride.
            $smsmsg = "Dear $ename, Please use $otp as mobile no verification OTP in " . SMS_TITLE;
            $smssts = $this->cmodel->sendsms($mobileno, $smsmsg);
            $data = array('MOBILE_NO' => $mobileno, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0, 'SMS_RESPONSE' => $smssts,
                'OTP_CATEGORY' => 'ShuttleLogin', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdate_time);
            $cnt = $this->cmodel->c_insert('otp_verification', $data);
            $element_array = array('status' => 1, 'Message' => "True");
        }
        return $element_array;
    }

    public function profile_inactive_v1($mobileno, $eid, $branch_id) {
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);

        $data = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time, 'ACTIVE' => 2);
        $where = "MOBILE = '" . $ENC_MOBILE . "' AND BRANCH_ID='$branch_id' AND CATEGORY='Shuttle' AND ACTIVE=1";
        $cnt = $this->cmodel->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Please try again");
        }
        return $element_array;
    }

    public function shuttle_booking_insert_v1($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $stopid_p, $stopid_d, $noofdays, $noofrides, $sdate, $edate, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $branch_id, $totdist, $totfare,$ct) {
        //echo  $eid."|".$ppoint."|". $plat."|". $plong."|". $dpoint."|". $dlat."|". $dlong."|".$ttype."|".$ptime."|". $dtime."|".$tripid_p."|".$tripid_d."|".$stopid_p."|".$stopid_d."|".$noofdays."|".$noofrides."|".$sdate."|".$edate."|".$route_type."|".$subs_confirm_sts."|".$ssotoken."|".$cust_id."|".$mobileno."|".$branch_id."|".$totdist."|".$totfare;
        $element_array = array();
        $fare=0;$tfare=0;
        $getdate = $this->cmodel->get_datetime();
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;

        if ($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0 || $dlat == 0 || $dlong == 0 || $ssotoken == 'null') {
            $element_array = array('status' => 0, 'message' => 'Subsciption Failed');
            return $element_array;
            exit;
        }
        if ($noofdays == 1 && $branch_id != 1) {
            $element_array = array('status' => 0, 'message' => 'Subsciption only allowed');
            return $element_array;
            exit;
        }
        if ($noofdays > 1 && $branch_id == 47) {
            $element_array = array('status' => 0, 'message' => 'Booking will be enabled soon');
            return $element_array;
            exit;
        }
        if ($noofdays > 1) {
            $where = "EmployeeId='$eid' and '$sdate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $this->cmodel->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if (date("w", strtotime($sdate)) == 6 || date("w", strtotime($sdate)) == 0) {
                $element_array = array('status' => 0, 'message' => 'There is no shuttle service for this date');
                return $element_array;
                exit;
            }
            $subscribtionsts = 0;
        }
        $message = $this->seats_confirmed_sts($ttype, $tripid_p, $tripid_d);
        if ($message == "Confirmed") {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {

                $tfare=intval($totfare);
                if(($ct=='ip') && ($branch_id!=1) && ($noofdays==20) && ($noofrides==20)){
                   $fare= $tfare*2;
                   $noofdays=28;
                   $noofrides=40;                   
                }
                else
                {
                   $fare=$tfare;
                   $noofdays=$noofdays;
                   $noofrides=$noofrides;  
                }
                
                $data = array('BranchId' => $branch_id, 'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => trim($ppoint), 'DropPoint' => trim($dpoint), 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $fare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $sdate, 'EndDate' => $edate, 'NoofDays' => $noofdays, 'NoofRides' => $noofrides, 'TotalPaidAmt' => $fare, 'RouteType' => $route_type, 'TariffId' => 0);
                $cnt = $this->cmodel->c_insert('shuttle_booking', $data);               
           
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $this->cmodel->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {
                        if ($fare > 0 ) {
                            $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $fare, $stopid_p, $stopid_d);
                            $element_array = array('status' => 1, 'message' => $response);
                        } else {
                            $element_array = array('status' => 0, 'message' => 'Payment Failed');
                        }
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscribtion already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
        // $this->semodel->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id,json_encode($element_array));
        return $element_array;
    }
     public function update_rating($auto_id,$rating_sts)
    {
       // $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $element_array=array();
        
        $data = array('RATE_STATUS' => $rating_sts,'RATE_DATE' => $curdate_time);
        $where = "id='$auto_id'";
        $cnt = $this->cmodel->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Please try again");
        }
        return $element_array;
    }
    public function shuttle_subscription_mail($payment_no, $ename, $email, $empid, $mobile) {
        //$payment_no='151370411686480';
        $dao = new Shuttledao();
        $getdate = $this->cmodel->get_datetime();
        $curdate = $getdate['cur_date'];
        $pick_point = "";
        $drop_point = "";
        $s_date = "";
        $e_date = "";
        $noofdays = 0;
        $noofrides = 0;
        $km = 0;
        $totpaidamount = 0;
        $gst = 0;
        $paidamount = 0;
        $paid_date = date('d M Y', strtotime($curdate));
        $email = "<EMAIL>";
        try {
            $where = "PaymentNo='$payment_no' and PaymentStatus='S'";
            $row = $this->cmodel->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $totpaidamount = $row['PackageAmt'];
                $noofdays = $row['NoofDays'];
                $noofrides = $row['NoofRides'];
                $pick_point = $row['PickupPoint'];
                $drop_point = $row['DropPoint'];
                $s_date = $row['StartDate'];
                $e_date = $row['EndDate'];
                $gst = (round($totpaidamount) * GST) / 100;
                $paidamount = intval($totpaidamount) - $gst;

                // $email="<EMAIL>";
                $image = LOGO;
                $to = $email;
                $subject = "TOPSA Suscription Payment Details";
                $message = ' 
                <html>
                <head>
                <style>
                body {
                    font-family: "Open Sans", sans-serif;
                    line-height: 1.25;
                  }
                </style>
                <title>TOPSA</title>               
                </head>
                <body>
                <div style="overflow-x:auto;">
                 <table width=100% border=0 frame=box>
                 <tr height=40 bgcolor=F7F3F2><td colspan=3 align=center><b>Customer Details </b></td></tr>
                    <tr><td>Name</td><td>' . $ename . '</td><td rowspan=4 align=right>
                    <img src="' . $image . '" width=200 height=100>
                    </td></tr>
                    <tr> <td>ID</td><td>' . $empid . '</td></tr>
                    <tr> <td>Email</td><td>' . $email . '</td></tr>
                    <tr> <td>Phone No</td><td>' . $mobile . '</td></tr>                     
                 </table>        
                 <br>
                 <table width=100% frame=box>
                 <tr height=40 bgcolor=F7F3F2><td colspan=4 align=center><b>Suscription Details </b></td></tr>
                 <tr><td>Pickup Point</td><td>' . $pick_point . '</td><td>Start Date</td><td>' . date('d M Y', strtotime($s_date)) . "</td></tr>
                 <tr><td>Drop Point</td><td>$drop_point</td><td>End Date</td><td>" . date('d M Y', strtotime($e_date)) . "</td></tr>
                 <tr><td>No of days</td><td>$noofdays</td><td>No of rides</td><td>$noofrides</td></tr>
                 <tr height=40 bgcolor=F7F3F2><td colspan=4 align=center><b>Fare Details</b></td></tr>
                 <tr><td></td><td></td><td align=right>Base Fare</td><td align=right>" . round($paidamount) . "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>
                 <tr><td></td><td></td><td align=right>Tax </td><td align=right>" . round($gst) . "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>
                 <tr><td></td><td></td><td align=right><b>Total Paid Amount</b> </td><td align=right><b>" . intval($totpaidamount) . "</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>
                 <tr height=40 bgcolor=F7F3F2><td colspan=4></td></tr>
                 </table></div>
                 <br>
                 <br>
                 
                 <font color=red size=4>* This is system generated mail.</font>
                  ";
                $headers = "Content-type:text/html;charset=UTF-8" . "\r\n";
                $headers .= "From:<EMAIL>" . "\r\n";
                $send = mail($to, $subject, $message, $headers);
            }
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }
    
    public function shuttle_notification_mail() {       
        $dao = new Shuttledao();        
       // $email = "<EMAIL>";
        try {
            $arr=$dao->send_mail_forall();            
            foreach ($arr as $row)
            {
                $eamil=$row['EMAIL'];
                $eid=$row['EMPLOYEES_ID'];
                $data=array('SEND_MAIL_STATUS'=>1);
                $where="EMPLOYEES_ID='$eid' and BRANCH_ID=42";
                $this->cmodel->c_update('employees', $data, $where);                
                $to=$this->cmodel->AES_DECRYPT($eamil, AES_ENCRYPT_KEY);
                $this->mail_content($to);                
            }
           
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }
    
    function mail_content($to)
    {
        $subject = "TOPSA+ - Grab the opportunity !!!";
        $message = " 
        <html>
        <head>
        <style>
        #txt{
            font-family: 'Open Sans', sans-serif;                    
          }
        </style>
        <title>TOPSA</title>               
        </head>
        <body class='txt'>
        <font size=5 color=green>Grab the opportunity !!!</font>
        <br>
        <p align='justify'>
        Upto 20% discount on your ride!!! 
        <br>Monthly subscription starting INR 2500 !!!  <br>             
        Grab the limited opportunity. Subscribe today and be the early bird. 
        </p>                
         <br>
         <br>

         <b>With Regards,<br>
         TOPSA TEAM    
         </b>
         <br>
         <br>
         <font color=red size=4>* This is system generated mail.</font>
         </body> ";
        $headers = "Content-type:text/html;charset=iso 8859-1" . "\r\n";
        $headers.="MIME-Version: 1.0\n";
        $headers .= "From:<EMAIL>" . "\r\n";
        $send = mail($to, $subject, $message, $headers);           
    }
    
    public function shuttle_searchroute_driver($startlat, $startlong, $endlat, $endlong,$resheduledate, $reshedulderoute, $branchid, $route_type) 
    { 
        $dao = new Shuttledao();
        $element_arr=array();
        $pickarray = $this->semodel->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->semodel->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();

        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }           
            foreach ($output as $val) {                 
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];               
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d = $val['DROP_STOP_ID'];
//                $p_route_order = $dao->get_route_order($route_id, $stop_id);
//                $d_route_order = $dao->get_route_order($route_id, $stop_id_d);
//                
                $p_order = $dao->get_route_order($route_id,$stop_id);
                $d_order = $dao->get_route_order($route_id,$stop_id_d);
                $p_route_order=$p_order['Route_Order'];
                $d_route_order=$d_order['Route_Order'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {   
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];                       
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];                       
                        $dist = $this->cmodel->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                        $dist1 = $this->cmodel->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                        $traveldist = explode('-', $this->cmodel->calkm($origin, $destination, $origin, 'both', 'driving'));
                        $dist2=round($traveldist[0]);                            
                        $traveltime = $traveldist[1];
                        $element_arr[] = array('routeno' => $route_id,'pick_date' => $pick_date, 'drop_date' => $drop_date,'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong,
                            'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => $dist2, 'travel_time' => $traveltime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'routeorder_p' => $p_route_order, 'routeorder_d' => $d_route_order);
                }
                
            }
            $element = array('status' => 1, 'routeid_details' => $element_arr);
        }
        else
        {
            $element = array('status' => 0, 'routeid_details' => $element_arr);
        }        
        return $element;        
    }
    
    public function shuttle_loginverification_web($mobile, $password,$ct) {
        $sessionEncrypt = "";
        $encrypted = "";
        $officeaddr = "--";
        $officelat = 0;
        $officelong = 0;
        $element_array = array();
        $comp_id = 0;
        $auto_id = 0;
        try {
            $dao = new Shuttledao();
            $getdate = $this->cmodel->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            
                    $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
                    $row = $this->cmodel->c_selectrow('employees', $where);
                    //echo $this->db->last_query();
                    if ($row) {
                        $name = $this->cmodel->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                        $gender = $row['GENDER'];
                        $email = $this->cmodel->AES_DECRYPT($row['EMAIL'], AES_ENCRYPT_KEY);
                        $address = $row['ADDRESS'];
                        $lat = $row['LATITUDE'];
                        $long = $row['LONGITUDE'];
                        $category = $row['CATEGORY'];
                        $bid = $row['BRANCH_ID'];
                        $eid = $row['EMPLOYEES_ID'];
                        $emergency_no = $row['EMERGENCY_CONTACT_NO'];
                        $comp_id = $row['SHUTTLE_COMPANY_ID'];
                        $auto_id = $row['id'];
                        if (!(is_null($emergency_no))) {
                            $emergency_no = $this->cmodel->AES_DECRYPT($emergency_no, AES_ENCRYPT_KEY);
                        } else {
                            $emergency_no = 0;
                        }
                        $mcat = 'WEB';
                        
                        $address_property=$this->property_check('OFFICE_ADDRESS', $bid, 0);
                        if($address_property=='Branch'){
                            $where1 = "BRANCH_ID = '$bid ' AND ACTIVE = '1'";
                            $row1 = $this->cmodel->c_selectrow('branch', $where1);
                            if ($row1) {
                                $officeaddr = $row1['LOCATION'];
                                $officelat = $row1['LAT'];
                                $officelong = $row1['LONG'];
                            }   
                        }
                        else
                        {
                            $where1 = "BRANCH_ID = '$bid ' AND COMPANY_ID='$comp_id' AND ACTIVE = '1'";
                            $row1 = $this->cmodel->c_selectrow('shuttle_company_master', $where1);
                            if ($row1) {
                                $officeaddr = $row1['LOCATION'];
                                $officelat = $row1['LAT'];
                                $officelong = $row1['LONG'];
                            }   
                        }
                        $sessionid = rand(10000, 100000);
                        $sql_encryt = $this->cmodel->AES_ENCRYPT($sessionid, AES_ENCRYPT_KEY);
                        $data1 = array(
                            'SESSION_ID' => base64_encode($sql_encryt),                            
                            'MOBILE_CATEGORY' => $mcat
                        );
                        $this->cmodel->c_update("employees", $data1, $where);
                        $sessionEncrypt = base64_encode($sql_encryt);
                        $encrypted = $this->rsaencrypt($mobile);
                        if ($encrypted != 'False') {
                            $element_array = array('status' => 1, 'Message' => 'success', 'branch_id' => $bid, 'employee_id' => $eid, 'mobile' => $mobile, 'emergency_contact_no' => $emergency_no, 'name' => $name, 'gender' => $gender, 'email' => $email,
                                'homeaddress' => $address, 'homelat' => $lat, 'homelong' => $long, 'officeaddress' => $officeaddr, 'officelat' => $officelat, 'officelong' => $officelong,
                                'category' => $category, 'sessionid' => $sessionEncrypt, 'publickey' => $encrypted, 'company_id' => $comp_id, 'auto_id' => $auto_id, 'PAYTM_MID' => PAYTM_MID, 'PAYTM_WEBSITE' => PAYTM_WEBSITE_WEB,
                                'PAYTM_INDUSTRY_TYPE_ID' => PAYTM_INDUSTRY_TYPE_ID, 'PAYTM_CHANNEL_ID_WEB' => PAYTM_CHANNEL_ID_WEB,'login_password'=>$ENCRYP_PWD);
                        } else {
                            $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                        }
                    } else {
                        $element_array = array('status' => 0, 'Message' => 'Invalid Mobile No/Password');
                    }
               
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }
	
    public function shuttle_booking_insert_without_payment($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $stopid_p, $stopid_d, $noofdays, $noofrides, $sdate, $edate, $route_type, $subs_confirm_sts,$mobileno, $branch_id, $totdist, $totfare,$ct) {
        //echo  $eid."|".$ppoint."|". $plat."|". $plong."|". $dpoint."|". $dlat."|". $dlong."|".$ttype."|".$ptime."|". $dtime."|".$tripid_p."|".$tripid_d."|".$stopid_p."|".$stopid_d."|".$noofdays."|".$noofrides."|".$sdate."|".$edate."|".$route_type."|".$subs_confirm_sts."|".$ssotoken."|".$cust_id."|".$mobileno."|".$branch_id."|".$totdist."|".$totfare;
        $element_array = array();
        $fare=0;$tfare=0;
        $getdate = $this->cmodel->get_datetime();
        $cur_time = $getdate['cur_date_time'];
        $curdate=$getdate['cur_date'];
        $subscribtionsts = 0;

        if ($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0 || $dlat == 0 || $dlong == 0 || $ssotoken == 'null') {
            $element_array = array('status' => 0, 'message' => 'Subscription Failed');
            return $element_array;
            exit;
        }
        if ($noofdays == 1 && $branch_id != 1) {
            $element_array = array('status' => 0, 'message' => 'Subscription only allowed');
            return $element_array;
            exit;
        }
        if ($noofdays > 1 && $branch_id == 47) {
            $element_array = array('status' => 0, 'message' => 'Booking will be enabled soon');
            return $element_array;
            exit;
        }
        if ($noofdays > 1) {
            $where = "EmployeeId='$eid' and '$sdate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $this->cmodel->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if (date("w", strtotime($sdate)) == 6 || date("w", strtotime($sdate)) == 0) {
                $element_array = array('status' => 0, 'message' => 'There is no shuttle service for this date');
                return $element_array;
                exit;
            }
            $subscribtionsts = 0;
        }
        $message = $this->seats_confirmed_sts($ttype, $tripid_p, $tripid_d);
        if ($message == "Confirmed" && $sdate >= $curdate && $edate >= $curdate){
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {

                $tfare=intval($totfare);
                if(($ct=='ip') && ($branch_id!=1) && ($noofdays==20) && ($noofrides==20)){
                   $fare= $tfare*2;
                   $noofdays=28;
                   $noofrides=40;                   
                }
                else
                {
                   $fare=$tfare;
                   $noofdays=$noofdays;
                   $noofrides=$noofrides;  
                }
                
                $data = array('BranchId' => $branch_id, 'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => trim($ppoint), 'DropPoint' => trim($dpoint), 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $fare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $sdate, 'EndDate' => $edate, 'NoofDays' => $noofdays, 'NoofRides' => $noofrides,'Discount'=>$fare,'TotalPaidAmt' => 0, 'RouteType' => $route_type, 'TariffId' => 0);
                $cnt = $this->cmodel->c_insert('shuttle_booking', $data);               
           
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno,'PaymentStatus'=>'S');
                    $where = "Sno=$id";
                    $cnt1 = $this->cmodel->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {                        
                            $response = $this->paytmwithdraw_response($paymentno, 0, 0, $mobileno,$stopid_p, $stopid_d);
                            $resp=($response=='Payment Success')?'Booking Success':'Booking Failed';
                            $element_array = array('status' => 1, 'message' => $resp);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscription already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
        // $this->semodel->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime,$tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno,$stopid_p,$stopid_d,$branch_id,json_encode($element_array));
        return $element_array;
    }
	
	
}
