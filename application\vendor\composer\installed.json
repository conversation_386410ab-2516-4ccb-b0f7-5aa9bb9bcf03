{"packages": [{"name": "elasticsearch/elasticsearch", "version": "v5.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "48b8a90e2b97b4d69ce42851c1b9e59f8054661a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/48b8a90e2b97b4d69ce42851c1b9e59f8054661a", "reference": "48b8a90e2b97b4d69ce42851c1b9e59f8054661a", "shasum": ""}, "require": {"guzzlehttp/ringphp": "~1.0", "php": "^5.6|^7.0", "psr/log": "~1.0"}, "require-dev": {"cpliakas/git-wrapper": "~1.0", "doctrine/inflector": "^1.1", "mockery/mockery": "0.9.4", "phpunit/phpunit": "^4.7|^5.4", "sami/sami": "~3.2", "symfony/finder": "^2.8", "symfony/yaml": "^2.8"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "time": "2019-07-18T15:11:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v5.5.0"}, "install-path": "../elasticsearch/elasticsearch"}, {"name": "guzzlehttp/ringphp", "version": "1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/RingPHP.git", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/RingPHP/zipball/5e2a174052995663dd68e6b5ad838afd47dd615b", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b", "shasum": ""}, "require": {"guzzlehttp/streams": "~3.0", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "time": "2018-07-31T13:22:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple API and specification that abstracts away the details of HTTP into a single PHP function.", "support": {"issues": "https://github.com/guzzle/RingPHP/issues", "source": "https://github.com/guzzle/RingPHP/tree/1.1.1"}, "abandoned": true, "install-path": "../guzzlehttp/ringphp"}, {"name": "guzzlehttp/streams", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/streams.git", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/streams/zipball/47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2014-10-12T19:18:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple abstraction over streams of data", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "install-path": "../guzzlehttp/streams"}, {"name": "mikey179/vfsstream", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "fc0fe8f4d0b527254a2dc45f0c265567c881d07e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/fc0fe8f4d0b527254a2dc45f0c265567c881d07e", "reference": "fc0fe8f4d0b527254a2dc45f0c265567c881d07e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2012-08-25T12:49:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"org\\bovigo\\vfs": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD"], "homepage": "http://vfs.bovigo.org/", "support": {"issues": "https://github.com/bovigo/vfsStream/issues", "source": "https://github.com/bovigo/vfsStream/tree/v1.1.0"}, "install-path": "../mikey179/vfsstream"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "razorpay/razorpay", "version": "2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/razorpay/razorpay-php.git", "reference": "96c0167176cf53e3da15640622e9b993a3450ec7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/razorpay/razorpay-php/zipball/96c0167176cf53e3da15640622e9b993a3450ec7", "reference": "96c0167176cf53e3da15640622e9b993a3450ec7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.3.0", "rmccue/requests": "v1.7.0"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.0", "raveren/kint": "1.*"}, "time": "2019-05-20T13:15:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Razorpay\\Api\\": "src/", "Razorpay\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://captnemo.in", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Razorpay PHP Client Library", "homepage": "https://docs.razorpay.com", "keywords": ["api", "client", "php", "razorpay"], "support": {"email": "<EMAIL>", "issues": "https://github.com/Razorpay/razorpay-php/issues", "source": "https://github.com/Razorpay/razorpay-php"}, "install-path": "../razorpay/razorpay"}, {"name": "react/promise", "version": "v2.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "time": "2023-11-16T16:16:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "install-path": "../react/promise"}, {"name": "rmccue/requests", "version": "v1.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/rmccue/Requests.git", "reference": "87932f52ffad70504d93f04f15690cf16a089546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rmccue/Requests/zipball/87932f52ffad70504d93f04f15690cf16a089546", "reference": "87932f52ffad70504d93f04f15690cf16a089546", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"requests/test-server": "dev-master"}, "time": "2016-10-13T00:11:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/rmccue/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "support": {"issues": "https://github.com/rmccue/Requests/issues", "source": "https://github.com/rmccue/Requests/tree/master"}, "install-path": "../rmccue/requests"}], "dev": true, "dev-package-names": ["elasticsearch/elasticsearch", "guzzlehttp/ringphp", "guzzlehttp/streams", "mikey179/vfsstream", "psr/log", "react/promise"]}