

[[Elasticsearch_Namespaces_CatNamespace]]
=== Elasticsearch\Namespaces\CatNamespace



Class CatNamespace


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_CatNamespacealiases_aliases,`aliases()`>>
* <<Elasticsearch_Namespaces_CatNamespaceallocation_allocation,`allocation()`>>
* <<Elasticsearch_Namespaces_CatNamespacecount_count,`count()`>>
* <<Elasticsearch_Namespaces_CatNamespacehealth_health,`health()`>>
* <<Elasticsearch_Namespaces_CatNamespacehelp_help,`help()`>>
* <<Elasticsearch_Namespaces_CatNamespaceindices_indices,`indices()`>>
* <<Elasticsearch_Namespaces_CatNamespacemaster_master,`master()`>>
* <<Elasticsearch_Namespaces_CatNamespacenodes_nodes,`nodes()`>>
* <<Elasticsearch_Namespaces_CatNamespacenodeAttrs_nodeAttrs,`nodeAttrs()`>>
* <<Elasticsearch_Namespaces_CatNamespacependingTasks_pendingTasks,`pendingTasks()`>>
* <<Elasticsearch_Namespaces_CatNamespacerecovery_recovery,`recovery()`>>
* <<Elasticsearch_Namespaces_CatNamespacerepositories_repositories,`repositories()`>>
* <<Elasticsearch_Namespaces_CatNamespaceshards_shards,`shards()`>>
* <<Elasticsearch_Namespaces_CatNamespacesnapshots_snapshots,`snapshots()`>>
* <<Elasticsearch_Namespaces_CatNamespacethreadPool_threadPool,`threadPool()`>>
* <<Elasticsearch_Namespaces_CatNamespacefielddata_fielddata,`fielddata()`>>
* <<Elasticsearch_Namespaces_CatNamespaceplugins_plugins,`plugins()`>>
* <<Elasticsearch_Namespaces_CatNamespacesegments_segments,`segments()`>>
* <<Elasticsearch_Namespaces_CatNamespacetasks_tasks,`tasks()`>>



[[Elasticsearch_Namespaces_CatNamespacealiases_aliases]]
.`aliases()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->aliases($params);
----
****



[[Elasticsearch_Namespaces_CatNamespaceallocation_allocation]]
.`allocation()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['bytes']          = (enum) The unit in which to display byte values
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->allocation($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacecount_count]]
.`count()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->count($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacehealth_health]]
.`health()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['ts']             = (bool) Set to false to disable timestamping
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->health($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacehelp_help]]
.`help()`
****
[source,php]
----
/*
$params['help'] = (bool) Return help information
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->help($params);
----
****



[[Elasticsearch_Namespaces_CatNamespaceindices_indices]]
.`indices()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['bytes']          = (enum) The unit in which to display byte values
       ['pri']            = (bool) Set to true to return stats only for primary shards
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->indices($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacemaster_master]]
.`master()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->master($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacenodes_nodes]]
.`nodes()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->nodes($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacenodeAttrs_nodeAttrs]]
.`nodeAttrs()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->nodeAttrs($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacependingTasks_pendingTasks]]
.`pendingTasks()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->pendingTasks($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacerecovery_recovery]]
.`recovery()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['bytes']          = (enum) The unit in which to display byte values
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->recovery($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacerepositories_repositories]]
.`repositories()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->repositories($params);
----
****



[[Elasticsearch_Namespaces_CatNamespaceshards_shards]]
.`shards()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['bytes']          = (enum) The unit in which to display byte values
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->shards($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacesnapshots_snapshots]]
.`snapshots()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['bytes']          = (enum) The unit in which to display byte values
       ['repository']     = (string) Name of repository from which to fetch the snapshot information
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->snapshots($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacethreadPool_threadPool]]
.`threadPool()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['full_id']        = (bool) Enables displaying the complete node ids
       ['size']           = (enum) The multiplier in which to display values ([ "", "k", "m", "g", "t", "p" ])
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->threadPool($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacefielddata_fielddata]]
.`fielddata()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['bytes']          = (enum) The unit in which to display byte values
       ['fields']         = (list) A comma-separated list of fields to return the fielddata size
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->fielddata($params);
----
****



[[Elasticsearch_Namespaces_CatNamespaceplugins_plugins]]
.`plugins()`
****
[source,php]
----
/*
$params['local']          = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['master_timeout'] = (time) Explicit operation timeout for connection to master node
       ['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->plugins($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacesegments_segments]]
.`segments()`
****
[source,php]
----
/*
$params['h']              = (list) Comma-separated list of column names to display
       ['help']           = (bool) Return help information
       ['v']              = (bool) Verbose mode. Display column headers
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->segments($params);
----
****



[[Elasticsearch_Namespaces_CatNamespacetasks_tasks]]
.`tasks()`
****
[source,php]
----
/*
$params['format']         = (string) a short version of the Accept header, e.g. json, yaml
       ['node_id']        = (list) A comma-separated list of node IDs or names to limit the returned information; use `_local` to return information from the node you're connecting to, leave empty to get information from all nodes
       ['format']         = (string) a short version of the Accept header, e.g. json, yaml
       ['actions']        = (list) A comma-separated list of actions that should be returned. Leave empty to return all.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat()->tasks($params);
----
****


