<?php

set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
include(APPPATH . 'libraries/Shuttledao.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttlemdl
 *
 * <AUTHOR>
 */
class Shuttlemdl extends CI_Model {

    public function ssvaluecheck($mobileno, $ssid) {        
        $name = "";        
        $ssval = "";
        $ret="";
        $dao = new Shuttledao(); 
        $where = "MOBILE = '" . $mobileno . "' and ACTIVE=1";
        $row = $dao->c_selectrow('employees', $where);
        if ($row) {
            $name = $row['NAME'];
            $ssval = $row['SESSION_ID'];
            $privatekey = $row['PRIVATE_KEY'];
        }
        $rsa = new Crypt_RSA();
        $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
        $decoded = base64_decode($ssid);
        $rsa->loadKey($privatekey);
        $decrypted = $rsa->decrypt($decoded);
        if ($decrypted == $ssval) {
            $ret = 'true,';            
        } else {
            $ret = 'false';           
        }
        return $ret;
    }

    public function rsaencrypt($mobileno) {
        $rsa = new Crypt_RSA();
        extract($rsa->createKey());
        $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
        $element_array = str_ireplace('-----BEGIN PUBLIC KEY-----', '', $publickey);
        $elementrsaarray = trim(str_ireplace('-----END PUBLIC KEY-----', '', $element_array));

        $element_array1 = str_ireplace('-----BEGIN RSA PRIVATE KEY-----', '', $privatekey);
        $elementrsaarray1 = trim(str_ireplace('-----END RSA PRIVATE KEY-----', '', $element_array1));
        $data = array(
            'PUBLIC_KEY' => $elementrsaarray,
            'PRIVATE_KEY' => $elementrsaarray1
        );
        
        $dao = new Shuttledao();  
        $where1 = "MOBILE = '" . $mobileno . "' and ACTIVE=1";
        $cnt = $dao->c_update('employees', $data, $where1);
        if ($cnt > 0) {
            return $elementrsaarray;
        } else {
            return $element_array = 'False';
        }
    }

    public function shuttle_registraton($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct) {
        //echo $name,$email,$mobile,$password,$gender,$deviceinfo,$deviceid,$gcmid,$ct;
        date_default_timezone_set('Asia/Kolkata');
        $curdatetime = date('Y-m-d H:i:s');
        $ENCRYP_PWD = $this->Encrypt_Script($password, SECERT_KEY_NTL);

        $where = "(CustomerEmail='$email' || CustomerMobile='$mobile')";
        $row = $this->c_selectrow('shuttle_registration', $where);
        if ($row) {
            $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
        } else {
            $custid = $this->getrandomnumber();
            $data = array('CustomerName' => $name, 'Password' => $ENCRYP_PWD, 'CustomerEmail' => $email, 'CustomerMobile' => $mobile, 'Category' => 'Shuttle', 'RegisterDatetime' => $curdatetime,
                'CustomerId' => $custid, 'Gender' => $gender, 'DeviceInfo' => $deviceinfo, 'DeviceId' => $deviceid, 'MobileGcm' => $gcmid);
            $cnt = $this->c_insert('shuttle_registration', $data);
            if ($cnt > 0) {
                $element_array = array('status' => 1, 'Message' => 'Successfully registered');
            } else {
                $element_array = array('status' => 0, 'Message' => 'Registration failed');
            }
        }
        echo $this->output($element_array, $ct);
    }

    public function shuttle_otpcreation($mobile, $password) {
        $smssts = "";
        $smsmsg = "";
        $element_array = array();
        $dao = new Shuttledao();        
        $getdate=$dao->get_datetime();
        $curdatetime=$getdate['cur_date_time'];
        
        $otp = $this->getrandomnumber1();
       // $ENCRYP_PWD = $this->Encrypt_Script($password, SECERT_KEY_NTL);
        $where = "MOBILE = '" . $mobile . "' AND password = '$password'";
        $row = $dao->c_selectrow('employees', $where);        
        if ($row) {
            $ename = $row['NAME'];
            $eid=$row['EMPLOYEES_ID'];
            $smsmsg = "Dear $ename, Your login OTP is $otp. Please use this OTP in corporate COG MATS APP. Thanks ";
            $smssts = $this->sendsms($mobile, $smsmsg);
            $data = array('MOBILE_NO'=>$mobile,'ROSTER_PASSENGER_ID'=>$eid,'OTP'=>$otp,'VERIFIED_STATUS'=>0,'SMS_RESPONSE'=>$smssts,
                'OTP_CATEGORY' => 'EmpLogin','CREATED_BY'=>$eid,'CREATED_DATE'=>$curdatetime);
            $cnt = $dao->c_insert('otp_verification', $data);
            if ($cnt > 0) {                
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
            }
        } else {
            $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
        }      
        return $element_array;
    }

    public function shuttle_otpverified($mobile, $password, $otp) {       
        $sessionEncrypt = "";
        $encrypted = "";
        $officeaddr="--";
        $officelat=0;
        $officelong=0;
        $element_array = array();
        
        $dao = new Shuttledao();        
        $getdate=$dao->get_datetime();
        $curdatetime=$getdate['cur_date_time'];
        
        $where1="OTP='$otp' and VERIFIED_STATUS='0' and OTP_CATEGORY='EmpLogin' and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
        $value=array('VERIFIED_STATUS'=>1);
        $cnt=$dao->c_update('otp_verification', $value, $where1);        
        if($cnt>0)
        {
            $where = "MOBILE = '" . $mobile . "' AND password = '$password'";
            $row =$dao->c_selectrow('employees', $where);
            if ($row) {
                $name = $row['NAME'];
                $gender = $row['GENDER'];
                $email = $row['EMAIL'];
                $address = $row['ADDRESS'];
                $lat = $row['LATITUDE'];
                $long = $row['LONGITUDE'];
                $category = $row['CATEGORY'];
                $bid=$row['BRANCH_ID'];
                $eid=$row['EMPLOYEES_ID'];
                if($category!="shuttle")
                {
                    $where1 = "BRANCH_ID = '$bid ' AND ACTIVE = '1'";
                    $row1 =$dao->c_selectrow('branch', $where1);
                    if ($row1) {
                        $officeaddr=$row1['LOCATION'];
                        $officelat=$row1['LAT'];
                        $officelong=$row1['LONG'];
                    }
                }
                $sessionid = rand(10000, 100000);
                $sql_encryt=$dao->AES_ENCRYPT($sessionid, AES_ENCRYPT_KEY);            
                $data1 = array(
                    'SESSION_ID' => base64_encode($sql_encryt)        
                );
                $this->c_update("employees", $data1, $where);
                $sessionEncrypt = base64_encode($sql_encryt);
                $encrypted = $this->rsaencrypt($mobile);
                if ($encrypted != 'False') {
                    $element_array = array('status' => 1, 'Message' => 'success','branch_id'=>$bid,'employee_id'=>$eid,'mobile' => $mobile, 'name' => $name, 'gender' => $gender, 'email' => $email,
                        'homeaddress' => $address, 'homelat' => $lat, 'homelong' => $long,'officeaddress'=>$officeaddr,'officelat'=>$officelat,'officelong'=>$officelong,
                        'category'=>$category,'sessionid' => $sessionEncrypt, 'publickey' => $encrypted,'PAYTM_MID'=>PAYTM_MID,'PAYTM_WEBSITE'=>PAYTM_WEBSITE);
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                }
            } 
             else {
                $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
            }
        }
        else {
                $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
            }
        //echo json_encode($element_array);
        return $element_array;
    }

    public function shuttle_search($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $rtype,$branchid) {
        $dao = new Shuttledao();   
        $this->load->model('ShuttleElasticmdl');
        $getdate=$dao->get_datetime();
        $curdate=$getdate['cur_date'];
        $curtime=$getdate['cur_time'];
        
        if($ttype=="Twoway")
        {
            $pickarray1=$this->ShuttleElasticmdl->getSearchGroup($startlat,$startlong,$branchid,'P');  
            $droparray1=$this->ShuttleElasticmdl->getSearchGroup($endlat,$endlong,$branchid,'D');
        }
        $pickarray=$this->ShuttleElasticmdl->getSearchGroup($startlat,$startlong,$branchid,'P');  
        $droparray=$this->ShuttleElasticmdl->getSearchGroup($endlat,$endlong,$branchid,'D');      
        
        $output = array();
        $output1 = array();
        $element_arr=array();
        $element_arr1=array();
        $package=array();
        $element=array();
        $element1=array();
        $element2=array();
        if(count($pickarray>0) && count($droparray)>0)
        {
           $arrayAB = array_merge($pickarray, $droparray);
            foreach ( $arrayAB as $value ) {
              $id = $value['ROUTE_ID'];
              if ( !isset($output[$id]) ) {
                $output[$id] = array();
              }
              $output[$id] = array_merge($output[$id], $value);
            }
            $spos="";$dpos=""; $p_time="";$d_time="";
           
            foreach ($output as $val)
            {
                $p_time=$this->sec_to_time($val['APROX_PICK']); 
                $d_time=$this->sec_to_time($val['APROX_DROP']); 
                if(date('H:i:s',strtotime($p_time)) < date('H:i:s',strtotime($d_time)))
                {
                    $row=$dao->check_seat_availability($val['ROUTE_ID']); 
                    if($row)
                    {
                        $rosterid=$row['ROSTER_ID'];
                        $routedate=$row['ESTIMATE_END_TIME'];
                        $category=$row['category'];
                        $a_pick=$val['APROX_PICK'];
                        $stime=$row['stime'];                   
                        $secs = strtotime($stime)-strtotime("00:00:00");
                        $a_p_picktime=date("H:i:s",strtotime($p_time)+$secs);

                        $origin=$val['PICK_POSITION'];
                        $destination=$val['DROP_POSITION'];
                        $pick_date=$val['PICK_DATE'];
                        $drop_date=$val['DROP_DATE'];
                        $spos=explode(',',$origin);
                        $dpos=explode(',',$destination);

                        $plat=$spos[0];
                        $plong=$spos[1];
                        $dlat=$dpos[0];
                        $dlong=$dpos[1];
                        $plandmark = $this->getAddress($plat, $plong);
                        $dlandmark=$this->getAddress($dlat, $dlong);
                        $dist = $this->calkm($startlat.','.$startlong, $plat.','.$plong, $startlat.','.$startlong);
                        $dist1 = $this->calkm($endlat.','.$endlong, $dlat.','.$dlong, $endlat.','.$endlong);
                       // $package = array($this->tariff_cal(round($dist, 2), $ttype));
                        $element_arr[]=array('routeno'=>$val['ROUTE_ID'],'roster_id'=>$rosterid,'pick_date'=>$pick_date,'drop_date'=>$drop_date,'route_date'=>$routedate,'dist'=>$dist,'d_dist'=>$dist1,'stime_secs'=>$a_pick,'approxtime'=>$a_p_picktime,'approxdroptime'=>$val['APROX_DROP'],'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'category'=>$category,'package_details' => $package) ;
                   }
                }                
            }
            $element1=array('status' => 1,'Message'=>'success','pickupsearch'=>$element_arr);
        }
        if(count($pickarray1)>0 && count($droparray1)>0)
        {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ( $arrayAB as $value ) {
              $id = $value['ROUTE_ID'];
              if ( !isset($output1[$id]) ) {
                $output1[$id] = array();
              }
              $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos="";$dpos=""; $p_time="";$d_time="";
           
            foreach ($output1 as $val)
            {
                $p_time=$this->sec_to_time($val['APROX_PICK']); 
                $d_time=$this->sec_to_time($val['APROX_DROP']); 
                if(date('H:i:s',strtotime($p_time)) > date('H:i:s',strtotime($d_time)))
                {
                    $row=$dao->check_seat_availability($val['ROUTE_ID']); 
                    if($row)
                    {
                        $rosterid=$row['ROSTER_ID'];
                        $routedate=$row['ESTIMATE_END_TIME'];
                        $category=$row['category'];
                        $a_pick=$val['APROX_PICK'];

                        $stime=$row['stime'];                   
                        $secs = strtotime($stime)-strtotime("00:00:00");
                        $a_p_picktime=date("H:i:s",strtotime($p_time)+$secs);

                        $destination=$val['PICK_POSITION'];
                        $origin =$val['DROP_POSITION'];
                        $pick_date=$val['PICK_DATE'];
                        $drop_date=$val['DROP_DATE'];
                        $spos=explode(',',$origin);
                        $dpos=explode(',',$destination);

                        $plat=$spos[0];
                        $plong=$spos[1];
                        $dlat=$dpos[0];
                        $dlong=$dpos[1];
                        $plandmark = $this->getAddress($plat, $plong);
                        $dlandmark=$this->getAddress($dlat, $dlong);
                        $dist1 = $this->calkm($startlat.','.$startlong, $dlat.','.$dlong, $startlat.','.$startlong);
                        $dist = $this->calkm($endlat.','.$endlong, $plat.','.$plong, $endlat.','.$endlong);
                       // $package = array($this->tariff_cal(round($dist, 2), $ttype));
                        $element_arr1[]=array('routeno'=>$val['ROUTE_ID'],'roster_id'=>$rosterid,'pick_date'=>$pick_date,'drop_date'=>$drop_date,'route_date'=>$routedate,'dist'=>$dist,'d_dist'=>$dist1,'stime_secs'=>$a_pick,'approxtime'=>$a_p_picktime,'approxdroptime'=>$val['APROX_DROP'],'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'category'=>$category,'package_details' => $package) ;
                   }
                }                
            }
            $element2=array('status' => 1,'Message'=>'success','dropsearch'=>$element_arr1);
        }
        
        if($ttype=="Twoway")
        {
            if(count($element_arr)>0 && count($element_arr1)>0)
            {
                $element=  array_merge($element1,$element2);
            }
            else if(count($element_arr)>0 && count($element_arr1)==0)
            {
                $element=array('status' => 1,'Message'=>'success','pickupsearch'=>$element_arr,'dropsearch'=>$element_arr1);
            }
            else if(count($element_arr)==0 && count($element_arr1)>=0)
            {
                $element=array('status' => 1,'Message'=>'success','pickupsearch'=>$element_arr,'dropsearch'=>$element_arr1);
            }
            else if(count($element_arr)==0 && count($element_arr1)==0)
            {
                $element=array('status' => 0,'Message'=>'No routes');
            }
        }
        else
        {
            if(count($element_arr)==0)
            {
               $element=array('status' => 0,'Message'=>'No routes');  
            }
            else{
            $element=$element1;
            }
        }
        
        //echo json_encode($element);
       return $element;
    }
   
    private function tariff_cal($dist, $ttype) {
        $element_array = array();
        $minkm = 0;
        $minamt = 0;
        $extamt = 0;
        $exkm = 0;
        $weekdiscount = 0;
        $monthdiscount = 0;
        $quartdiscount = 0;
        $fare = 0;
        $week = 0;
        $month = 0;
        $quartmont = 0;
        $onedaycost = 0;
        $where = "CHECK_ACTIVE = '1'";
        $row = $this->c_selectrow('shuttle_tariff', $where);
        if ($row) {
            $minkm = $row['MINIMUM_KM'];
            $minamt = $row['MINIMUM_AMOUNT'];
            $extamt = $row['SIXTO20KM'];
            $weekdiscount = $row['WEEKLY_DISCOUNT'];
            $monthdiscount = $row['MONTHLY_DISCOUNT'];
            $quartdiscount = $row['QUARTELY_DISCOUNT'];
            if ($dist > $minkm) {
                $exkm = $dist - $minkm;
                $fare = $minamt + ($exkm * $extamt);
            } else {
                $fare = $minamt;
            }
            $onedaycost = round($fare);
            if ($weekdiscount > 0) {
                $week = round(($fare * 5) - (($fare * 5 / 100) * $weekdiscount));
            } else {
                $week = $fare * 5;
            }
            if ($monthdiscount > 0) {
                $month = round(($fare * 30) - (($fare * 30 / 100) * $monthdiscount));
            } else {
                $month = $fare * 30;
            }
            if ($quartdiscount > 0) {
                $quartmont = round(($fare * 90) - (($fare * 90 / 100) * $quartdiscount));
            } else {
                $quartmont = $fare * 90;
            }
            //  if($ttype=='P'||$ttype=='D')
            if ($ttype == 'Oneway') {
                $week = $week;
                $month = $month;
                $quartmont = $quartmont;
            }
            /* else
              {
              $week=$week*2;
              $month=$month*2;
              $quartmont=$quartmont*2;
              } */
            $element_array = array('oneday' => $onedaycost, 'weekly' => $week, 'wdiscount' => $weekdiscount, 'monthly' => $month, 'mdiscount' => $monthdiscount, 'quartely' => $quartmont, 'qdiscount' => $quartdiscount);
            return $element_array;
        }
    }

    public function shuttle_routepath($routeid, $approxptime, $approxdtime, $ct) {
        $element_array = array();
        //$where = "RouteId='$routeid' and Duration between '$approxptime' and '$approxdtime' order by Sno";
        $where = "RouteId='$routeid' order by Sno";
        $row = $this->c_selectarray('shuttle_route_path', $where);
        if (count($row) > 0) {            
            foreach($row as $val) {
                $element_array[] = array('plat' => $val['Latitude'], 'plong' => $val['Longitude']);
            }
        }
        $element_arr = array('routepath' => $element_array);
        echo $this->output($element_arr, $ct);
    }

    public function shuttle_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $fare,$dist,$rtype,$shuttledate,$rosterid) {       
        $element_array=array();
        $dao = new Shuttledao();         
        $getdate=$dao->get_datetime();
        $curdate=$getdate['cur_date'];
        $cur_time=$getdate['cur_date_time'];
        
        if($rtype=="Regular")
        {
            $date=date("Y-m-04");
            $enddate = date('Y-m-d', strtotime('+1 month', strtotime($date))); 
        }
        else
        {
            $shuttledate=$curdate;
            $enddate=$curdate;
        }
        
        $start = strtotime($shuttledate);
        $end = strtotime($enddate);
        $count = 1;
        while(date('Y-m-d', $start) < date('Y-m-d', $end)){
          $count += date('N', $start) < 6 ? 1 : 0;
          $start = strtotime("+1 day", $start);
        }
        $tariff=$this->tariff_cal($dist, $ttype);
        $perdaycost=$tariff['oneday'];
        $totfare=$count*$perdaycost;
        $data = array('EmployeeId' => $eid,'RosterId'=>$rosterid, 'PickupPoint' => $ppoint, 'DropPoint' => $dpoint, 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $dist, 'PackageAmt' => $totfare, 'ProcessDatetime' => $cur_time,
            'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'Duration' => $rtype, 'StartDate' => $shuttledate, 'EndDate' => $enddate,'NoofDays'=>$count);
        $cnt = $dao->c_insert('shuttle_booking_master', $data);
        $id = $this->db->insert_id();
        if ($cnt > 0) {
            $paymentno = date('YmdHis') . $id;
            $data = array('PaymentNo' => $paymentno);
            $where = "Sno=$id";
            $cnt1=$dao->c_update('shuttle_booking_master', $data, $where);
            if($cnt1>0)
            {
//                $totamt=0;
//                $carryforwardtrip=0;$carryforwardamt=0;$cno=0;
//                $where1="EmployeeId='$eid' and PaymentTranactionNo!='' and PaymentRemarks='Txn Successful.' and CarryForwardSno=0";
//                $row=$dao->c_selectrow('shuttle_booking_master', $where1);
//                if($row)
//                {
//                    $carryforwardtrip=$row['CarryForwardTrip'];
//                    $carryforwardamt=$row['CarryForwardAmt'];
//                    $cno=$row['Sno'];
//                }
               // $totamt=$totfare-$carryforwardamt;
                $element_array = array('status' => 1, 'message' => 'success', 'paymentno' => $paymentno,'sdate'=>$shuttledate,'edate'=>$enddate,'noofdays'=>$count);
            }
        }
        else
        {
            $element_array = array('status' => 0, 'message' => 'Please try again');
        }
        //echo json_encode($element_array);
        return $element_array;       
    }

    public function shuttle_payment($MID, $ORDERID, $TXNID, $BANKTXNID, $TXNAMOUNT, $TXNTYPE, $GATEWAYNAME, $RESPCODE, $RESPMSG, $BANKNAME, $PAYMENTMODE, $REFUNDAMT, $TXNDATE, $STATUS, $id, $sdate, $duration,$carryforward_no) {        
        $response_message = array();        
        $dao = new Shuttledao();         
        $getdate=$dao->get_datetime();
        $curdate=$getdate['cur_date'];
        $cur_time=$getdate['cur_time'];
        $curdate_time=$getdate['cur_date_time'];
        
        $data = array('MID' => $MID, 'ORDERID' => $ORDERID, 'TXNID' => $TXNID, 'BANKTXNID' => $BANKTXNID, 'TXNAMOUNT' => $TXNAMOUNT, 'TXNTYPE' => $TXNTYPE, 'GATEWAYNAME' => $GATEWAYNAME,
            'RESPCODE' => $RESPCODE, 'RESMSG' => $RESPMSG, 'BANKNAME' => $BANKNAME, 'PAYMENTMODE' => $PAYMENTMODE, 'TXNDATE' => $TXNDATE, 'REFUNDAMT' => $REFUNDAMT, 'STATUS' => $STATUS, 'PROCESS_DATETIME' => $cur_time);
        $cnt=$dao->c_insert('shuttle_payment_gateway', $data);
        if ($cnt > 0) {
            if ($STATUS == "TXN_SUCCESS" && $RESPMSG == "Txn Successful.") {
                $empid = "";
                $stime = "";
                $etime = "";
                $ppoint = "";
                $dpoint = "";
                $tmode = "";
                $pamt = 0;
                $where = "PaymentNo=$ORDERID";
                $row = $dao->c_selectrow('shuttle_booking_master', $where);
                if ($row) {
                    $Sno = $row['Sno'];
                    $stime = $row['StartTime'];
                    $etime = $row['EndTime'];
                    $ppoint = $row['PickupPoint'];
                    $dpoint = $row['DropPoint'];
                    $tmode = $row['TravelMode'];
                    $pamt = $row['PackageAmt'];
                    $dist=$row['PackageKm'];
                    $rosterid=$row['RosterId'];
                   //$picklat=$row['PickLatitude'];
                   //$picklong=$row['PickLongitude'];
                    $empid=$row['EmployeeId'];
                    // }
                    $enddate = "";
                    $date_from = $sdate;
                    $date_from = strtotime($date_from); // Convert date to a UNIX timestamp
                    // Specify the end date. This date can be any English textual format
//                    $carryforwardamt=0;
//                    if($carryforward_no>0)
//                    {
//                       $where2 = "Sno=$carryforward_no"; 
//                       $row1 = $dao->c_selectrow('shuttle_booking_master', $where2);
//                       if($row1)
//                       {
//                           $carryforwardamt=$row1['CarryForwardAmt'];
//                           $arr=array('CarryForwardSno'=>$Sno);
//                           $dao->c_update('shuttle_booking_master', $arr, $where2);
//                       }
//                    }                    
                    $transamt=$pamt-$carryforwardamt;
                    if ($transamt == $TXNAMOUNT) {
                        $where1 = "PaymentNo=$ORDERID";
                        $data1 = array('PaymentMode' => $PAYMENTMODE, 'PaymentTranactionNo' => $TXNID, 'PaymentDateTime' => $TXNDATE, 'PaymentRemarks' => $RESPMSG,
                            );
                        $ucnt=$dao->c_update('shuttle_booking_master', $data1, $where1);
                        if ($ucnt > 0) {
                            if ($duration == 'Regular') {                               
                                $date=date("Y-m-04");
                                $enddate = strtotime ( '+1 month' , strtotime ( $date));
                            } else {
                                $enddate = $sdate;                                
                                $data=array('EMPLOYEE_ID'=>$empid,'ROSTER_ID'=>$rosterid,'ESTIMATE_START_TIME'=>$curdate_time,'ACTIVE'=>1,'ROUTE_ORDER'=>$dist,
                                    'CREATED_BY'=>$empid,'CREATED_DATE'=>$curdate_time);
                            }
                            $date_to = $enddate;
                            $date_to = strtotime($date_to);
                            if ($sdate >= $curdate && $enddate >= $curdate) {
                                for ($i = $date_from; $i <= $date_to; $i+=86400) {
                                    if (date("w", strtotime(date("Y-m-d", $i))) == 6 || date("w", strtotime(date("Y-m-d", $i))) == 0) {
                                        
                                    } else {
                                        $data = array('BookingMasterId' => $Sno, 'TravelMode' => $tmode, 'StartTime' => $row['StartTime'], 'EndTime' => $row['EndTime'], 'ProcessDatetime' => $cur_time,
                                            'TravelDate' => date("Y-m-d", $i));
                                        $dao->c_insert('shuttle_booking_trans', $data);
                                    }
                                }
                            }
                            $response_message = array('status' => 1);
                        } else {
                            $response_message = array('status' => 0);
                        }
                    } else {
                        $response_message = array('status' => 0);
                    }
                } else {
                    $response_message = array('status' => 0);
                }
            } else {
                $where = "PaymentNo=$ORDERID";
                $data1 = array('PaymentMode' => $PAYMENTMODE, 'PaymentTranactionNo' => $TXNID, 'PaymentDateTime' => $TXNDATE, 'PaymentRemarks' => $RESPMSG);
                $dao->c_update('shuttle_booking_master', $data1, $where);
                $response_message = array('status' => 1);
            }
        } else {
            $response_message = array('status' => 0);
        }
        return $response_message;       
    }

    public function profile_update($emergencyno, $homeaddr, $homelat, $homelong, $id) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array=array();
       // $data4 = array();         
        $dao = new Shuttledao();         
        $getdate=$dao->get_datetime();
        $curdate_time=$getdate['cur_date_time'];
        
        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' => $emergencyno);
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        $data4=array('UPDATED_BY'=>$id,'updated_at'=>$curdate_time);
        $data5 = array_merge($data2, $data3,$data4);
        $where = "EMPLOYEES_ID='$id' and ACTIVE='1'";
        $cnt= $dao->c_update('employees', $data5, $where);
       
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 1, 'Message' => "Please try again");
        }
        return $element_array;        
    }

    public function shuttleimage_upload($img) {
        $UPLOAD_DIR = "../shuttle_profile/";
        $data = base64_decode($img);
        $file = $UPLOAD_DIR . uniqid() . '.png';
        file_put_contents($file, $data);
        return $file;
    }

    public function forgot_password($mobile, $otpnew) {
        $smsmsg = "";
        $smssts = "";        
        $dao = new Shuttledao();        
        $getdate=$dao->get_datetime();
        $curdatetime=$getdate['cur_date_time'];       
        if ($otpnew == "") {
            $otp = $this->getrandomnumber1();
            $where = "MOBILE = '$mobile' AND ACTIVE=1";
            $row = $this->c_selectrow('employees', $where);
            if ($row) {
                $ename = $row['NAME'];
                $eid=$row['EMPLOYEES_ID'];
                $smsmsg = "Dear $ename, Your login OTP is $otp. Please use this OTP in corporate COG MATS APP. Thanks ";
                $smssts = $this->sendsms($mobile, $smsmsg);
                $data1 = array('MOBILE_NO'=>$mobile,'ROSTER_PASSENGER_ID'=>$eid,'OTP'=>$otp,'VERIFIED_STATUS'=>0,
                    'SMS_RESPONSE' => $smssts,'OTP_CATEGORY'=>'Forgotpw','CREATED_BY'=>$eid,'CREATED_DATE'=>$curdatetime                   
                );
                $cnt = $dao->c_insert('otp_verification', $data1);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Please try again");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "Mobile no not registered");
            }
        } else {
            $where = "MOBILE_NO = '$mobile' and OTP='$otpnew' and OTP_CATEGORY='Forgotpw' and VERIFIED_STATUS=0 and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";            
            $data=array('VERIFIED_STATUS'=>1);
            $cnt = $dao->c_update('otp_verification',$data, $where);
            if ($cnt>0) {
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid OTP");
            }
        }        
        return $element_array;
    }

    public function generate_newpassword($mobile,$newpassword) {
        $element_array = array();
        $dao = new Shuttledao();        
        $getdate=$dao->get_datetime();
        $curdatetime=$getdate['cur_date_time'];   
        //$ENCRYP_PWD = $this->Encrypt_Script($newpassword, SECERT_KEY_NTL);
        $where = "MOBILE='$mobile' and ACTIVE=1";
        $data1 = array(
            'password' => $newpassword,
            'updated_at'=>$curdatetime
        );
        $cnt = $dao->c_update('employees', $data1, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Invalid OTP");
        }
        return $element_array;      
    }

    public function change_password($oldpass, $newpass, $mobile) {
        $dao = new Shuttledao();        
        $getdate=$dao->get_datetime();
        $curdatetime=$getdate['cur_date_time'];
        $where = "MOBILE='$mobile' and password='$oldpass' and ACTIVE=1";
        $data = array(
            'password' => $newpass,
            'updated_at' => $curdatetime
        );
        $cnt = $dao->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Your old password is wrong");
        }
        return $element_array;       
    }

    public function booking_details($id) {
        $element_arr = array();
        $dao = new Shuttledao();  
        $where = "EMPLOYEES_ID='$id' AND ACTIVE = '1'";
        $row = $dao->c_selectrow('employees', $where);
        //echo $this->db->last_query();
        if ($row) {
            $where1 = "EmployeeId='$id'";//StartDate DESC
            $row1 = $dao->c_selectarray('shuttle_booking_master', $where1);           
            foreach($row1 as $val) {
                $psts = "Paid";
                $transno = $val['PaymentTranactionNo'];
                if ($transno == "") {
                    $psts = "Not Paid";
                }
                $element_arr[] = array('Sno' => $val['PaymentNo'], 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'Duration' => $val['Duration'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => (int) $val['PackageAmt'], 'PaymentStatus' => $psts);
            }            
        }
        $element_array = array('booking_details' => $element_arr);
        return $element_array;        
    }

    public function booking_list($eid) {       
        $element_arr = array();
        $element_arr1 = array();            
        $dao = new Shuttledao(); 
        $getdate=$dao->get_datetime();
        $curdate=$getdate['cur_date'];
        $cabid=0;
            
        $row1=$dao->booking_list_dao($eid,'upcoming');
        foreach ($row1 as $val) {
            $cancelsts = "disable";
            $traveldate=$val['TravelDate'];
            $canceldate = date('Y-m-d', strtotime('+2 day', strtotime($curdate)));
            if ($traveldate > $canceldate) {
                $cancelsts = "enable";
            } 
            else 
            {
                if($traveldate==$curdate)
                {
                    $row=$dao->get_cabid($eid);
                    if($row)
                    {
                        $cabid=$row['CAB_ID'];
                    }
                }
                else
                {
                    $cancelsts = "disable";
                }
            }
            $element_arr[] = array('PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelType' => $val['TravelMode'], 'TravelDate' => $val['TravelDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'CancelSts' => $cancelsts, 'BookingID' => $val['BookingTransId'],'CabId'=>$cabid);
        }        
       
        $row2 = $dao->booking_list_dao($id,'completed');
        foreach ($row2 as $val2) {
            $element_arr1[] = array('PickupPoint' => $val2['PickupPoint'], 'DropPoint' => $val2['DropPoint'], 'TravelType' => $val2['TravelMode'], 'TravelDate' => $val2['TravelDate'], 'StartTime' => $val2['StartTime'], 'EndTime' => $val2['EndTime'], 'CancelSts' => 'Completed', 'BookingID' => $val2['BookingTransId']);
        }
        
        $element_array = array('booking_list' => $element_arr, 'completed_list' => $element_arr1);
        return $element_array;       
    }

    public function booking_edit($id, $intime, $outtime, $ct) {
        $element_array = array();
        $tdate = "";
        $where = "BookingTransId='$id'";
        $data = array('StartTime' => $intime, 'EndTime' => $outtime);
        $this->c_update('shuttle_booking_trans', $data, $where);
        if ($this->db->affected_rows() > 0) {
            $element_array = array('status' => 'true');
        } else {
            $element_array = array('status' => 'false');
        }
        echo $this->output($element_array);
    }

    public function shuttle_support() {
        $tmp = array();
        $dao = new Shuttledao(); 
        $where="ACTIVE=1 and CATEGORY='Shuttle'";
        $row=$dao->c_selectarray('reason_master', $where); 
        foreach($row as $arg)
        {
            $tmp[$arg['SUPPORT_CATEGORY']][] = $arg['SUB_SUPPORT_CATEGORY'].'@@#'.$arg['REASON'].'@@#'.$arg['REASON_ID'];
        }
        
        $output = array();

        foreach($tmp as $type => $labels)
        {
            $output[] = array(               
                'category' => $type,
                'sub_category' => $labels
            );
        }
      return array('Apiresponse' => $output);
    }
    public function shuttle_support_log($bid,$empid,$reasonid,$remarks)
    {
        $element_array=array();
        $sts="false";
        $dao = new Shuttledao(); 
        $getdate=$dao->get_datetime();
        $curdatetime=$getdate['cur_date_time'];
        $data=array('EMPLOYEE_ID'=>$empid,'REASON_ID'=>$reasonid,'REMARKS'=>$remarks,'PROCESS_TIME'=>$curdatetime,'BRANCH_ID'=>$bid);
        $cnt=$dao->c_insert('support_log', $data);
        if($cnt>0)
        {
            $sts="true";            
        }
        $element_array = array('Apiresponse' => $sts);
        return $element_array;
    }

    public function shuttle_issue() {
        $element_arr = array();//shuttle_issue
        $where="Active=1";
        $dao = new Shuttledao(); 
        $row=$dao->c_selectarray('shuttle_issue', $where); 
        foreach ($row as $val) {
            $element_arr[] = array('Sno'=>$val['Sno'], 'IssueName' => $val['IssueName'], 'IssueSubName' => $val['IssueSubName'], 'IssueDetails' => $val['IssueDetails']);
        }
        $element_array = array('issue_det' => $element_arr);
        return $element_array;
    }
    
     public function emp_app_panic($empid,$cab_id, $roster_id, $lat, $lng, $location) {
        $element_array = array();
        //create object
        $dao = new Shuttledao();       
        $getdate=$dao->get_datetime();
        $cur_time=$getdate['cur_date_time'];
        
        $data = array('EMPLOYEE_ID'=>$empid,'ROSTER_ID' => $roster_id, 'CAB_ID' => $cab_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        $cnt = $dao->c_insert('panic_alert', $data);
        if ($cnt > 0) {
            $element_array = array('Apiresponse' => 'True');
        } else {
            $element_array = array('Apiresponse' => 'False');
        }
        return $element_array;
    }
    
    public function insert_route_master($startpoint,$endpoint,$startlat,$startlong,$endlat,$endlong,$duration,$stime,$etime,$routepath,$ct)
    { 
        $dao = new Shuttledao();         
        $getdate=$dao->get_datetime();       
        $cur_time=$getdate['cur_date_time'];
        
        $tottime=$this->sec_to_time($duration);
        $this->load->model('ShuttleElasticmdl');
        $data = array('StartPoint' => $startpoint, 'EndPoint' => $endpoint, 'Start_Latitude' => $startlat, 'Start_Longitude' => $startlong, 'End_Latitude' => $endlat, 
            'End_Longitude' => $endlong, 'StartTime' => $stime, 'EndTime' => $etime, 'Travel_Duration' => $tottime,'Process_Datetime' => $cur_time);
        $cnt = $dao->c_insert('shuttle_route_master', $data);
        $id = $this->db->insert_id();
        if ($cnt > 0) {
            $userColData = json_decode($routepath, true);
            $path=$userColData['RoutePath'];
            $this->ShuttleElasticmdl->pathInsert($id,'1',$duration,$path,$startlat.','.$startlong);           
        }
    }
    
    public function shuttle_notification($eid,$bid) {
        $element_arr = array();        
        $dao = new Shuttledao();         
      
        $where = "status=1 and emp_id='$eid' and branch_id='$bid'";
        $row=$dao->c_selectarray('shuttle_notification', $where); 
        foreach($row as $val) {
            $element_arr[] = array('Heading' => $val['heading'], 'Message' => $val['message'], 'DateTime' => $val['created_at']);
        }
        $element_array = array('notification' => $element_arr);        
        return $element_array;
    }

    public function shuttle_invoice($bookingno) {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function shuttle_termscondition() {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }
    
    public function tracking($rosterid,$cabid,$eid,$gpsdate) {         
        $dao = new Shuttledao();  
        $this->load->model('ShuttleElasticmdl');
        $getdate=$dao->get_datetime();       
        $cur_datetime=$getdate['cur_date_time'];
        $cur_date=$getdate['cur_date'];
        $data = array();
        $element_arr1 = array();
        $element_arr = array();  
        $tripsts="";
        $tripmsg="";
        $drivername="";
        $drivermobile="";
        $vehno="";
        //employee details
        $row1 = $dao->track1($rosterid, $cabid, $eid);       
        if($row1){
            $cab_arrived_time=$row1['cab_arrived_time'];
            $rsts=$row1['ROSTER_PASSENGER_STATUS'];
        if (is_null($cab_arrived_time)) {
            $tripsts = "1";
            $tripmsg = "Live Tracking";
        } else if ((!is_null($cab_arrived_time)) && (!in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) ) {
            $tripsts = "2";
            $tripmsg = "Cab reached your Location.OTP is " . $row1['OTP'] . " ";
        } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
            $tripsts = "3";
            $tripmsg = "You have entered the cab.Have a safe journey!!!!";
        }
        $order = $row1['ROUTE_ORDER'];       
        $waypoints = '';
        $row2 = $dao->track2($rosterid, $cabid, $order);
        if(count($row2)>0)
        {
            foreach($row2 as $val)
            {               
                $element_arr1[] = array('emplat' => $val['LATITUDE'], 'emplong' => $val['LONGITUDE'], 'pickuptime' => $val['picktime'], 'empname' => $val['NAME'], 'emplocation' => $val['LOCATION_NAME']);               
                $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";               
            }
        } else {
            $element_arr1[] = array('emplat' => $row1['LATITUDE'], 'emplong' => $row1['LONGITUDE'], 'pickuptime' => $row1['picktime'], 'empname' => $row1['NAME'], 'emplocation' => $row1['LOCATION_NAME']);
            $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
        }
        //$cabid=1;
        //driver details
        //echo "gps==".$gpsdate;
        if(str_replace('+', " ", $gpsdate)=="1900-01-01 00:00:00"){
            $driverdet=$dao->get_driver_details($cabid);
            if($driverdet)
            {
                $drivername=$driverdet['DRIVERS_NAME'];
                $drivermobile=$driverdet['DRIVER_MOBILE'];   
                $vehno=$driverdet['DRIVER_MOBILE']; 
            }
            $ret=$this->ShuttleElasticmdl->cabNavigation($cabid,'1900-01-01 00:00:00');
            $origin=$ret['RESULT'][0]['POSITION'];
            $driverimage = mysql_real_escape_string("https://mytransportportal.com/cog_track/img/driver_img/photo.jpg");
            $km = $this->calkm($origin, $destination, $waypoints);
            $x = explode("-", $km);
            if ($x[1] > 0) {
                $hours = floor($x[1] / 3600);
                $mins = floor($x[1] / 60 % 60);
                if ($hours == 0) {
                    $minutes = $mins . " mins";
                } else {
                    $minutes = $hours . 'hrs -' . $mins . " mins";
                }
            } else {
                $minutes = "NA";
            }
            $data[] = array('DriverName' => $drivername,
                "DriverMobile" => $drivermobile,
                "CabNo" => $vehno,
                "cabLatLong" => $origin,                
                "TripStatus" => $tripsts,
                "TripMsg" => $tripmsg,                
                "DriverImage" => $driverimage, "ETA" => $minutes);
           }
           
            $element_arr=$this->ShuttleElasticmdl->cabNavigation($cabid,$gpsdate);   
            $gps_det=$element_arr['RESULT'];
        }
        $element_arr=$this->ShuttleElasticmdl->cabNavigation($cabid,  str_replace('+', " ", $gpsdate));   
        $gps_det=$element_arr['RESULT'];
       //$gpsdate="2017-01-04 17:20:10";
       
        $element = array('DriverDetails' => $data, "details" => $gps_det, 'empdetails' => $element_arr1);
        //echo json_encode($element);
       
        return $element;
    }
    
    public function employee_noshow($employee_id, $roster_passenger_id, $emplat, $emplong, $noshow_reason_id, $trip_type, $roster_id, $cab_id, $branch_id, $decrypted) {
        $ret=false;
       //create object
        $dao = new Shuttledao();         
        //get current date time
        $getdate=$dao->get_datetime();
        $cur_time=$getdate['cur_date_time'];
       
        $response = $dao->employee_noshow_fun($roster_passenger_id, $emplat, $emplong, $trip_type, $roster_id, $cab_id, $branch_id);       
        if ($response == 1) {
            $data = array('ROSTER_PASSENGER_ID' => $roster_passenger_id, 'REASON_ID' => $noshow_reason_id, 'CREATED_DATE' => $cur_time);
            $cnt = $dao->c_insert('reason_log', $data);
            if ($cnt > 0) {                
                $ret = $dao->employee_tripstatus_update($trip_type, $roster_id);
                $escort_property = $this->property_check('ESCORT', $branch_id);               
                if ($escort_property == 'true') {
                    $this->check_escort_status($trip_type, $roster_id, $cab_id);
                }
            }
        }
        $element_array = array('Apiresponse' => $ret);        
        //$res = json_encode($element_array);
        return $element_array;
    }
    public function c_insert($tablename, $values) {
        $this->db->set($values);
        $this->db->insert($tablename, $this);
        if ($this->db->affected_rows() > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function c_update($tablename, $values, $where) {
        $where1 = $where;
        if ($where1 != '') {
            $this->db->where($where1);
            $this->db->update($tablename, $values);
            if ($this->db->affected_rows() > 0) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public function c_selectrow($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            return $row1;
        }
    }

    public function c_selectarray($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->result_array();
            return $row1;
        }
    }

    public function output($element_array, $ct) {
        if ($ct == "ip") {
            return json_encode($element_array);
        } else {
            $string = json_encode($element_array);
            $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
            $valreturn = array('xyssdff' => $returnval);
            return json_encode($valreturn);
        }
    }

    function encrypt($message, $initialVector, $secretKey) {
        return base64_encode(
                mcrypt_encrypt(
                        MCRYPT_RIJNDAEL_128, md5($secretKey), $message, MCRYPT_MODE_CFB, $initialVector
                )
        );
    }

    function decrypt($data, $key, $secretKey) {
        $decode = base64_decode($data);
        return mcrypt_decrypt(
                MCRYPT_RIJNDAEL_128, md5($key), $decode, MCRYPT_MODE_CFB, $secretKey
        );
    }

    private function record_sort($records, $field, $reverse = false) {
        $hash = array();

        foreach ($records as $record) {
            $hash[$record[$field]] = $record;
        }

        ($reverse) ? krsort($hash) : ksort($hash);

        $records = array();

        foreach ($hash as $record) {
            $records [] = $record;
        }

        return $records;
    }

    public function getrandomnumber() {
        $a = 0;
        for ($i = 0; $i < 6; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(100000, 999999);

        return $a;
    }

    public function getrandomnumber1() {
        $a = 0;
        for ($i = 0; $i < 4; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(1000, 9999);

        return $a;
    }

    function Encrypt_Script($string, $key) {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result.=$char;
        }
        return base64_encode($result);
    }

    function Decrypt_Script($string, $key) {
        $result = '';
        //$string = base64_decode($string);

        for ($i = 0; $i < strlen($string); $i++) {

            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) - ord($keychar));
            $result.=$char;
        }
        return $result;
    }

    function sec_to_time($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor($seconds % 3600 / 60);
    $seconds = $seconds % 60;

    return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
  } 
    public function sendsms($mobile, $sms) {
        $msg = urlencode($sms);
        $urltopost = "http://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=ntltaxi1&send=CTSCKC&dest=$mobile&msg=" . $msg;
        $ch = curl_init($urltopost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //Sms_Response
        $returndata = curl_exec($ch);
        return $returndata;
    }

    function getAddress($latitude, $longitude) {
        if (!empty($latitude) && !empty($longitude)) {
            //Send request and receive json data by address
            $geocodeFromLatLong = file_get_contents('http://maps.googleapis.com/maps/api/geocode/json?latlng=' . trim($latitude) . ',' . trim($longitude) . '&sensor=false');
            $output = json_decode($geocodeFromLatLong);
            $status = $output->status;
            //Get address from json data
            $address = ($status == "OK") ? $output->results[1]->formatted_address : '';
            //Return address of the given latitude and longitude
            if (!empty($address)) {
                return $address;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    function encodeBase64UrlSafe($value) {
        return str_replace(array('+', '/'), array('-', '_'), base64_encode($value));
    }

    function decodeBase64UrlSafe($value) {
        return base64_decode(str_replace(array('-', '_'), array('+', '/'), $value));
    }

    function signUrl($myUrlToSign, $privateKey) {
        // parse the url
        $url = parse_url($myUrlToSign);
        $urlPartToSign = $url['path'] . "?" . $url['query'];

        // Decode the private key into its binary format
        $decodedKey = $this->decodeBase64UrlSafe($privateKey);

        // Create a signature using the private key and the URL-encoded
        // string using HMAC SHA1. This signature will be binary.
        $signature = hash_hmac("sha1", $urlPartToSign, $decodedKey, true);

        $encodedSignature = $this->encodeBase64UrlSafe($signature);

        return $myUrlToSign . "&signature=" . $encodedSignature;
    }

    function calkm($origin, $destination, $waypoints) {
        $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        //echo $response;
        curl_close($ch);

        $data = json_decode($response);

        $km = 0;
        $tim = "";
        // If we got directions, output all of the HTML instructions
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                //foreach ($leg->steps as $step) {
                $i = 0;
                foreach ($leg->distance as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        $x = explode(" ", $val);
                        if ($x[1] == 'km') {
                            $km+=$x[0];
                        } else {
                            $km+=($x[0] * 0.001);
                        }
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        
                    } else {
                        $tim +=$val;
                    }
                }
            }
            //  return $km . '-' . $tim;
            return $km;
        }
    }

    
}
