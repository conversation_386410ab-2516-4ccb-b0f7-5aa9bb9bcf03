<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Display Debug backtrace
|--------------------------------------------------------------------------
|
| If set to TRUE, a backtrace will be displayed along with php errors. If
| error_reporting is disabled, the backtrace will not display, regardless
| of this setting
|
*/
defined('SHOW_DEBUG_BACKTRACE') OR define('SHOW_DEBUG_BACKTRACE', TRUE);

/*
|--------------------------------------------------------------------------
| File and Directory Modes
|--------------------------------------------------------------------------
|
| These prefs are used when checking and setting modes when working
| with the file system.  The defaults are fine on servers with proper
| security, but you may wish (or even need) to change the values in
| certain environments (Apache running a separate process for each
| user, PHP under CGI with Apache suEXEC, etc.).  Octal values should
| always be used to set the mode correctly.
|
*/
define("AES_ENCRYPT_KEY",'flowercat_123456');//key length must 16,24 or 32
define("GOOGLE_MAP_API_KEY","AIzaSyBJKhn3Vq9BR2JWrynMpshx3xAkj0rsm-0");//AIzaSyDE5GX52_ZsLxwY2dsRxwuzQIy-FCmsdv0-old

//paytm details
define("PAYTM_MID",'NTLIND24898271393596');
define("PAYTM_WEBSITE_WEB",'NTLINDWEB');
define("PAYTM_WEBSITE_APP",'NTLINDWAP');
define('PAYTM_MERCHANT_KEY','TSxQO!GfgPv%NByp');
define('PAYTM_INDUSTRY_TYPE_ID','Travel');
define('PAYTM_CHANNEL_ID_WEB','WEB');
define('PAYTM_CHANNEL_ID_APP','WAP');
define('PAYTM_SERVER_ADDMONEY','https://secure.paytm.in/oltp-web/processTransaction');
define('PAYTM_SERVER_MONEY_WITHDRAW','https://secure.paytm.in/oltp/HANDLER_FF/withdrawScw');
define('PAYTM_REFUND_URL','https://secure.paytm.in/oltp/HANDLER_INTERNAL/REFUND');
define('PAYTM_REFUND_STS_URL','https://secure.paytm.in/oltp/HANDLER_INTERNAL/REFUND_STATUS');
define('PAYTM_TXNSTATUS_URL','https://secure.paytm.in/oltp/HANDLER_INTERNAL/TXNSTATUS');

//https://secure.paytm.in/oltp/HANDLER_INTERNAL/TXNSTATUS



define("PAYTM_CLIENT_ID", "merchant-ntlindia");
define("PAYTM_CLIENT_SECRET", "76e37782-3c18-4537-8e8b-bfb748d0deb5");
define("SMS_TITLE","GTAXIE Shuttle Ride");

define("JM_CLIENT_ID",'10001380');
define("JM_MERCHANT_ID",'100001001566949');
define("JM_CHECKSUM_SEED",'FaKgJepvjXRh4ifOPYvZVAIkhRnIzqS8I35b8Q5GLDbPQoaQ4ryUpyDUxqXtXCaL3GIclpfv9DQ0Erh6yyolGent5C7oDuMrRuQhAKKLpe5qCYaGPcL7AfbMXvs0mqt5d7y6afsCGX4mw8TrJDWCYlbogtoc98ZxF6OLrb6GlUoDUUFVSEvZ1qXgTjacpofQArI1Otruef3bXmbQ4EpTmgUV96bx3wyhy3bEBmFHJQIozbcEBGz7KURbNvbm70ir');
define('JM_RETURN_URL','https://topsa.in/JIO/jio_response.php');
define('JM_BALANCE_URL','https://pp2pay.jiomoney.com/reliance-webpay/v1.0/payment/apis');
define('JM_REGISTRATION_URL','https://pp2bill.jiomoney.com:8443/Services/CustomerManagement');


define("CCA_MERCHANT_ID", "3660227");
define("CCA_ACCESS_CODE", "AVUG01LG00AD77GUDA");
define("CCA_WORKING_KEY", "3ABEBBBD423435E59E71781AA310715D");

define("EMP_CATEGORY","Shuttle");
define('TRIP_P','P');
define('TRIP_D','D');
define('SHUTTLE_CANCEL_HR',2);
define("SHUTTLE_TRIP_ONDAY",'oneday');
define("SHUTTLE_TRIP_REGULAR",'regular');
define("SHUTTLE_RESHEDULE_TIME_LIMIT",1);
define("SHUTTLE_CANCEL_TIME_LIMIT",6);
define("BUFFER_TIME",'00:15:00');
define("TRIP_TIME",4);


define("RP_NOSHOW",16);//add this number if employee is absent
define("RP_DROP_DEBOARD",256);

//define('SHUTTLE_RENEWAL_DATE',date("Y-m-04"));


//ENCRYPTION AND DECRYPTION KEY
defined('DECRYPT_KEY1')  OR define('DECRYPT_KEY1', 'Vp91SGZFVBSJZT5V');
defined('DECRYPT_KEY2')  OR define('DECRYPT_KEY2', 'PiaP79bERDCVUagn');

defined('ENCRYPT_KEY1')  OR define('ENCRYPT_KEY1', '1*b:@+R)0e@/iV!>');
defined('ENCRYPT_KEY2')  OR define('ENCRYPT_KEY2', 'h^8q.(t%X@3?6-c%');
defined('SECERT_KEY_NTL') OR define('SECERT_KEY_NTL', 'N!@#$%^&*()B!@#$%^&*()A!@#$%^&*()');

//tracking
define("RP_CREATED",serialize(array (1,3)));//roster created
define("R_CAB_TRACKING",serialize(array (89,91,93,95)));
define("RP_ARRIVED_BOARDED",serialize(array (5,7,13,15,69,71)));
define("RP_PICKUP_DROP_NOSHOW",serialize(array (16,17,19,21,23,29,31)));//pickup noshow
define("RP_PICKUP_DROP_OTP",serialize(array (37,39,45,47,101,103,109,111,133,135,141,143))); //otp entered status for pickup and drop
define("R_TRIP_CLOSE_HISTORY",serialize(array (345,347,349,351,857,859,861,863,1113,1115,1117,1119,16477,1033,1037)));
define("R_TRIP_CLOSE",1000);
define("R_TRIP_ACCEPTED",serialize(array(25,27,29,31)));
define("RP_DROPPED",serialize(array(37,39,101,103,133,135,197,199)));
define("RP_DROP_DEBOARD_CONFIRM",serialize(array (293,295,357,359,389,391,453,455))); 

define("GST",12);
define("LOGO","https://topsa.in/TOPSA_EMP/assets/img/NTL-New.png");


//properties
define ("CANCELLATION_POLICY_ONEDAY","CANCELLATION POLICY ONEDAY");
define ("CANCELLATION_POLICY_REGULAR","CANCELLATION POLICY REGULAR");
define ("BOOKING_TIME_DIFFERENCE","BOOKING_TIME_DIFFERENCE");
define ("OFFICE_ADDRESS","OFFICE_ADDRESS");
define ("CORPORATE_SHUTTLE_ROSTER","CORPORATE_SHUTTLE_ROSTER");
define ("CORPORATE_SHUTTLE_ROSTER_OPTION_1","CORPORATE_SHUTTLE_ROSTER_OPTION_1");
define ("CORPORATE_SHUTTLE_ADHOC","CORPORATE_SHUTTLE_ADHOC");
define ("CANCEL_STATUS","CANCEL_STATUS");
define ("BOOKIING_OTP_VALID","BOOKIING_OTP_VALID");

//ELASTIC

define('ES_INDEX','b2bshuttle');

define('ES_TYPE_PATH','shuttle_route_path');
define('ES_TYPE_GPS_LOGGING','driver_gps_logging');
define('ES_TYPE_ROUTE_PATH','shuttle_route_path');
define('ES_TYPE_SHUTTLE_UNAVAILABLE','shuttle_unavailable');
define('ES_TYPE_SHUTTLE_BOOKING','booking_log');
define('ES_TYPE_SHUTTLE_API_REQUEST','shuttle_api_logs');
define('ES_TYPE_SHUTTLE_STATUS_LOG','customer_satus_logs');

define('ES_FIELD_ROUTE_ORDER','ROUTE_DATE');
define('ES_FIELD_ROUTE_DATE','ROUTE_DATE');
define('ES_FIELD_BRANCH_ID','BRANCH_ID');

//TimeZone
define ('ES_TIME_ZONE','Asia/Kolkata');
define ('ES_CURRENT_DATE_TIME','Y-m-d H:i:s');
define ('ES_CURRENT_DATE','Y-m-d');
define ('ES_CURRENT_TIME','H:i:s');

define('ES_SEARCH_KM','3km');
define('ES_FIX_SEARCH_KM','0.4km');
define('ES_SEARCH_UNIT','km');
define('ES_SEARCH_TYPE','plane');
define('ES_SEARCH_FIXED','Fixed');
define('ES_DEFAULT_DATE','1900-01-01 00:00:00');
define('ES_DEFAULT_DATET','1900-01-01T00:00:00');

define('ES_TIME_SCROLL','30s');
define('ES_SIZE_ZERO','0');
define('ES_SIZE_SINGLE','1');
define('ES_SIZE_THIRTY','30');
define('ES_SIZE_MAX','10000');
define('ES_FIELD_POSITION','POSITION');
define('ES_FIELD_GPS_DATE','GPS_DATE');
define('ES_FIELD_ROUTE_ID','ROUTE_ID');
define('ES_FIELD_ROUTE_TYPE','ROUTE_TYPE');
define('ES_FIELD_ROUTE_STATUS','ROUTE_STATUS');
define('ES_FIELD_CAB_ID','CAB_ID');
define('ES_FIELD_START_POSITION','START_POSITION');

define('ES_DEFAULT','0');



defined('BATCHLIMIT') OR define('BATCHLIMIT',1000);
defined('FILE_READ_MODE')  OR define('FILE_READ_MODE', 0644);
defined('FILE_WRITE_MODE') OR define('FILE_WRITE_MODE', 0666);
defined('DIR_READ_MODE')   OR define('DIR_READ_MODE', 0755);
defined('DIR_WRITE_MODE')  OR define('DIR_WRITE_MODE', 0755);

/*
|--------------------------------------------------------------------------
| File Stream Modes
|--------------------------------------------------------------------------
|
| These modes are used when working with fopen()/popen()
|
*/
defined('FOPEN_READ')                           OR define('FOPEN_READ', 'rb');
defined('FOPEN_READ_WRITE')                     OR define('FOPEN_READ_WRITE', 'r+b');
defined('FOPEN_WRITE_CREATE_DESTRUCTIVE')       OR define('FOPEN_WRITE_CREATE_DESTRUCTIVE', 'wb'); // truncates existing file data, use with care
defined('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE')  OR define('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE', 'w+b'); // truncates existing file data, use with care
defined('FOPEN_WRITE_CREATE')                   OR define('FOPEN_WRITE_CREATE', 'ab');
defined('FOPEN_READ_WRITE_CREATE')              OR define('FOPEN_READ_WRITE_CREATE', 'a+b');
defined('FOPEN_WRITE_CREATE_STRICT')            OR define('FOPEN_WRITE_CREATE_STRICT', 'xb');
defined('FOPEN_READ_WRITE_CREATE_STRICT')       OR define('FOPEN_READ_WRITE_CREATE_STRICT', 'x+b');

/*
|--------------------------------------------------------------------------
| Exit Status Codes
|--------------------------------------------------------------------------
|
| Used to indicate the conditions under which the script is exit()ing.
| While there is no universal standard for error codes, there are some
| broad conventions.  Three such conventions are mentioned below, for
| those who wish to make use of them.  The CodeIgniter defaults were
| chosen for the least overlap with these conventions, while still
| leaving room for others to be defined in future versions and user
| applications.
|
| The three main conventions used for determining exit status codes
| are as follows:
|
|    Standard C/C++ Library (stdlibc):
|       http://www.gnu.org/software/libc/manual/html_node/Exit-Status.html
|       (This link also contains other GNU-specific conventions)
|    BSD sysexits.h:
|       http://www.gsp.com/cgi-bin/man.cgi?section=3&topic=sysexits
|    Bash scripting:
|       http://tldp.org/LDP/abs/html/exitcodes.html
|
*/
defined('EXIT_SUCCESS')        OR define('EXIT_SUCCESS', 0); // no errors
defined('EXIT_ERROR')          OR define('EXIT_ERROR', 1); // generic error
defined('EXIT_CONFIG')         OR define('EXIT_CONFIG', 3); // configuration error
defined('EXIT_UNKNOWN_FILE')   OR define('EXIT_UNKNOWN_FILE', 4); // file not found
defined('EXIT_UNKNOWN_CLASS')  OR define('EXIT_UNKNOWN_CLASS', 5); // unknown class
defined('EXIT_UNKNOWN_METHOD') OR define('EXIT_UNKNOWN_METHOD', 6); // unknown class member
defined('EXIT_USER_INPUT')     OR define('EXIT_USER_INPUT', 7); // invalid user input
defined('EXIT_DATABASE')       OR define('EXIT_DATABASE', 8); // database error
defined('EXIT__AUTO_MIN')      OR define('EXIT__AUTO_MIN', 9); // lowest automatically-assigned error code
defined('EXIT__AUTO_MAX')      OR define('EXIT__AUTO_MAX', 125); // highest automatically-assigned error code
