<?php

set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
//include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
//include(APPPATH . 'libraries/Shuttledao.php');
//include(APPPATH . 'libraries/EncdecPaytm.php');
include(APPPATH . 'libraries/ShuttleReferraldao.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class ShuttleRefferalmdl extends CI_Model {

    //put your code here
    function __construct() {
        parent::__construct();
        $this->load->model('ShuttleElasticmdl', 'semodel');
        $this->load->model('ShuttleCommonmdl', 'cmodel');
        $this->load->model('Shuttlemdl', 'smodel');
    }

    public function shuttle_registration_withreferral($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct, $branch_id, $comp_id, $comp_name, $referral_code) {
       // $dao = new Shuttledao();
        $refdao = new ShuttleReferraldao();
        $getdate = $this->cmodel->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        $ref=false;$ref_empid="";$ref_branch=0;
        try {
            $ENCRYP_PWD = $this->cmodel->AES_ENCRYPT($password, AES_ENCRYPT_KEY);            
            $element_array = array();
            $ENC_EMAIL = $this->cmodel->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $this->cmodel->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);            
            if($referral_code!=0)
            {
                $ENC_REF_MOBILE = $this->cmodel->AES_ENCRYPT($referral_code, AES_ENCRYPT_KEY);
                $ref_sts=$refdao->check_refferalcode($ENC_REF_MOBILE);
                if($ref_sts)
                {
                    $ref_empid=$ref_sts['EMPLOYEES_ID'];
                    $ref_branch=$ref_sts['BRANCH_ID'];
                    $ref=true;
                }
            }            
            $where = "(EMAIL = '" . $ENC_EMAIL . "' || MOBILE = '" . $ENC_MOBILE . "') AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row = $this->cmodel->c_selectrow('employees', $where);            
            if ($row) {
                $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
            } else if ($ref===false) {
                $element_array = array('status' => 0, 'Message' => 'Invalid Referral Code');
            } else {
                $eid = mt_rand(100000, 999999);
               if ($comp_name != "0" && $branch_id == 1) {
                    $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdatetime);
                    $this->cmodel->c_insert('shuttle_company_master', $data1);
                    $comp_id = $this->db->insert_id();
                } else if ($comp_name != "0" && $branch_id != 1) {
                    $element_array = array('status' => 0, 'Message' => 'Other Company Registration Not Allowed');
                    return $element_array;
                    exit;
                }             
                $ENC_NAME = $this->cmodel->AES_ENCRYPT(str_replace('+', ' ', $name), AES_ENCRYPT_KEY);
                $data = array('BRANCH_ID' => $branch_id, 'SHUTTLE_COMPANY_ID' => $comp_id, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                    'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
                $cnt = $this->cmodel->c_insert('employees', $data);
                if ($cnt > 0) {                    
                    if($ref===true)
                    {
                        $data2=array('Reg_Employee_Id'=>$eid,'Reg_Branch_Id'=>$branch_id,'Ref_Employee_Id'=>$ref_empid,
                            'Ref_Branch_Id'=>$ref_branch,'Created_at'=>$curdatetime);
                        $this->cmodel->c_insert('employee_referral', $data2);
                    }                    
                    $message = "Thank you for registering with " . SMS_TITLE . ".enjoy the ride!";
                    $this->cmodel->insert_sms($branch_id, 'TOPSCS', $mobile, $message);
                    $data1 = array('branch_id' => $branch_id, 'shuttle_company_id' => $comp_id, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                    $this->cmodel->c_insert('shuttle_notification', $data1);                    
                    $element_array = array('status' => 1, 'Message' => 'Successfully registered');
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Registration failed');
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

}
