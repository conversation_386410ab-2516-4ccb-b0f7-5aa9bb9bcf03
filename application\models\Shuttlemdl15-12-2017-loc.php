<?php

set_include_path(get_include_path() . PATH_SEPARATOR . APPPATH . 'third_party/phpseclib');
include(APPPATH . 'third_party/phpseclib/Crypt/RSA.php');
include(APPPATH . 'libraries/Shuttledao.php');
include(APPPATH . 'libraries/EncdecPaytm.php');
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Shuttlemdl
 *
 * <AUTHOR>
 */
class Shuttlemdl extends CI_Model {

    public function ssvaluecheck($mobileno, $ssid, $ct) {
        $name = "";
        $ssval = "";
        $ret = "";
        $dao = new Shuttledao();
        log_message('custom', "\n------------ My var value is -----------\n" . $mobileno);
        try {
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
            $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $dao->c_selectrow('employees', $where);
            if ($row) {
                $name = $row['NAME'];
                $ssval = $row['SESSION_ID'];
                $privatekey = $row['PRIVATE_KEY'];
            }
            if ($ct == "ip" && $ssid == $ssval) {
                $ret = 'true,';
            } else {
                $rsa = new Crypt_RSA();
                $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
                $decoded = base64_decode($ssid);
                $rsa->loadKey($privatekey);
                $decrypted = $rsa->decrypt($decoded);
                $ret = $decrypted == $ssval ? 'true' : 'false';
            }
            return $ret;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function rsaencrypt($mobileno) {
        $rsa = new Crypt_RSA();
        try {
            extract($rsa->createKey());
            $rsa->setEncryptionMode(CRYPT_RSA_ENCRYPTION_PKCS1);
            $element_array = str_ireplace('-----BEGIN PUBLIC KEY-----', '', $publickey);
            $elementrsaarray = trim(str_ireplace('-----END PUBLIC KEY-----', '', $element_array));

            $element_array1 = str_ireplace('-----BEGIN RSA PRIVATE KEY-----', '', $privatekey);
            $elementrsaarray1 = trim(str_ireplace('-----END RSA PRIVATE KEY-----', '', $element_array1));
            $data = array(
                'PUBLIC_KEY' => $elementrsaarray,
                'PRIVATE_KEY' => $elementrsaarray1
            );

            $dao = new Shuttledao();
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
            $where1 = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
            $cnt = $dao->c_update('employees', $data, $where1);
            if ($cnt > 0) {
                return $elementrsaarray;
            } else {
                return $element_array = 'False';
            }
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_registraton($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        try {
            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $element_array = array();
            $ENC_EMAIL = $dao->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "(EMAIL = '" . $ENC_EMAIL . "' || MOBILE = '" . $ENC_MOBILE . "') AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row = $this->c_selectrow('employees', $where);
            if ($row) {
                $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
            } else {
                //$eid = rand(10000, 100000);
                $eid=mt_rand(100000,999999);
                $ENC_NAME = $dao->AES_ENCRYPT($name, AES_ENCRYPT_KEY);
                $data = array('BRANCH_ID' => 1, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                    'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
                $cnt = $this->c_insert('employees', $data);
                if ($cnt > 0) {
                    $message = "Thank you for registering with " . SMS_TITLE . ".enjoy the ride!";
                    $dao->insert_sms(1, 'TOPSCS', $mobile, $message);
                    $data1 = array('branch_id' => 1, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                    $this->c_insert('shuttle_notification', $data1);
                    $element_array = array('status' => 1, 'Message' => 'Successfully registered');
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Registration failed');
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_otpcreation($mobile, $password, $ct) {
        $smssts = "";
        $smsmsg = "";
        $element_array = array();
        $dao = new Shuttledao();
        try {
            $getdate = $dao->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $dao->c_selectrow('employees', $where);
            if ($row) {
                $ename = $dao->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                $eid = $row['EMPLOYEES_ID'];
                //Thank you for registering with {V}. enjoy the ride!
                $otp = $mobile == "9176797925" && $ct == "ip" ? '1111' : $otp;

                $smsmsg = "Dear $ename, Please use $otp as login OTP in " . SMS_TITLE;//TOPSA Corporate Share Ride
                $smssts = $this->sendsms($mobile, $smsmsg);
                $data = array('MOBILE_NO' => $mobile, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0, 'SMS_RESPONSE' => $smssts,
                    'OTP_CATEGORY' => 'ShuttleLogin', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime);
                $cnt = $dao->c_insert('otp_verification', $data);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_otpresend($mobile, $ct) {
        $smssts = "";
        $smsmsg = "";
        $empid = "";
        $branch_id = 0;
        $element_array = array();
        $dao = new Shuttledao();
        try {
            $getdate = $dao->get_datetime();
            $curdatetime = $getdate['cur_date_time'];

            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            // $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
            $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $dao->c_selectrow('employees', $where);
            if ($row) {
                $ename = $dao->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                $empid = $row['EMPLOYEES_ID'];
                $branch_id = $row['BRANCH_ID'];
                $where1 = " VERIFIED_STATUS='0' and MOBILE_NO='$mobile' and OTP_CATEGORY='ShuttleLogin' and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
                $row1 = $dao->c_selectrow('otp_verification', $where1);
                if ($row1) {
                    $otp = $row1['OTP'];
                }
                //TOPSCS
                $otp = $mobile == "9176797925" && $ct == "ip" ? '1111' : $otp;
                $smsmsg = "Dear $ename, Please use $otp as login OTP in " . SMS_TITLE;

                $data2 = array('BRANCH_ID' => $branch_id, "ORIGINATOR" => 'TOPSCS', 'RECIPIENT' => $mobile, 'MESSAGE' => $smsmsg, 'STATUS' => 'U', 'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_BY' => $empid, 'CREATED_DATE' => $curdatetime);
                $dao->c_insert('sms', $data2);
                // $smssts = $this->sendsms($mobile, $smsmsg);
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid Login Credentials");
            }
            return $element_array;
        } catch (Exception $e) {
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_otpverified($mobile, $password, $otp, $deviceinfo, $deviceid, $fcmid, $appversion, $ct) {
        $sessionEncrypt = "";
        $encrypted = "";
        $officeaddr = "--";
        $officelat = 0;
        $officelong = 0;
        $element_array = array();
        $comp_id = 0;
        $auto_id = 0;
        try {
            $dao = new Shuttledao();
            $getdate = $dao->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            //$ENCRYP_PWD = $this->Encrypt_Script($password, SECERT_KEY_NTL);
            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where1 = "OTP='$otp' and VERIFIED_STATUS='0' and MOBILE_NO='$mobile' and OTP_CATEGORY='ShuttleLogin' and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
            $value = array('VERIFIED_STATUS' => 1);
            $cnt = $dao->c_update('otp_verification', $value, $where1);
            if ($cnt > 0) {
                $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
                $row = $dao->c_selectrow('employees', $where);
                //echo $this->db->last_query();
                if ($row) {
                    $name = $dao->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                    $gender = $row['GENDER'];
                    $email = $dao->AES_DECRYPT($row['EMAIL'], AES_ENCRYPT_KEY);
                    $address = $row['ADDRESS'];
                    $lat = $row['LATITUDE'];
                    $long = $row['LONGITUDE'];
                    $category = $row['CATEGORY'];
                    $bid = $row['BRANCH_ID'];
                    $eid = $row['EMPLOYEES_ID'];
                    $emergency_no = $row['EMERGENCY_CONTACT_NO'];
                    $comp_id = $row['SHUTTLE_COMPANY_ID'];
                    $auto_id = $row['id'];
                    if (!(is_null($emergency_no))) {
                        $emergency_no = $dao->AES_DECRYPT($emergency_no, AES_ENCRYPT_KEY);
                    } else {
                        $emergency_no = 0;
                    }
                    $mcat = $ct == 'ip' ? 'iOS' : 'ANDROID';
                    //if ($category != "Shuttle") {
                    $where1 = "BRANCH_ID = '$bid ' AND ACTIVE = '1'";
                    $row1 = $dao->c_selectrow('branch', $where1);
                    if ($row1) {
                        $officeaddr = $row1['LOCATION'];
                        $officelat = $row1['LAT'];
                        $officelong = $row1['LONG'];
                    }
                    //}

                    $sessionid = rand(10000, 100000);
                    $sql_encryt = $dao->AES_ENCRYPT($sessionid, AES_ENCRYPT_KEY);
                    $data1 = array(
                        'SESSION_ID' => base64_encode($sql_encryt),
                        'MOBILE_GCM' => $fcmid,
                        'DEVICE_INFO' => $deviceinfo,
                        'DEVICE_ID' => $deviceid,
                        'APP_VERSION' => $appversion,
                        'MOBILE_CATEGORY' => $mcat
                    );
                    $this->c_update("employees", $data1, $where);
                    $sessionEncrypt = base64_encode($sql_encryt);
                    $encrypted = $this->rsaencrypt($mobile);
                    if ($encrypted != 'False') {
                        $element_array = array('status' => 1, 'Message' => 'success', 'branch_id' => $bid, 'employee_id' => $eid, 'mobile' => $mobile, 'emergency_contact_no' => $emergency_no, 'name' => $name, 'gender' => $gender, 'email' => $email,
                            'homeaddress' => $address, 'homelat' => $lat, 'homelong' => $long, 'officeaddress' => $officeaddr, 'officelat' => $officelat, 'officelong' => $officelong,
                            'category' => $category, 'sessionid' => $sessionEncrypt, 'publickey' => $encrypted, 'company_id' => $comp_id, 'auto_id' => $auto_id, 'PAYTM_MID' => PAYTM_MID, 'PAYTM_WEBSITE' => PAYTM_WEBSITE_APP,
                            'PAYTM_INDUSTRY_TYPE_ID' => PAYTM_INDUSTRY_TYPE_ID, 'PAYTM_CHANNEL_ID_APP' => PAYTM_CHANNEL_ID_APP, 'PAYTM_CLIENT_ID' => PAYTM_CLIENT_ID, 'PAYTM_CLIENT_SECRET' => PAYTM_CLIENT_SECRET, 'GOOGLE_MAP_API_KEY' => GOOGLE_MAP_API_KEY);
                    } else {
                        $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                    }
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
                }
            } else {
                $element_array = array('status' => 0, 'Message' => 'Invalid OTP');
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function appversion($eid, $bid, $appversion, $ct) {
        $dao = new Shuttledao();
        $element_array = array();
        $property_array = array();
        $roster_id = 0;
        $roster_passenger_id = 0;
        $cab_id = 0;
        $feedback_roster_id = 0;
        $sloc = "";
        $eloc = "";
        $emploc = "";
        $trip_type = "";
        $tsloc = "";
        $teloc = "";
        $ttrip_type = "";
        $temploc = "";
        $cabno = "";
        $stime = "";
        $feedbackstime = "";
        $place_picker_sts = 0;
        $rating_value="";
        try {
            if ($appversion != 0) {
                $where1 = "EMPLOYEES_ID = '" . $eid . "' AND BRANCH_ID = '$bid' and ACTIVE=1 and CATEGORY='Shuttle'";
                $data1 = array(
                    'APP_VERSION' => $appversion
                );
                $this->c_update("employees", $data1, $where1);
            }
            $where1 = "EMPLOYEES_ID = '" . $eid . "' AND BRANCH_ID = '$bid' and ACTIVE=1 and CATEGORY='Shuttle'";
            $emp = $dao->c_selectrow('employees', $where1); // //check employee PLACE_PICKER_STATUS
            $place_picker_sts = $emp['PLACE_PICKER_STATUS'];
           // $rating=$emp['RATING'];
            $rate_sts=$emp['RATE_STATUS'];
            $rate_date=$emp['RATE_DATE'];
            $auto_id=$emp['id'];
            if(is_null($rate_sts) && is_null($rate_date))
            {
                $rating_value=0;
            }
            else
            {
               $rating_value=$dao->getrating_sts($eid);
            }

            $where = "ACTIVE=1 and PROPERTIE_CATEGORY='ShuttleApp' and BRANCH_ID='$bid'";
            $row = $dao->c_selectarray('properties', $where); // //check employee app property  
            foreach ($row as $val) {
                $property_array[] = array('property_name' => $val['PROPERTIE_NAME'], 'property_value' => $val['PROPERTIE_VALUE']);
            }
            $track = $dao->get_current_tracking_details($eid); //get traking details
            if ($track) {
                $roster_id = $track['ROSTER_ID'];
                $cab_id = $track['CAB_ID'];
                $tsloc = $track['START_LOCATION'];
                $teloc = $track['END_LOCATION'];
                $ttrip_type = $track['TRIP_TYPE'];
                $temploc = $track['LOCATION_NAME'];
                $cabno = $track['VEHICLE_REG_NO'];
                $stime = $track['ESTIMATE_START_TIME'];
                $roster_passenger_id = $track['ROSTER_PASSENGER_ID'];
                if ($ttrip_type == "P") {
                    $tsloc = $temploc;
                    $teloc = $teloc;
                } else {
                    $tsloc = $tsloc;
                    $teloc = $temploc;
                }
            }
            if ($ct != 'ip') {
                $feedback = $dao->get_current_feedback_details($eid,$ct); //get feedback deatils
                if ($feedback) {
                    $sloc = $feedback['START_LOCATION'];
                    $eloc = $feedback['END_LOCATION'];
                    $trip_type = $feedback['TRIP_TYPE'];
                    $emploc = $feedback['LOCATION_NAME'];
                    $feedback_roster_id = $feedback['ROSTER_ID'];
                    $feedbackstime = $feedback['ESTIMATE_START_TIME'];
                }
            }
            $element_array = array('status' => 1, 'GOOGLE_MAP_API_KEY' => GOOGLE_MAP_API_KEY, 'property_details' => $property_array, 'track_roster_id' => $roster_id, 'track_passenger_id' => $roster_passenger_id, 'track_cab_id' => $cab_id, 'track_sloc' => $tsloc, 'track_eloc' => $teloc, 'track_trip_type' => $ttrip_type, 'track_emp_loc' => $temploc, 'track_cab_no' => $cabno, 'track_start_time' => $stime, 'feedback_roster_id' => $feedback_roster_id, 'sloc' => $sloc, 'eloc' => $eloc, 'trip_type' => $trip_type, 'emp_loc' => $emploc, 'trip_time' => $feedbackstime, 'place_picker_status' => $place_picker_sts,'rating_value'=>$rating_value,'auto_id'=>$auto_id);
//            print_r($element_array);
//          EXIT;
            return $element_array; //return response to controller    
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_search($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";

        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->getSearchGroup($startlat, $startlong, $branchid, 'D', $route_type);
            $droparray1 = $this->ShuttleElasticmdl->getSearchGroup($endlat, $endlong, $branchid, 'P', $route_type);
        }
        $pickarray = $this->ShuttleElasticmdl->getSearchGroup($startlat, $startlong, $branchid, 'P', $route_type);
        $droparray = $this->ShuttleElasticmdl->getSearchGroup($endlat, $endlong, $branchid, 'D', $route_type);
//        print_r($pickarray);
//        print_r($droparray);
//        print_r($pickarray1);
//        print_r($droparray1);
//        exit;
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();

        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->check_seat_availability($route_id, 0);
                    foreach ($seatavial as $row) {
                        $rosterid = $row['ROSTER_ID'];
                        $routedate = $row['ESTIMATE_START_TIME'];
                        $category = $row['category'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['stime'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);


                        $dist = $this->calkm($startlat . ',' . $startlong, $plat . ',' . $plong, $startlat . ',' . $startlong, 'km', 'walk');
                        $dist1 = $this->calkm($endlat . ',' . $endlong, $dlat . ',' . $dlong, $endlat . ',' . $endlong, 'km', 'walk');
                        $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));

                        $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, 0);
                        $traveltime = $traveldist[1];

                        if ($resheduledate == '0000-00-00') {
                            if (strtotime($resheduledate) == strtotime($curdate)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);
                            } else if (strtotime($resheduledate) > strtotime($curdate)) {
                                $tomorrow = date('Y-m-d', strtotime('-1 day', strtotime($curdate)));
                                $comparetime = $tomorrow . " " . $stime;
                                $shuttletime = $this->shuttle_timings($comparetime, 'Regular', $a_p_picktime);
                            } else {
                                $shuttletime = $this->shuttle_timings($comparetime, $category, $a_p_picktime);
                            }
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                        } else {
                            if (strtotime($stime) >= strtotime($curtime)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);

                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial1 = $dao->check_seat_availability($route_id, 0);
                    foreach ($seatavial1 as $row) {
                        $rosterid = $row['ROSTER_ID'];
                        $routedate = $row['ESTIMATE_START_TIME'];
                        $category = $row['category'];
                        $a_pick = $val['APROX_PICK'];
                        $destination = $val['DROP_POSITION'];
                        $origin = $val['PICK_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['stime'];
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $comparetime = $curdate . " " . $stime;

                        $dist1 = $this->calkm($startlat . ',' . $startlong, $dlat . ',' . $dlong, $startlat . ',' . $startlong, 'km', 'walk');
                        $dist = $this->calkm($endlat . ',' . $endlong, $plat . ',' . $plong, $endlat . ',' . $endlong, 'km', 'walk');
                        $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                        $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, 0);

                        $traveltime = $traveldist[1];
                        if ($resheduledate == '0000-00-00') {
                            if (strtotime($resheduledate) == strtotime($curdate)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);
                            } else if (strtotime($resheduledate) > strtotime($curdate)) {
                                $tomorrow = date('Y-m-d', strtotime('-1 day', strtotime($curdate)));
                                $comparetime = $tomorrow . " " . $stime;
                                $shuttletime = $this->shuttle_timings($comparetime, 'Regular', $a_p_picktime);
                            } else {
                                $shuttletime = $this->shuttle_timings($comparetime, $category, $a_p_picktime);
                            }
                            $element_arr1[] = array('routeno' => $val['ROUTE_ID'], 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                        } else {
                            if (strtotime($stime) >= strtotime($curtime)) {
                                $shuttletime = $this->shuttle_timings($comparetime, 'Oneday', $a_p_picktime);

                                $element_arr1[] = array('routeno' => $val['ROUTE_ID'], 'roster_id' => $rosterid, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $routedate, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'category' => $category, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }

        $p_array = $this->msort($element_arr, array('shuttle_time', 'route_date'));
        $d_array = $this->msort($element_arr1, array('shuttle_time', 'route_date'));
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }

    public function shuttle_timings($comparetime, $category, $a_p_picktime) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttlestr = "";
        if ($category == 'Regular') {
            if (strtotime($comparetime) > strtotime($curdatetime)) {
                $shuttlestr = (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) ? 'CMonday' : 'ANext';
            } else {
                $shuttlestr = (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) ? 'CMonday' : 'BTomorrow';
            }
        } else if ($category == 'Oneday') {
            if (strtotime($comparetime) > strtotime($curdatetime)) {
                $shuttlestr = "ANext";
            }
        } else {
            if (strtotime($comparetime) > strtotime($curdatetime)) {
                $shuttlestr = (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) ? 'CMonday' : 'ANext';
            } else {
                $shuttlestr = (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) ? 'CMonday' : 'BTomorrow';
            }
        }
        return $shuttlestr;
    }

    public function tariff_cal($dist, $ttype, $tariff_type, $vehicle_id, $noofdays,$branch_id) {
        if ($dist == 0) {
            $dist = 1;
        }
        $condition = "";
        if ($noofdays > 0) {
            $condition = "and NumberofDays='$noofdays'";
        }
        $oneday_fixed_price = 0;
        // echo $dist, $ttype, $tariff_type,$noofdays;
        try {
            $element_array = array();
            if (strtolower($tariff_type) == strtolower("Km")) {

                $where = "BranchId='$branch_id' and Active ='1' and  '$dist'  BETWEEN Tariff_St_Km AND Tariff_Cl_Km and Tariff_Type='km' and VehicleId='$vehicle_id' $condition order by NumberofDays";
                $row = $this->c_selectarray('shuttle_tariff', $where);
                // echo $this->db->last_query();
                foreach ($row as $val) {
                    $tariffId = $val['TariffId'];
                    $validDays = $val['NumberofDays'];
                    $validTrip = $val['NumberofTrips'];
                    $baseKm = $val['Tariff_Base_Km'];
                    $basCost = $val['Tariff_Base_Cost'];
                    $extKmCost = $val['Tariff_Ext_Km_Cost'];
                    $flatCost = $val['Tariff_Flat_Cost'];
                    $percentage = $val['Percentage'];
                    $oneday_fixed_price = $val['Oneday_Fixed_Cost'];
                    $tariff_name = $val['Tariff_Name'];

                    if ($flatCost == 0) {
                        $extKmAmt = 0;
                        $exkm = 0;
                        $fare = 0;
                        $percentAmt = 0;
                        $exkm = $dist - $baseKm;
                        if ($exkm > 0) {
                            $extKmAmt = $exkm * $extKmCost;
                        }
                        if ($percentage == 0) {
                            $fare = ($basCost + $extKmAmt) * $validTrip;
                        } else {
                            $percentAmt = ($basCost + $extKmAmt) * $percentage / 100;
                            $fare = ($basCost + $extKmAmt + $percentAmt) * $validTrip;
                        }
                    } else {
                        if ($percentage == 0) {
                            $fare = $flatCost * $validTrip;
                        } else {
                            $percentAmt = ($flatCost) * $percentage / 100;
                            $fare = ($flatCost + $percentAmt) * $validTrip;
                        }
                    }
                    $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'fare' => round($fare), 'validDays' => $validDays, 'validTrip' => $validTrip, 'onedayFixedprice' => $oneday_fixed_price);
                }
            } else {
                //and  '$dist'BETWEEN Tariff_St_Km AND Tariff_Cl_Km
                $where = "BranchId='$branch_id' and Active ='1' and Tariff_Type='Fixed' and VehicleId='$vehicle_id' $condition order by NumberofDays";
                $row = $this->c_selectarray('shuttle_tariff', $where);
                foreach ($row as $val) {
                    $tariffId = $val['TariffId'];
                    $validDays = $val['NumberofDays'];
                    $validTrip = $val['NumberofTrips'];
                    $flatCost = $val['Tariff_Flat_Cost'];
                    $tariff_name = $val['Tariff_Name'];
                    $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'fare' => ($flatCost * $validTrip), 'validDays' => $validDays, 'validTrip' => $validTrip, 'onedayFixedprice' => $oneday_fixed_price);
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function shuttle_routepath($routeid, $approxptime, $approxdtime, $ct) {
        $element_array = array();
        //$where = "RouteId='$routeid' and Duration between '$approxptime' and '$approxdtime' order by Sno";
        $where = "RouteId='$routeid' order by Sno";
        $row = $this->c_selectarray('shuttle_route_path', $where);
        if (count($row) > 0) {
            foreach ($row as $val) {
                $element_array[] = array('plat' => $val['Latitude'], 'plong' => $val['Longitude']);
            }
        }
        $element_arr = array('routepath' => $element_array);
        echo $this->output($element_arr, $ct);
    }

    public function shuttle_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $fare, $dist, $rosterid_p, $rosterid_d, $noofdays, $noofrides, $ptag, $dtag, $route_type, $subs_confirm_sts, $routeid_p, $routeid_d, $ssotoken, $cust_id, $mobileno) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
        $avail_p = false;
        $avail_d = false;

        if ($noofdays > 1) {
            $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            $enddate = date('Y-m-d', strtotime("+$noofdays day", strtotime($shuttledate)));
            $where = "EmployeeId='$eid' and '$shuttledate' between StartDate and EndDate and PaymentStatus='S'";
            $row = $dao->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {
            if ($ptag == "Next") {
                if ($ttype == "B" && $dtag == "Tomorrow") {
                    $shuttledate = $curdate;
                    $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                } else {
                    $shuttledate = $curdate;
                    $enddate = $curdate;
                }
            } else if ($ptag == "Tomorrow") {
                $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            } else {
                $shuttledate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
            }
            $subscribtionsts = 0;
        }
        if ($routeid_p != 0) {
            $row1 = $dao->check_seat_availability($routeid_p, $rosterid_p);
            if ($row1) {
                $avail_p = true;
            }
        } else {
            $row1 = $dao->check_seat_availability($routeid_d, $rosterid_d);
            if ($row1) {
                $avail_d = true;
            }
        }

        if ($avail_p == true || $avail_d == true) {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {

                $origin = $plat . "," . $plong;
                $destination = $dlat . "," . $dlong;
                $tdist = $this->calkm($origin, $destination, $origin, 'km', 'driving');
                $tariff = $this->tariff_cal($tdist, $ttype, $route_type, $noofdays);
                $totdays = 0;
                $tfare = 0;
                $validtrip = 0;
                if (count($tariff) > 0 && $noofdays > 0) {
                    if ($ttype == "B") {
                        $totdays = $noofdays;
                        $tfare = $tariff[0]['fare'] * 2;
                        $validtrip = $tariff[0]['validTrip'] * 2;
                        $totdist = $tdist * 2;
                    } else {
                        $totdays = $noofdays;
                        $tfare = $tariff[0]['fare'];
                        $validtrip = $tariff[0]['validTrip'];
                        $totdist = $tdist;
                    }
                }
                $data = array('EmployeeId' => $eid, 'PickupRosterId' => $rosterid_p, 'DropRosterId' => $rosterid_d, 'PickupPoint' => $ppoint, 'DropPoint' => $dpoint, 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $tfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $shuttledate, 'EndDate' => $enddate, 'NoofDays' => $totdays, 'NoofRides' => $validtrip, 'TotalPaidAmt' => $tfare, 'RouteType' => $route_type);
                $cnt = $dao->c_insert('shuttle_booking', $data);
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $dao->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {
                        $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $tfare);

                        // $element_array = array('status' => 1, 'message' => 'success', 'fare' => $row['TotalPaidAmt'], 'paymentid' => $payment_id, 'paymentno' => $paymentno, 'sdate' => $row['StartDate'], 'edate' => $row['EndDate'], 'noofdays' => $row['NoofDays'], 'noofrides' => $row['NoofRides']);
                        $element_array = array('status' => 1, 'message' => $response);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'subscribtion already avilable for this date');
            }
        } else {
            $element_array = array('status' => 3, 'message' => 'No seats available');
        }
        //echo json_encode($element_array);
        return $element_array;
    }

    public function paytmwalletmoney_withdraw($order_id, $ssotoken, $cust_id, $mobile_no, $fare, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        $encdec = new EncdecPaytm();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_time = $getdate['cur_date_time']; //have to check $ssotoken is null or not,$cust_id is null or not
        $checkSum = "";
        $paramList = array();
        $paramList["MID"] = PAYTM_MID;
        $paramList["OrderId"] = $order_id;
        $paramList["IndustryType"] = PAYTM_INDUSTRY_TYPE_ID;
        $paramList["Channel"] = PAYTM_CHANNEL_ID_WEB;
        $paramList["TxnAmount"] = $fare;
        $paramList["AuthMode"] = "USRPWD";
        $paramList["ReqType"] = "WITHDRAW";
        $paramList["SSOToken"] = $ssotoken;
        $paramList["AppIP"] = $_SERVER["REMOTE_ADDR"];
        $paramList["Currency"] = "INR";
        $paramList["DeviceId"] = $mobile_no;
        $paramList["PaymentMode"] = "PPI";
        $paramList["CustId"] = $cust_id;
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CheckSum"] = $checkSum;
        $data = array('OrderId' => $order_id, 'CustId' => $cust_id, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => PAYTM_CHANNEL_ID_WEB, 'TxnAmount' => $fare, 'RequestType' => 'WITHDRAW', 'SsoToken' => $ssotoken,
            'CheckSumHash' => $checkSum, 'Currency' => 'INR', 'DeviceId' => $mobile_no, 'PaytmCustomerId' => $cust_id, 'AppIp' => $_SERVER["REMOTE_ADDR"], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
        $cnt = $dao->c_insert('shuttle_payment_request', $data);
        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_SERVER_MONEY_WITHDRAW, $paramList);
            if (isset($resparr['ResponseCode']) && isset($resparr['ResponseMessage']) && isset($resparr['Status'])) {
                $txnid = $resparr['TxnId'];
                $orderid = $resparr['OrderId'];
                $txnamt = $resparr['TxnAmount'];
                $respdata = array('OrderId' => $orderid, 'TxnId' => $txnid, 'TxnAmount' => $resparr['TxnAmount'], 'BankTxnId' => $resparr['BankTxnId'],
                    'ResponseCode' => $resparr['ResponseCode'], 'ResponseMessage' => $resparr['ResponseMessage'], 'Status' => $resparr['Status'], 'PaymentMode' => $resparr['PaymentMode'],
                    'BankName' => $resparr['BankName'], 'CheckSumHash' => $resparr['CheckSum'], 'PaytmCustomerId' => $resparr['CustId'], 'Mbid' => $resparr['MBID'], 'CreatedBy' => $mobile_no, 'CreatedTime' => $cur_time);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
                $stsarr = array();
                $stsarr['MID'] = PAYTM_MID;
                $stsarr['ORDERID'] = $orderid;
                //$respconfirm = $encdec->callAPI(PAYTM_TXNSTATUS_URL,$stsarr);                 
                if ($cnt > 0 && $resparr['Status'] == 'TXN_SUCCESS' && $resparr['ResponseMessage'] == 'Txn Successful.' && $resparr['ResponseCode'] == '01') {
                    // if ($cnt > 0 && $respconfirm['STATUS'] == 'TXN_SUCCESS' && $respconfirm['RESPMSG'] == 'Txn Successful.' && $respconfirm['RESPCODE'] == '01') {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'S', 'UpdatedDatetime' => $cur_time);
                    $dao->c_update('shuttle_booking', $data1, $where);
                    $response = $this->paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no, $stopid_p,$stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
                } else {
                    $where = "PaymentNo='$order_id'";
                    $data1 = array('PaymentStatus' => 'F', 'UpdatedDatetime' => $cur_time);
                    $dao->c_update('shuttle_booking', $data1, $where);
                    //$response = "Payment Failed";
                    $response = $this->paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no, $stopid_p,$stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
                }
            } else {
                //echo "else  ";
                $respdata = array('OrderId' => $order_id, 'ResponseMessage' => $resparr['Error'], 'CreatedTime' => $cur_time);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
                $response = "Payment Failed";
                //change
//                 $where = "PaymentNo='$order_id'";
//                    $data1 = array('PaymentStatus' => 'S','UpdatedDatetime'=>$cur_time);
            //        $dao->c_update('shuttle_booking', $data1, $where);
             //   $response = $this->paytmwithdraw_response($order_id, $txnid, $txnamt, $mobile_no,$stopid_p,$stopid_d);
            //
            }
        }
        return $response;
    }

    public function paytmwithdraw_response($ORDERID, $txnid, $txnamt, $mobile_no, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];
        $empid = "";
        $stime = "";
        $etime = "";
        $ppoint = "";
        $dpoint = "";
        $tmode = "";
        $pamt = 0;
        $route_id = 0;
        $duration = 0;
        $ENC_MOBILE = $dao->AES_ENCRYPT($mobile_no, AES_ENCRYPT_KEY);
        $row = $dao->getshuttle_bookingdetails($ENC_MOBILE, $ORDERID);
        if ($row) {
            $stime = $row['StartTime'];
            $etime = $row['EndTime'];
            $ppoint = $row['PickupPoint'];
            $dpoint = $row['DropPoint'];
            $tmode = $row['TravelMode'];
            $pamt = $row['TotalPaidAmt'];
            $dist = $row['PackageKm'];
            $tripid_p = $row['PickupRosterId'];
            $tripid_d = $row['DropRosterId'];
            $empid = $row['EmployeeId'];
            $sdate = $row['StartDate'];
            $enddate = $row['EndDate'];
            $duration = $row['NoofDays'];
            $emp_id = $row['EmployeeId'];
            $noofrides = $row['NoofRides'];
            $ename = $dao->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
            $msg = $duration == 1 ? " 1 day" : $duration . " days " . $noofrides . " rides";
            $smsmessage = "Dear $ename, Thanks for your subscriptions $msg plan.For any queries contact 04440003002, enjoy the ride!";
            $dao->insert_sms(1, 'TOPSCS', $mobile_no, $smsmessage);
            $date_to = $enddate;
            $date_from = strtotime($sdate);
            $date_to = strtotime($enddate);
            $message = $this->seats_confirmed_sts($tmode, $tripid_p, $tripid_d);
          //  $message = $this->stage_seats_confirmed_sts($tmode,$tripid_p,$tripid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d); 
            if ($sdate >= $curdate && $enddate >= $curdate && $message == 'Confirmed') {

                // for ($i = $date_from; $i <= $date_to; $i+=86400) {
                for ($i = 0; $i < $duration; $i++) {
                    $shuttledate = date('Y-m-d', strtotime("+$i day", strtotime($sdate)));
                    if (date("w", strtotime($shuttledate)) == 6 || date("w", strtotime($shuttledate)) == 0) {
                        
                    } else {
                       // echo "else ";
                        if ($tmode == "B") {
                            $trip_fare = $pamt / 2;

                            //$seat_cost = $trip_fare / $noofrides;
                            $seat_cost = $pamt / $noofrides;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id = $row['Vehicle_ID'];
                                $route_name = $row['Route_Name'];
                                // $dao->seatcount_update($route_id, $schedule_time, date("Y-m-d", $i), $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist/2),$vehicle_id,'P',$dpoint,$route_name);
                                $dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'P', $dpoint, $route_name,$p_routeorder_p,$p_routeorder_d);
                            }
                            $row1 = $dao->get_routeid_sheduletime($tripid_d);
                            if ($row1) {
                                $route_id = $row1['Schedule_Route_ID'];
                                $schedule_time = $row1['Schedule_Time'];
                                $vehicle_id = $row1['Vehicle_ID'];
                                $route_name = $row1['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_d, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'D', $dpoint, $route_name,$d_routeorder_p,$d_routeorder_d);
                            }
                        } else {
                           // echo "else";
                            $seat_cost = $pamt / $duration;
                            $row = $dao->get_routeid_sheduletime($tripid_p);
                            if ($row) {
                                $route_id = $row['Schedule_Route_ID'];
                                $schedule_time = $row['Schedule_Time'];
                                $vehicle_id = $row['Vehicle_ID'];
                                $route_name = $row['Route_Name'];
                                $dao->seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, round($dist / 2), $vehicle_id, 'P', $dpoint, $route_name,$p_routeorder_p,$p_routeorder_d);
                               // $dao->stage_seatcount_update($route_id, $schedule_time, $shuttledate, $seat_cost, $stopid_p, $ORDERID, $emp_id, $dist, $vehicle_id, 'P', $dpoint, $route_name,$p_routeorder_p,$p_routeorder_d);
                            }
                        }
                    }
                }
                $response_message = "Payment Success";
            } else {
                $substr = substr($ORDERID, 0, 1);
                if ($substr == 'J') {
                    $txnTimeStamp="";
                    $this->jiomoney_refund($ORDERID, $txnid, $txnamt, $mobile_no, $txnTimeStamp);
                } else {
                   $this->paytmwalletmoney_refund($ORDERID, $txnid, $txnamt, $mobile_no);
                }                
                $response_message = "No seats avilable.Your amount has been refunded";
            }
        }
        return $response_message;
    }

    public function paytmaddmoney_request($orderid, $custid, $channelid, $txnamount, $website, $reqtype, $ssotoken, $mobileno) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $data = array('OrderId' => $orderid, 'CustId' => $custid, 'IndustryType' => PAYTM_INDUSTRY_TYPE_ID, 'ChannelId' => $channelid, 'WebSite' => $website, 'TxnAmount' => $txnamount, 'RequestType' => $reqtype, 'SsoToken' => $ssotoken, 'CreatedBy' => $mobileno, 'CreatedTime' => $curdate_time);
        $dao->c_insert('shuttle_payment_request', $data);
    }

    public function paytmaddmoney_response($ORDERID, $TXNID, $BANKTXNID, $TXNAMOUNT, $GATEWAYNAME, $RESPCODE, $RESPMSG, $BANKNAME, $PAYMENTMODE, $TXNDATE, $STATUS, $checkcumvalid, $mobileno) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $data = array('OrderId' => $ORDERID, 'TxnId' => $TXNID, 'BankTxnId' => $BANKTXNID, 'BankName' => $BANKNAME, 'GateWay' => $GATEWAYNAME, 'TxnAmount' => $TXNAMOUNT, 'TxnType' => $TXNTYPE, 'ResponseCode' => $RESPCODE, 'ResponseMessage' => $RESPMSG,
            'PaymentMode' => $PAYMENTMODE, 'TxnDate' => $TXNDATE, 'Status' => $STATUS, 'RefundAmount' => $REFUNDAMT, 'CreatedBy' => $mobileno, 'CreatedTime' => $curdate_time, 'CheckSumValid' => $checkcumvalid);
        $dao->c_insert('shuttle_payment_response', $data);
    }

    public function paytmwalletmoney_refund($order_id, $txnid, $refundamt, $mobile_no) {
        //{"MID":"NTLIND32103984777778","ORDERID":"NTL37523","REFUNDAMOUNT":"1","TXNID":"********","TXNTYPE":"REFUND","REFID":"36581RFIDNTL","CHECKSUM":"3Mv3pNrMhdXEaUYveoVzwzOiDhQsXxEkkKPZi3tfJRff4FpTPPUiE26W1MNGEC0x+mF+QnNKjM13+eiyc0n/iLHxBscfNxOWzTemHDXHKvM="}
        //{"MID":"NTLIND24898271393596","ORDERID":"************","REFUNDAMOUNT":"1.00","TXNID":**********,"TXNTYPE":"REFUND","REFID":"36648RFIDNTL","CHECKSUM":"y4kepWEt2wfXyyN2VyvuiWuR5cL5IQTR2h6QaEaHiV\/RbVmIZkb\/B2x9A2I82dNcwi1c4rrbG06nmkMaVcQjiA+zGyDIwPHG\/6DgHznF62g="}
   
        $dao = new Shuttledao();
        $encdec = new EncdecPaytm();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];

        $checkSum = "";
        $paramList = array();
        $paramList["MID"] = PAYTM_MID;
        $paramList["ORDERID"] = $order_id;
        $paramList["REFUNDAMOUNT"] = $refundamt;
        $paramList["TXNID"] = $txnid;
        $paramList["TXNTYPE"] = 'REFUND';
        $paramList["REFID"] = rand(10000, 100000) . "RFIDNTL";
        $checkSum = $encdec->getChecksumFromArray($paramList, PAYTM_MERCHANT_KEY);
        $paramList["CHECKSUM"] = $checkSum;
        $data = array('OrderId' => $order_id, 'TxnId' => $txnid, 'RefundAmount' => $refundamt, 'RequestType' => 'REFUND', 'RefId' => $paramList["REFID"],
            'CheckSumHash' => $checkSum, 'CreatedBy' => $mobile_no, 'CreatedTime' => $curdate_time);
         //echo json_encode($paramList);
        $cnt = $dao->c_insert('shuttle_payment_request', $data);

        if ($cnt > 0) {
            $resparr = $encdec->callAPI(PAYTM_REFUND_URL, $paramList);
           //  print_r($resparr);
          //   exit;
            if (isset($resparr['RESPCODE']) && isset($resparr['RESPMSG'])) {
                $respdata = array('OrderId' => $resparr['ORDERID'], 'TxnId' => $resparr['TXNID'], 'TxnAmount' => $resparr['TXNAMOUNT'], 'RefundAmount' => $resparr['REFUNDAMOUNT'],
                    'ResponseCode' => $resparr['RESPCODE'], 'ResponseMessage' => $resparr['RESPMSG'], 'Status' => $resparr['STATUS'], 'RefundId' => $resparr['REFUNDID'],
                    'RefId' => $resparr['REFID'], 'GateWay' => $resparr['GATEWAY'], 'CardIssuer' => $resparr['CARD_ISSUER'], 'CreatedTime' => $curdate_time, 'CreatedBy' => $mobile_no,);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
                // $confirmsts = $encdec->callAPI(PAYTM_REFUND_STS_URL, $paramList);
                $stsarr = array();
                $stsarr['MID'] = PAYTM_MID;
                $stsarr['ORDERID'] = $order_id;
                $stsarr['REFID'] = $paramList["REFID"];
                $respconfirm = $encdec->callAPI(PAYTM_REFUND_STS_URL, $stsarr);
                if ($cnt > 0 && $resparr['RESPCODE'] == '10' && $resparr['RESPMSG'] == 'Refund Successful.') {
                    // if ($cnt > 0 && $respconfirm['RESPCODE'] == '10' && $respconfirm['RESPMSG'] == 'Refund Successful.' && $respconfirm['STATUS '] == 'TXN_SUCCESS') {
                    $where = "PaymentNo='$order_id'";
                    $this->db->set("RefundAmt", "if(RefundAmt is null," . $resparr['REFUNDAMOUNT'] . ",RefundAmt+" . $resparr['REFUNDAMOUNT'] . ")", FALSE);
                    $this->db->set("RefundReferanceId", "if(RefundReferanceId is null,'" . $resparr['REFUNDID'] . "',CONCAT(RefundReferanceId,',','" . $resparr['REFUNDID'] . "'))", FALSE);
                    $this->db->set("Active", "if(NoofRides-NoofCancelRides=1,3,2)", FALSE);
                    $this->db->set("NoofCancelRides", "NoofCancelRides+1", FALSE);
                    $this->db->set("UpdatedDatetime", $curdate_time);
                    $this->db->where($where);
                    $this->db->update('shuttle_booking');
                    //echo $this->db->last_query();
                }
            } else {
                $respdata = array('OrderId' => $order_id, 'ResponseCode' => $resparr['ErrorCode'], 'ResponseMessage' => $resparr['ErrorMsg'], 'CreatedTime' => $curdate_time, 'CreatedBy' => $mobile_no,);
                $cnt = $dao->c_insert('shuttle_payment_response', $respdata);
            }
        }
    }

    public function shuttle_payment($MID, $ORDERID, $TXNID, $BANKTXNID, $TXNAMOUNT, $TXNTYPE, $GATEWAYNAME, $RESPCODE, $RESPMSG, $BANKNAME, $PAYMENTMODE, $REFUNDAMT, $TXNDATE, $STATUS, $payment_id) {
        $response_message = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_time'];
        $curdate_time = $getdate['cur_date_time'];
        $avail_p = false;
        $avail_d = false;

        $where = "ORDERID='$ORDERID' and MID='$MID' and SNO='$payment_id'";
        $data = array('TXNID' => $TXNID, 'BANKTXNID' => $BANKTXNID, 'TXNAMOUNT' => $TXNAMOUNT, 'TXNTYPE' => $TXNTYPE, 'GATEWAYNAME' => $GATEWAYNAME,
            'RESPCODE' => $RESPCODE, 'RESMSG' => $RESPMSG, 'BANKNAME' => $BANKNAME, 'PAYMENTMODE' => $PAYMENTMODE, 'TXNDATE' => $TXNDATE, 'REFUNDAMT' => $REFUNDAMT, 'STATUS' => $STATUS, 'RESPONSED_TIME' => $curdate_time);
        $cnt = $dao->c_update('shuttle_payment_gateway', $data, $where);
        if ($cnt > 0) {
            if ($STATUS == "TXN_SUCCESS") {
                // if ($STATUS == "TXN_SUCCESS" && $TXNID!='' && $BANKTXNID!='' && $GATEWAYNAME!='' && $TXNDATE!='1970-01-01' && $RESPMSG=="Txn Successful." && $RESPCODE=="01") {                

                $empid = "";
                $stime = "";
                $etime = "";
                $ppoint = "";
                $dpoint = "";
                $tmode = "";
                $pamt = 0;
                $route_id = 0;
                $where = "PaymentNo='$ORDERID'";
                $row = $dao->c_selectrow('shuttle_booking', $where);
                if ($row) {
                    $Sno = $row['Sno'];
                    $stime = $row['StartTime'];
                    $etime = $row['EndTime'];
                    $ppoint = $row['PickupPoint'];
                    $dpoint = $row['DropPoint'];
                    $tmode = $row['TravelMode'];
                    $pamt = $row['TotalPaidAmt'];
                    $dist = $row['PackageKm'];
                    $rosterid_p = $row['PickupRosterId'];
                    $rosterid_d = $row['DropRosterId'];
                    $empid = $row['EmployeeId'];
                    $sdate = $row['StartDate'];
                    $enddate = $row['EndDate'];
                    $duration = $row['NoofDays'];

                    $roster_id = $rosterid_p == 0 ? $rosterid_d : $rosterid_p;
                    $row = $dao->check_seat_availability(0, $roster_id);
                    if ($row) {
                        $date_from = $sdate;
                        $date_from = strtotime($date_from);
                        if ($pamt == $TXNAMOUNT) {
                            $where1 = "PaymentNo='$ORDERID'";
                            $data1 = array('PaymentStatus' => 'S'
                            );
                            $ucnt = $dao->c_update('shuttle_booking', $data1, $where1);
                            if ($ucnt > 0) {
                                if ($duration == 1) {
                                    if ($tmode == "P") {
                                        $this->insert_roster_passenger($empid, $rosterid_p, $dist, $sdate . " " . $stime);
                                    } else if ($tmode == "R") {
                                        $this->insert_roster_passenger($empid, $rosterid_d, $dist, $sdate . " " . $stime);
                                    } else {
                                        $this->insert_roster_passenger($empid, $rosterid_p, $dist, $sdate . " " . $stime);
                                        $this->insert_roster_passenger($empid, $rosterid_d, $dist, $enddate . " " . $etime);
                                    }
                                }
                                $date_to = $enddate;
                                $date_to = strtotime($date_to);
                                $inc = 0;
                                if ($sdate >= $curdate && $enddate >= $curdate) {
                                    for ($i = $date_from; $i <= $date_to; $i+=86400) {
                                        if (date("w", strtotime(date("Y-m-d", $i))) == 6 || date("w", strtotime(date("Y-m-d", $i))) == 0) {
                                            
                                        } else {
                                            if ($tmode == "B") {
                                                for ($j = 0; $j < 2; $j++) {
                                                    if ($j == 0) {
                                                        $stime = $row['StartTime'];
                                                        $etime = "00:00:00";
                                                        $roster_id = $rosterid_p;
                                                    }
                                                    if ($j == 1) {
                                                        $stime = $row['EndTime'];
                                                        $etime = "00:00:00";
                                                        $roster_id = $rosterid_d;
                                                    }
                                                    $data = array('BookingMasterId' => $Sno, 'StartTime' => $stime, 'EndTime' => $etime, 'CreatedDatetime' => $cur_time,
                                                        'TravelDate' => date("Y-m-d", $i), 'RosterId' => $roster_id);
                                                    $dao->c_insert('shuttle_booking_trans', $data);
                                                    if ($inc == 0) {
                                                        $row1 = $dao->get_roster_details($roster_id, date("Y-m-d", $i));
                                                        $dao->update_passenger_count($row1['ROSTER_ID'], 'add');
                                                    }
                                                    //update passenger count here
                                                }
                                            } else {
                                                $roster_id = $rosterid_p == 0 ? $rosterid_d : $rosterid_p;
                                                $data = array('BookingMasterId' => $Sno, 'StartTime' => $stime, 'EndTime' => $etime, 'CreatedDatetime' => $cur_time,
                                                    'TravelDate' => date("Y-m-d", $i), 'RosterId' => $roster_id);
                                                $dao->c_insert('shuttle_booking_trans', $data);
                                                //echo $this->db->last_query();
                                                //update passenger count here
                                                if ($inc == 0) {
                                                    $row1 = $dao->get_roster_details($roster_id, date("Y-m-d", $i));
                                                    $dao->update_passenger_count($row1['ROSTER_ID'], 'add');
                                                }
                                            }
                                        }
                                        $inc++;
                                    }
                                }
                            }
                            $response_message = array('status' => 1);
                        } else {
                            $response_message = array('status' => 0);
                        }
                    } else {
                        $response_message = array('status' => 1, 'message' => "payment return");
                    }
                } else {
                    $response_message = array('status' => 0);
                }
            } else {
                $where = "PaymentNo='$ORDERID'";
                $data1 = array('PaymentStatus' => 'F');
                $dao->c_update('shuttle_booking', $data1, $where);
                $response_message = array('status' => 1);
            }
        } else {
            $where = "PaymentNo='$ORDERID'";
            $data1 = array('PaymentStatus' => 'F');
            $dao->c_update('shuttle_booking', $data1, $where);
            $response_message = array('status' => 1);
        }
        return $response_message;
    }

    public function insert_roster_passenger($eid, $rosterid, $dist, $tripdate_time) {
        // $enddate = $sdate;  
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $OTP = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
        $data = array('EMPLOYEE_ID' => $eid, 'ROSTER_ID' => $rosterid, 'ESTIMATE_START_TIME' => $tripdate_time, 'ACTIVE' => 1, 'ROUTE_ORDER' => $dist,
            'CREATED_BY' => $eid, 'CREATED_DATE' => $curdate_time); //have to discuss otp_verification 
        $rpcnt = $dao->c_insert('roster_passengers', $data);
        $rpid = $this->db->insert_id();
        if ($rpcnt > 0) {
            $cnt = $dao->update_passenger_count($rosterid, 'add');
            $row = $dao->get_employee_details($eid);
            $data1 = array("MOBILE_NO" => $row['MOBILE'], 'ROSTER_PASSENGER_ID' => $rpid, 'OTP' => $OTP, 'VERIFIED_STATUS' => 0, 'OTP_CATEGORY' => 'Pickup', 'CREATED_DATE' => $curdate_time);
            $dao->c_insert('otp_verification', $data1);

            $tdate = date('Y-m-d', strtotime($tripdate_time));
            $ttime = date('H:i:s', strtotime($tripdate_time));
            // $message = "Greetings from CTS Transport,Your Pickup on $tdate at $ttime hrs Log in.for any help contact:9176797925";
            $data2 = array('BRANCH_ID' => $row['BRANCH_ID'], "ORIGINATOR" => 'CTSCKC', 'RECIPIENT' => $row['MOBILE'], 'MESSAGE' => $message, 'STATUS' => 'U', 'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdate_time);
            $dao->c_insert('sms', $data2);
        }
    }

    public function profile_update($emergencyno, $homeaddr, $homelat, $homelong, $eid, $officeaddr, $officelat, $officelong, $branch_id) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array = array();
        $data4 = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];

        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' => $dao->AES_ENCRYPT($emergencyno, AES_ENCRYPT_KEY));
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        $data4 = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time);
        $data5 = array_merge($data2, $data3, $data4);
        $where = "EMPLOYEES_ID='$eid' and ACTIVE='1' and CATEGORY='Shuttle' and BRANCH_ID='$branch_id'";
        $cnt = $dao->c_update('employees', $data5, $where);
        //echo $this->db->last_query();
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 1, 'Message' => "Please try again");
        }
        return $element_array;
    }

    public function shuttleimage_upload($img) {
        $UPLOAD_DIR = "../shuttle_profile/";
        $data = base64_decode($img);
        $file = $UPLOAD_DIR . uniqid() . '.png';
        file_put_contents($file, $data);
        return $file;
    }

    public function forgot_password($mobile, $otpnew) {
        $smsmsg = "";
        $smssts = "";
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        if ($otpnew == "") {
            $otp = substr(number_format(time() * rand(), 0, '', ''), 0, 4);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "MOBILE = '" . $ENC_MOBILE . "' AND ACTIVE=1 and CATEGORY='Shuttle'";
            $row = $dao->c_selectrow('employees', $where);
            if ($row) {
                $ename = $dao->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
                $eid = $row['EMPLOYEES_ID'];
                $branch_id = $row['BRANCH_ID'];
                $smsmsg = "Dear $ename, Please use $otp as login OTP in " . SMS_TITLE;
                //$smssts = $this->sendsms($mobile, $smsmsg);
                $data2 = array('BRANCH_ID' => $branch_id, "ORIGINATOR" => 'TOPSCS', 'RECIPIENT' => $mobile, 'MESSAGE' => $smsmsg, 'STATUS' => 'U', 'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime);
                $dao->c_insert('sms', $data2);

                $data1 = array('MOBILE_NO' => $mobile, 'ROSTER_PASSENGER_ID' => $eid, 'OTP' => $otp, 'VERIFIED_STATUS' => 0,
                    'SMS_RESPONSE' => $smssts, 'OTP_CATEGORY' => 'Forgotpw', 'CREATED_BY' => $eid, 'CREATED_DATE' => $curdatetime
                );
                $cnt = $dao->c_insert('otp_verification', $data1);
                if ($cnt > 0) {
                    $element_array = array('status' => 1, 'Message' => "True");
                } else {
                    $element_array = array('status' => 0, 'Message' => "Please try again");
                }
            } else {
                $element_array = array('status' => 0, 'Message' => "Mobile no not registered");
            }
        } else {
            $where = "MOBILE_NO = '$mobile' and OTP='$otpnew' and OTP_CATEGORY='Forgotpw' and VERIFIED_STATUS=0 and CREATED_DATE between subtime('$curdatetime','00:15:00') and '$curdatetime'";
            $data = array('VERIFIED_STATUS' => 1);
            $cnt = $dao->c_update('otp_verification', $data, $where);
            if ($cnt > 0) {
                $element_array = array('status' => 1, 'Message' => "True");
            } else {
                $element_array = array('status' => 0, 'Message' => "Invalid OTP");
            }
        }
        return $element_array;
    }

    public function generate_newpassword($mobile, $newpassword) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        // $ENCRYP_PWD = $this->Encrypt_Script($newpassword, SECERT_KEY_NTL);
        $ENCRYP_PWD = $dao->AES_ENCRYPT($newpassword, AES_ENCRYPT_KEY);
        $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE=1 and CATEGORY='Shuttle'";
        $data1 = array(
            'password' => $ENCRYP_PWD,
            'updated_at' => $curdatetime
        );
        $cnt = $dao->c_update('employees', $data1, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Invalid OTP");
        }
        return $element_array;
    }

    public function change_password($oldpass, $newpass, $mobile) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        // echo "pw==". $ENCRYP_PWD=  $dao->AES_ENCRYPT($oldpass, AES_ENCRYPT_KEY);
        $ENCRYP_PWD_NEW = $dao->AES_ENCRYPT($newpass, AES_ENCRYPT_KEY);
        $ENCRYP_PWD = $dao->AES_ENCRYPT($oldpass, AES_ENCRYPT_KEY);
        $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' AND password = '$ENCRYP_PWD' and ACTIVE=1 and CATEGORY='Shuttle'";
        $data = array(
            'password' => $ENCRYP_PWD_NEW,
            'updated_at' => $curdatetime
        );
        $cnt = $dao->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Your old password is wrong");
        }
        return $element_array;
    }

    public function payment_history($eid, $bid) {
        $element_arr = array();
        $dao = new Shuttledao();
        $psts = "Failed";
        $respdate = "";
        $row1 = $dao->payment_history($eid, $bid);
        foreach ($row1 as $val) {
            $paidamt = 0;
            $respcode = $val['ResponseCode'];
            $respmsg = $val['ResponseMessage'];
            $rsts = $val['Status'];
            $respdate = $val['CreatedTime'];
            $payment_no = $val['PaymentNo'];
            $paidamt = $val['TotalPaidAmt'];
            $refun_id = $val['RefundId'];
            if (($respcode == "01" || $respcode == '000' ) && ($respmsg == "Txn Successful." || $respmsg == 'Approved') && ($rsts == "TXN_SUCCESS" || $rsts == 'SUCCESS') && is_null($refun_id)) {
                $psts = "Paid";
                // $element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
            } else if (($respcode == "10" || $respcode == '000' ) && ($respmsg == "Refund Successful." || $respmsg == 'Approved') && ($rsts == "TXN_SUCCESS" || $rsts == 'SUCCESS') && !is_null($refun_id)) {
                $psts = "Refunded";
                $paidamt = $val['RefundAmount'];
                // $refundamt=$val['RefundAmount'];
                $payment_no = $val['RefundReferanceId'];
                // $refundid=  explode(',', $val['RefundReferanceId']);               
//                for($i=0;$i<count($refundid);$i++)
//                {
//                    $payment_no=$refundid[$i];
//                    $paidamt=$refundamt;
//                    $element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
//                }
            } else {
                $psts = "Failed";
                //$element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => round($paidamt), 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
            }
            $element_arr[] = array('Sno' => $payment_no, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'StartDate' => $val['StartDate'], 'StartTime' => $val['StartTime'], 'EndTime' => $val['EndTime'], 'PackageAmt' => $paidamt, 'NoofDays' => $val['NoofDays'], 'PaymentStatus' => $psts, 'PaymentDatetime' => $respdate);
        }
        $element_array = array('payment_history' => $element_arr);
        return $element_array;
    }

    public function booking_details($eid, $bid) {
        $element_arr = array();
        $startDate = "";
        $endDate = "";
        $working_days = 0;
        $noof_rides = 0;
        $used_rides = 0;
        $noshow_ride = 0;
        $cancel_ride = 0;
        $addl_rides = 0;
        $extra_days = 0;
        $dao = new Shuttledao();
        $row1 = $dao->subscription_details($eid, $bid);
        foreach ($row1 as $val) {
            $bal_ride = 0;
            $startDate = $val['StartDate'];
            $endDate = $val['EndDate'];
            $travel_mode = $val['TravelMode'];
            $working_days = $this->getWorkingDays($startDate, $endDate);
            $noof_rides = $val['NoofRides'];
            $used_rides = $val['NoofRidesUsed'];
            $noshow_ride = $val['NoShowRides'];
            $cancel_ride = $val['NoofCancelRides'];
            $extra_days = $travel_mode == 'B' ? $working_days * 2 : $working_days;
            $addl_rides = $extra_days - $noof_rides;

            if (($cancel_ride >= $addl_rides) && ($cancel_ride > 0)) {
                $bal_ride = $noof_rides - ($used_rides + $noshow_ride + ($cancel_ride - $addl_rides));
                
               // echo $noof_rides."=".$used_rides ."=". $noshow_ride ."=". $cancel_ride."=". $addl_rides;
            } else {
                $bal_ride = $noof_rides - ($used_rides + $noshow_ride);
            }
            $element_arr[] = array('Sno' => $val['PaymentNo'], 'TotalAmount' => $val['TotalPaidAmt'], 'NoofDays' => $val['NoofDays'], 'NoofRides' => $noof_rides, 'NoofRidesUsed' => $used_rides, 'ExpiryDate' => $endDate, 'PickupPoint' => $val['PickupPoint'], 'DropPoint' => $val['DropPoint'], 'TravelMode' => $val['TravelMode'], 'PlanCategory' => $val['Tariff_Name'], 'RidesRemaining' => $bal_ride);
        }
        $element_array = array('booking_details' => $element_arr);
        return $element_array;
    }

    public function booking_list($eid, $bid) {
        $element_arr = array();
        $element_arr1 = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $curdate = $getdate['cur_date'];

        $noofdays = 0;
        $reshedulests = "false";
        $cancelsts = "false";
        $spoint = "";
        $dpoint = "";
        $stime = "";
        $etime = "";
        $payment_no = "";
        $n_payment_no = "";
        $startDate = "";
        $endDate = "";
        $working_days = 0;
        $cancel_ride = 0;
        $noofrides = 0;
        $noofridesused = 0;
        $noshowrides = 0;
        $addl_rides = 0;
        $seat_status = 0;

        //UPCOMING
        $where = "EmployeeId='$eid' and BranchId='$bid' and PaymentStatus='S' and Active!=3 and EndDate >= '$curdate'";
        $row1 = $dao->c_selectarray('shuttle_booking', $where);

        foreach ($row1 as $val) {
            $rosterid = 0;
            $cabid = 0;
            $i = 0;
            $payment_no = $val['PaymentNo'];
            $noofrides = $val['NoofRides'];
            $noofridesused = $val['NoofRidesUsed'];
            $noshowrides = $val['NoShowRides'];
            $cancel_ride = $val['NoofCancelRides'];
            $spoint = $val['PickupPoint'];
            $dpoint = $val['DropPoint'];
            $plat = $val['PickLatitude'];
            $plong = $val['PickLongitude'];
            $routetype = $val['RouteType'];
            $dlat = $val['DropLatitude'];
            $dlong = $val['DropLongitude'];
            $noofdays = $val['NoofDays'];
            $startDate = $val['StartDate'];
            $endDate = $val['EndDate'];
            if ($noofdays > 1 && $cancel_ride > 0) {
                $working_days = $this->getWorkingDays($startDate, $endDate);
                $addl_rides = $working_days - $noofrides;
                $noofrides = $cancel_ride < $addl_rides ? $noofrides + $cancel_ride : $noofrides + $addl_rides;
            }
            $row2 = $dao->booking_list_dao($payment_no, '>=', 'sts.Seat_status!=4', 'shuttle_trip_seat');
            foreach ($row2 as $val1) {
                $traveldate = $val1['Schedule_Date'];
                $activests = $val1['status'];
                $tripid = $val1['Trip_ID'];
                $tripseatid = $val1['Trip_Seat_ID'];
                $seat_status = $val1['Seat_status'];
                $route = $val1['Schedule_Route_ID'];
                $trip_id = $val1['Trip_ID'];
                $travel_type = $val1['Travel_Type'];
                $stime = $val1['Stop_Time'];
                $rpsts = 0;
                $traveldate_time = $traveldate . " " . $stime;

                if ($i < $noofrides) {

                    if ($activests == 2 && $seat_status == 1 && strtotime($traveldate) == strtotime($curdate)) { 
                        $route_id = "S" . $tripid;
                        $rosterrow = $dao->getroster_details($eid, $route_id, $traveldate);
                       // print_r($rosterrow);
                        foreach ($rosterrow as $result) {
                            $rosterid = $result['ROSTER_ID'];
                            $rpsts = $result['ROSTER_PASSENGER_STATUS'];
                            $rsts = $result['tracksts'];
                            $execute_time = $result['ACTUAL_START_TIME'];
                            $trip_time = date("Y-m-d H:i:s", strtotime("+" . TRIP_TIME . " hours", strtotime($execute_time)));
                            if (strtotime($curdate_time) <= strtotime($trip_time) || is_null($execute_time)) {
                               // echo "if";
                                if (in_array($rpsts, unserialize(RP_PICKUP_DROP_NOSHOW))) {
                                    $tripsts = "Cancelled";
                                } else {
                                    if (in_array($rsts, unserialize(R_CAB_TRACKING))) {
                                        $tripsts = "Upcoming";
                                        $rosterid = $result['ROSTER_ID'];
                                        $cabid = $result['CAB_ID'];
                                    } else if (in_array($rsts, unserialize(R_TRIP_CLOSE_HISTORY)) || $rsts > 1000) {
                                        $tripsts = "Completed";
                                        $rosterid = 0;
                                    } else {
                                        $tripsts = "Upcoming";
                                        $rosterid = $rosterid;
                                    }
                                }
                            } else {
                                //echo "else";
                                $tripsts = (in_array($rpsts, unserialize(RP_PICKUP_DROP_NOSHOW))) ? 'Cancelled' : 'Completed';
                                $rosterid = 0;
                            }
                        }
                    } else {                        
                        $rosterid = 0;
                        //echo "trip seat id==".$tripseatid."seat sts==".$seat_status."travel date==".$traveldate."<br>";
                        if (((strtotime($curdate_time) > strtotime($traveldate_time)) && $seat_status == 1)) {
                            $tripsts = "Completed";
                        } else {
                            if ($seat_status == 0) {
                                $tripsts = "Noshow"; //Noshow
                            } else if ($seat_status == 2) {
                                $tripsts = "Completed";
                            } else if ($seat_status == 3) {
                                $tripsts = "Cancelled";
                            } else if ($seat_status == 1) {
                                $tripsts = "Upcoming";
                            } else {
                                $tripsts = "Reshedule";
                            }
                        }
                    }

                    $seconds = strtotime($traveldate_time) - strtotime($curdate_time);
                    $days = floor($seconds / 86400);
                    $hours = floor(($seconds - ($days * 86400)) / 3600);
                    $minutes = floor(($seconds - ($days * 86400) - ($hours * 3600)) / 60);
                    if ($tripsts == "Upcoming" && $seat_status == 1) {
                        //enable cancel and reschedule discuss with md                   

                        if ($noofdays == 1 && $days >= 1) {
                            $cancelsts = "true";
                            $reshedulests = 'false';
                        }
                        //else if($noofdays == 1 && $days == 0 && $hours >=1)                    
                        else if (($noofdays == 1 && $days == 0 && $hours >= 0 ) && (in_array($rpsts, unserialize(RP_CREATED)) || $rpsts == 0)) {
                            $cancelsts = "true";
                            $reshedulests = 'false';
                           // $reshedulests = 'true';
                        }
                        // else if(($noofdays > 1 && $hours >= 6) || ($days >= 1))
                        else if ($noofdays > 1 && $days >= 0) {
                            $reshedulests = ($days == 0 && $hours >= 6) ? 'true' : ($days > 0 && $days < 2) ? 'true' : 'false';
                          //  $reshedulests = "true";
                            $cancelsts = "true";
                        } else {
                            $cancelsts = "false";
                            $reshedulests = 'false';
                        }

                        $element_arr[] = array('Sno' => $i, 'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $cancelsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype, 'TravelType' => $travel_type, 'RouteNo' => $route, 'TripId' => $trip_id);
                    } else if ($tripsts != 'Reshedule') {
                        // echo "else travel date==".$traveldate_time=$traveldate." ".$stime;     
                        $element_arr1[] = array('Sno' => $i, 'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $tripsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype, 'TravelType' => $travel_type, 'RouteNo' => $route, 'TripId' => $trip_id);
                    }
                    $i++;
                }
            }
        }

        $where = "EmployeeId='$eid' and BranchId='$bid' and PaymentStatus='S' and Active!=3 and EndDate < '$curdate'";
        $this->db->limit(5);
        $this->db->order_by("Sno desc");
        $row1 = $dao->c_selectarray('shuttle_booking', $where);
        // echo $this->db->last_query();
        foreach ($row1 as $val) {
            $rosterid = 0;
            $cabid = 0;
            $i = 0;
            $payment_no = $val['PaymentNo'];
            $noofrides = $val['NoofRides'];
            $noofridesused = $val['NoofRidesUsed'];
            $noshowrides = $val['NoShowRides'];
            $cancel_ride = $val['NoofCancelRides'];
            $spoint = $val['PickupPoint'];
            $dpoint = $val['DropPoint'];
            $plat = $val['PickLatitude'];
            $plong = $val['PickLongitude'];
            $routetype = $val['RouteType'];
            $dlat = $val['DropLatitude'];
            $dlong = $val['DropLongitude'];
            $noofdays = $val['NoofDays'];
            $startDate = $val['StartDate'];
            $endDate = $val['EndDate'];
            $row2 = $dao->booking_list_dao($payment_no, '<', 'sts.Seat_status!=4', 'shuttle_trip_seat'); //shuttle_trip_seat_history
            foreach ($row2 as $val1) {
                $traveldate = $val1['Schedule_Date'];
                $activests = $val1['status'];
                $tripid = $val1['Trip_ID'];
                $tripseatid = $val1['Trip_Seat_ID'];
                $seat_status = $val1['Seat_status'];
                $route = $val1['Schedule_Route_ID'];
                $trip_id = $val1['Trip_ID'];
                $travel_type = $val1['Travel_Type'];
                $stime = $val1['Stop_Time'];
                $rpsts = 0;
                $traveldate_time = $traveldate . " " . $stime;
                if ((strtotime($curdate_time) > strtotime($traveldate_time)) && $seat_status == 1) {
                    $tripsts = "Completed";
                } else if ($seat_status == 2) {
                    $tripsts = "Completed";
                } else if ($seat_status == 0) {
                    $tripsts = "Noshow";
                } else if ($seat_status == 3) {
                    $tripsts = "Cancelled";
                }
                $element_arr1[] = array('Sno' => $i, 'PickupPoint' => $spoint, 'DropPoint' => $dpoint, 'Plat' => $plat, 'Plong' => $plong, 'Dlat' => $dlat, 'Dlong' => $dlong, 'TravelDate' => $traveldate, 'StartTime' => $stime, 'CancelSts' => $tripsts, 'ResheduleSts' => $reshedulests, 'BookingID' => $tripseatid, 'NoofDays' => $noofdays, 'CabId' => $cabid, 'RosterId' => $rosterid, 'RouteType' => $routetype, 'TravelType' => $travel_type, 'RouteNo' => $route, 'TripId' => $trip_id);
            }
        }
        $b_array = $this->msort('asc', $element_arr, array('TravelDate', 'StartTime'));
        $c_array = $this->msort('desc', $element_arr1, array('TravelDate', 'StartTime'));
        $element_array = array('booking_list' => $b_array, 'completed_list' => $c_array);
        return $element_array;
    }

    public function booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category) {
        $element_array = array();
        $branchid = 1;
        $days = 0;
        $hours = 0;
        $ret = 0;
        $hrs = 0;
        $canceldate_time = $traveldate . " " . $stime;
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        if ($noofdays == 1) {
            if (strtotime($canceldate_time) >= strtotime($curdate_time)) {
                $seconds = strtotime($canceldate_time) - strtotime($curdate_time);
                $days = floor($seconds / 86400);
                $hours = floor(($seconds - ($days * 86400)) / 3600);
                $minutes = floor(($seconds - ($days * 86400) - ($hours * 3600)) / 60);
                if ($days >= 1) {
                    $hours = 22;
                    $ret = $this->property_check('CANCELLATION POLICY ONEDAY', $branchid, $hours);
                    $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_ONDAY, $category);
                } else {
                    $hrs = $hours == 0 && $minutes < 60 ? 0 : $hours;
                    $ret = $this->property_check('CANCELLATION POLICY ONEDAY', $branchid, $hrs);
                    $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_ONDAY, $category);
                }
            } else {
                $element_array = array('status' => 'false', 'message' => 'Invalid Date');
            }
        } else {
            $seconds = strtotime($canceldate_time) - strtotime($curdate_time);
            $days = floor($seconds / 86400);
            $hours = floor(($seconds - ($days * 86400)) / 3600);
            $hrs = $days >= 1 ? 22 : $hours;
            $ret = $this->property_check('CANCELLATION POLICY REGULAR', $branchid, $hrs);
            $element_array = $this->cancellation_policy($trans_id, $eid, $ret, SHUTTLE_TRIP_REGULAR, $category);
        }
        return $element_array;
    }

    private function property_check($property_name, $branch_id, $hours) {
        $ret = 0;
        $dao = new Shuttledao();
        //get current date time
        $getdate = $dao->get_datetime();
        $intime = $getdate['cur_time'];
        $where = "PROPERTIE_NAME='$property_name' and ACTIVE='1' and BRANCH_ID='$branch_id'";
        $row = $dao->c_selectrow('properties', $where);
        if ($row) {
            if ($property_name == "CANCELLATION POLICY ONEDAY") {
                $timevalue = unserialize($row['PROPERTIE_VALUE']);
                foreach ($timevalue as $test) {
                    list($t1, $t2, $t3) = $test;
                    $ret1 = $this->check_time($t1, $t2, $hours) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $ret = $t3;
                    }
                }
            } else if ($property_name == "CANCELLATION POLICY REGULAR") {
                $timevalue = unserialize($row['PROPERTIE_VALUE']);
                foreach ($timevalue as $test) {
                    list($t1, $t2, $t3) = $test;
                    $ret1 = $this->check_time($t1, $t2, $hours) ? "yes" : "no";
                    if ($ret1 == "yes") {
                        $ret = $t3;
                    }
                }
            }
        }
        return $ret;
    }

    public function cancellation_policy($transid, $eid, $retpercentage, $duration, $category) {
        $arr = array();
        $paidamount = 0;
        $refundamount = 0;
        $noof_rides = 0;
        $used_rides = 0;
        $refund = 0;
        $working_days = 0;
        $cancel_ride = 0;
        $addl_rides = 0;
        $startDate = "";
        $endDate = "";
        $dao = new Shuttledao();
        if ($duration == SHUTTLE_TRIP_ONDAY) {
            $row = $dao->cancellation_policy($transid, $eid, $duration);
            if ($row) {
                $paidamount = $row['paidamt'];
                $travelmode = $row['TravelMode'];
                $refundamount = ($paidamount * $retpercentage) / 100;
                $refund = $travelmode == 'B' ? $refundamount / 2 : $refundamount;
                $arr = array('status' => 'true', 'PaidAmount' => $paidamount, 'RefundAmount' => $refund, 'category' => $category);
            }
        } else {
            $row = $dao->cancellation_policy($transid, $eid, $duration);
            if ($row) {
                $startDate = $row['StartDate'];
                $endDate = $row['EndDate'];
                $working_days = $this->getWorkingDays($startDate, $endDate);
                $noof_rides = $row['NoofRides'];
                $used_rides = $row['NoofRidesUsed'];
                $noshow_ride = $row['NoShowRides'];
                $cancel_ride = $row['NoofCancelRides'];
                $addl_rides = $working_days - $noof_rides;
                if ($cancel_ride >= $addl_rides) {
                    $bal_ride = $noof_rides - ($used_rides + $noshow_ride + ($cancel_ride - $addl_rides));
                } else {
                    $bal_ride = $noof_rides - ($used_rides + $noshow_ride);
                }
                $arr = array('status' => 'true', 'available_rides' => $bal_ride, 'ride_expiry_date' => $row['EndDate'], 'category' => $category);
            }
        }
        return $arr;
    }

    // 8465@@#429093@@#6093@@#2017-07-21@@#17:30:10@@#1@@#0@@#5.00@@#4.5@@#2@@#0.0@@#0.0
    //8465@@#634341@@#73459@@#2017-08-08@@#20:30:21@@#40@@#0@@#0@@#0@@#1@@#0.0@@#0.0
    public function booking_cancel($trans_id, $eid, $traveldate, $stime, $noofdays, $roster_id, $paidfare, $refundfare, $confirmsts, $emplat, $emplong, $mobileno) {
        $element_array = array();
        $dao = new Shuttledao();
        //get current date time
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $stsarr = array('confirm_status' => $confirmsts);
        switch ($confirmsts) {
            case 1:
                if ($noofdays > 1) {
                    $category = 2; //regular
                    $element_array = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category);
                } else {
                    $category = 1; //oneday
                    $element_array = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category);
                }
                return array_merge($element_array, $stsarr);
                break;
            case 2:
                if ($noofdays > 1) {
                    $category = 2; //regular
                    $element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                } else {
                    $category = 1; //oneday
                    //payment return here
                    $element_array1 = $this->booking_edit($trans_id, $traveldate, $stime, $eid, $noofdays, $category);                    
                    if($element_array1['status']=='true' && $element_array1['RefundAmount']>0)
                    {
                        $refundfare=$element_array1['RefundAmount'];                    
                        $element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                        $paymentdet = $dao->getTxnid($trans_id);
                        if ($paymentdet && $element_array['status'] == 'true') {
                            $orderid = $paymentdet['Payment_Token'];
                            $txnid = $paymentdet['TxnId'];
                            $txnTimeStamp = $paymentdet['TxnTimeStamp'];
                            $substr = substr($orderid, 0, 1);
                            if ($substr == 'J') {
                                $this->jiomoney_refund($orderid, $txnid, $refundfare, $mobileno, $txnTimeStamp);
                            } else {
                                $this->paytmwalletmoney_refund($orderid, $txnid, $refundfare, $mobileno);
                            }
                        }
                    }
                    else
                    {
                        $element_array = array('status' => 'false', 'confirm_status' => $confirmsts, 'category' => $category);
                    }
                    //$element_array = $this->booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category);
                }
                return $element_array;
                break;
            default:
                $element_array = "False5";
                return $element_array;
                break;
        }
    }

    public function booking_cancel_confirm($roster_id, $trans_id, $paidfare, $refundfare, $eid, $trip_type, $emplat, $emplong, $confirmsts, $category) {
        $element_array = array();
        $dao = new Shuttledao();
        //get current date time

        $cnt = $dao->shuttle_booking_update($trans_id, $category, $roster_id, $eid, 3); //cancel
        if ($cnt == 1) {
            $element_array = array('status' => 'true', 'confirm_status' => $confirmsts, 'category' => $category);
        } else {

            $element_array = array('status' => 'false', 'confirm_status' => $confirmsts, 'category' => $category);
        }
        return $element_array;

        //noshow in roster_passenger
        //cancel in shuttle_booking_trans
    }

//14203@@#21813@@#354804@@#14378@@#109@@#36183@@#0.0@@#0.0"
    public function booking_reshedule($old_trip_id, $roster_id, $trans_id, $new_trip_id, $stop_id, $eid, $emplat, $emplong) {
        $dao = new Shuttledao();
        $sts = "false";
        $element_array = array();
        $category = 1;
        //echo $old_trip_id,$roster_id,$trans_id,$new_trip_id,$stop_id, $eid, $emplat, $emplong;
        //exit;
        $ret = $dao->shuttle_booking_update($trans_id, $category, $roster_id, $eid, 4); //4-'reschedule

        if ($ret == 1) {
            $dao->getpayment_tokenno($new_trip_id, $trans_id, $stop_id, $eid);
            $sts = 'true';
        }

        $element_array = array('status' => $sts);
        return $element_array;
    }
    public function stage_booking_reshedule($old_trip_id, $roster_id, $trans_id, $new_trip_id, $stop_id, $eid, $emplat, $emplong,$p_routeorder_p,$p_routeorder_d) {
        $dao = new Shuttledao();
        $sts = "false";
        $element_array = array();
        $category = 1;
        //echo $old_trip_id,$roster_id,$trans_id,$new_trip_id,$stop_id, $eid, $emplat, $emplong;
        //exit;
        $ret = $dao->shuttle_booking_update($trans_id, $category, $roster_id, $eid, 4); //4-'reschedule

        if ($ret == 1) {
            $dao->stagebooking_payment_tokenno($new_trip_id, $trans_id, $stop_id, $eid,$p_routeorder_p,$p_routeorder_d);
            $sts = 'true';
        }

        $element_array = array('status' => $sts);
        return $element_array;
    }

    private function check_time($t1, $t2, $tn) {
        if ($t2 >= $t1) {
            return $t1 <= $tn && $tn <= $t2;
        } else {
            return !($t2 <= $tn && $tn <= $t1);
        }
    }

    public function reason($bid) {
        $arr = array();
        //creae object for driver dao class
        $dao = new Shuttledao();
        try {
            //get reason master values
            $where = "ACTIVE = '1' and BRANCH_ID='$bid' and CATEGORY='ShuttleFeedback'";
            $row = $dao->c_selectarray('reason_master', $where);
            foreach ($row as $val) {
                $arr[] = array('sno' => $val['REASON_ID'], 'reason' => $val['REASON'], 'rstatus' => $val['CATEGORY']);
            }
            $element_array = array('status' => 1, 'reason_list' => $arr);
            return $element_array; //return response to controller
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    public function shuttle_support() {
        $tmp = array();
        $dao = new Shuttledao();
        $where = "ACTIVE=1 and CATEGORY='Shuttle' and BRANCH_ID='1'";
        $row = $dao->c_selectarray('reason_master', $where);
        foreach ($row as $arg) {
            $tmp[$arg['SUPPORT_CATEGORY']][] = $arg['SUB_SUPPORT_CATEGORY'] . '@@#' . $arg['REASON'] . '@@#' . $arg['REASON_ID'];
        }
        $output = array();
        foreach ($tmp as $type => $labels) {
            $output[] = array(
                'category' => $type,
                'sub_category' => $labels
            );
        }
        // echo json_encode($output);
        return array('Apiresponse' => $output);
    }

    public function shuttle_support_log($bid, $empid, $reasonid, $remarks) {
        $element_array = array();
        $sts = "false";
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        $data = array('EMPLOYEE_ID' => $empid, 'REASON_ID' => $reasonid, 'REMARKS' => $remarks, 'PROCESS_TIME' => $curdatetime, 'BRANCH_ID' => $bid);
        $cnt = $dao->c_insert('support_log', $data);
        if ($cnt > 0) {
            $sts = "true";
        }
        $element_array = array('Apiresponse' => $sts);
        return $element_array;
    }

    public function shuttle_issue() {
        $element_arr = array(); //shuttle_issue
        $where = "Active=1";
        $dao = new Shuttledao();
        $row = $dao->c_selectarray('shuttle_issue', $where);
        foreach ($row as $val) {
            $element_arr[] = array('Sno' => $val['Sno'], 'IssueName' => $val['IssueName'], 'IssueSubName' => $val['IssueSubName'], 'IssueDetails' => $val['IssueDetails']);
        }
        $element_array = array('issue_det' => $element_arr);
        return $element_array;
    }

    public function emp_app_panic($empid, $cab_id, $roster_id, $lat, $lng, $location, $branch_id) {
        $element_array = array();
        //create object
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_time = $getdate['cur_date_time'];

        if ($roster_id == 0) {
            $data = array('EMPLOYEE_ID' => $empid, 'BRANCH_ID' => $branch_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        } else {
            $data = array('EMPLOYEE_ID' => $empid, 'BRANCH_ID' => $branch_id, 'ROSTER_ID' => $roster_id, 'CAB_ID' => $cab_id, 'LAT' => $lat, 'LONG' => $lng, 'ADDRESS' => $location, 'CREATED_BY' => $empid, 'created_at' => $cur_time);
        }
        $cnt = $dao->c_insert('panic_alert', $data);
        if ($cnt > 0) {
            $element_array = array('status' => 'true');
        } else {
            $element_array = array('status' => 'false');
        }
        return $element_array;
    }

   // public function insert_route_master($startpoint, $endpoint, $startlat, $startlong, $endlat, $endlong, $duration, $stime, $etime, $routepath, $route_type, $ct) {
    public function insert_route_master($duration,$routepath, $route_type,$routeid,$branchid, $ct) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $cur_time = $getdate['cur_date_time'];
         $this->load->model('ShuttleElasticmdl');
        /*$tottime = $this->sec_to_time($duration);
        $this->load->model('ShuttleElasticmdl');
        $data = array('StartPoint' => $startpoint, 'EndPoint' => $endpoint, 'Start_Latitude' => $startlat, 'Start_Longitude' => $startlong, 'End_Latitude' => $endlat,
            'End_Longitude' => $endlong, 'StartTime' => $stime, 'EndTime' => $etime, 'Travel_Duration' => $tottime, 'Process_Datetime' => $cur_time);
        $cnt = $dao->c_insert('shuttle_route_master', $data);
        $id = $this->db->insert_id();
        if ($cnt > 0) {
            $userColData = json_decode($routepath, true);
            $path = $userColData['RoutePath'];
            $this->ShuttleElasticmdl->pathInsert($id, '1', 4107, $path, $route_type);
        }*/
        $userColData = json_decode($routepath, true);
            $path = $userColData['RoutePath'];
            $this->ShuttleElasticmdl->pathInsert($routeid, $branchid, $duration, $path, $route_type);
    }

    public function shuttle_notification($eid, $bid) {
        $element_arr = array();
        $dao = new Shuttledao();
        $row=$dao->notification($eid, $bid) ;
        foreach ($row as $val) {
            $element_arr[] = array('Heading' => $val['heading'], 'Message' => $val['message'], 'DateTime' => $val['created_at']);
        }
        $element_array = array('notification' => $element_arr);
        return $element_array;
    }

    public function shuttle_invoice($bookingno) {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function shuttle_termscondition() {
        $element_array = array('status' => 'true');
        echo $this->output($element_array);
    }

    public function tracking($rosterid, $cabid, $eid, $gpsdate) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $cur_datetime = $getdate['cur_date_time'];
        $cur_date = $getdate['cur_date'];
        $data = array();
        $element_arr1 = array();
        $element_arr = array();
        $gps_det = array();
        $tripsts = "";
        $tripmsg = "";
        $drivername = "";
        $drivermobile = "";
        $vehno = "";
        $vehmod = "";
        //employee details
        $row1 = $dao->track1($rosterid, $cabid, $eid);
        if ($row1) {
            $cab_arrived_time = $row1['cab_arrived_time'];
            $boarded_time = $row1['droptime'];
            $trip_type = $row1['TRIP_TYPE'];
            $rsts = $row1['ROSTER_PASSENGER_STATUS'];
            if ($trip_type == TRIP_P) {
                if (is_null($cab_arrived_time)) {
                    $tripsts = "1";
                    $tripmsg = "Live Tracking";
                } else if ((!is_null($cab_arrived_time)) && (!in_array($rsts, unserialize(RP_PICKUP_DROP_OTP)))) {
                    $tripsts = "2";
                    $tripmsg = "OTP :" . $row1['OTP'] . " ";
                } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                    $tripsts = "3";
                    $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                }
            } else {
                if (is_null($boarded_time) && (!in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                } else if (!(is_null($boarded_time)) && (!in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "1";
                    $tripmsg = "Not Boarded";
                } else if ((!is_null($boarded_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "1";
                    $tripmsg = " OTP :" . $row1['OTP'];
                } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                    $tripsts = "3";
                    $tripmsg = "You have reached your location!!!!";
                }
            }
            $order = floatval($row1['ROUTE_ORDER']);
            $waypoints = '';
            $row2 = $dao->track2($rosterid, $cabid, $order, $eid);
            if (count($row2) > 0) {
                foreach ($row2 as $val) {
                    $element_arr1[] = array('emplat' => $val['LATITUDE'], 'emplong' => $val['LONGITUDE'], 'pickuptime' => $val['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'], AES_ENCRYPT_KEY), 'emplocation' => $val['LOCATION_NAME']);
                    $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                }
            } else {
                $element_arr1[] = array('emplat' => $row1['LATITUDE'], 'emplong' => $row1['LONGITUDE'], 'pickuptime' => $row1['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'], AES_ENCRYPT_KEY), 'emplocation' => $row1['LOCATION_NAME']);
                $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
            }
            $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
            if ($gpsdate == "1900-01-01 00:00:00") {
                $driverdet = $dao->get_driver_details($cabid);
                if ($driverdet) {
                    $drivername = $driverdet['DRIVERS_NAME'];
                    $drivermobile = $driverdet['DRIVER_MOBILE'];
                    $vehno = $driverdet['VEHICLE_REG_NO'];
                    $driverimage = $driverdet['DRIVER_IMAGE'];
                    $vehmod = $driverdet['MODEL'];
                }
            }
            $ret = $this->ShuttleElasticmdl->cabNavigation($cabid, '1900-01-01 00:00:00');
            $origin = $ret['RESULT'][0]['POSITION'];
            $driverimage = mysql_real_escape_string("https://mytransportportal.com/cog_track/img/driver_img/photo.jpg");
            $km = $this->calkm($origin, $destination, $waypoints, 'both', 'driving');
            $x = explode("-", $km);
            if ($x[1] > 0) {
                $hours = floor($x[1] / 3600);
                $mins = floor($x[1] / 60 % 60);
                $minutes = $hours == 0 ? $mins . " mins" : $hours . 'hrs -' . $mins . " mins";
            } else {
                $minutes = "NA";
            }
            $data[] = array('DriverName' => $drivername,
                "DriverMobile" => $drivermobile,
                "CabNo" => $vehno,
                "cabLatLong" => $origin,
                "TripStatus" => $tripsts,
                "TripMsg" => $tripmsg,
                "DriverImage" => $driverimage,
                'CabModel' => $vehmod,
                "ETA" => $minutes);
            //}

            $element_arr = $this->ShuttleElasticmdl->cabNavigation($cabid, $gpsdate);
            //print_r($element_arr);
            $gps_det = $element_arr['RESULT'];
        }

        $element = array('DriverDetails' => $data, "details" => $gps_det, 'empdetails' => $element_arr1);
        //echo json_encode($element);

        return $element;
    }

    public function emp_logout($eid) {
        return $element_array = array('status' => 'true');
    }

    //get employee feedback 
    //insert feedback log table
    public function emp_feedback($roster_id, $emp_id, $feedback, $comments) {
        $ret = false;
        $dao = new Shuttledao();
        try {
            //get current date time
            $getdate = $dao->get_datetime();
            $curdatetime = $getdate['cur_date_time'];
            //$feedbackmsg = explode('|', $feedback);
            $data = array('ROSTER_ID' => $roster_id, 'EMPLOYEE_ID' => $emp_id, 'FEEDBACK1' => $feedback, 'COMMENTS' => $comments, 'CREATED_DATE' => $curdatetime);
            $cnt = $dao->c_insert('feedback_log', $data); //insert feedback log table
            if ($cnt > 0) {
                $ret = true;
            }
            $element_array = array('status' => $ret);
            return $element_array; //return response to controller
        } catch (Exception $e) {
            // echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    //--new table search and booking below//

    public function shuttle_searchroute($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        // $seat_count_time= date("H:i:s", strtotime('+2 hours', strtotime($curtime)));
        // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, 1, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, 1, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
        
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }           
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];               
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {                   
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);                    
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                            $traveltime = $traveldist[1];
                            // echo $plat, $plong, $dlat, $dlong;
//                            $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            // $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                        } else {
                            // if($shedule_date==$curdate && $stime > $curtime)
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $sts = "";
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                    $sts = 'true';
                                } else {
                                    if ($route_name == TRIP_P) {
                                        if ($a_p_picktime <= $seat_count_time && $seats_used > 0) {
                                            $sts = 'true';
                                        } else if ($a_p_picktime > $seat_count_time && $seats_used >= 0) {
                                            $sts = 'true';
                                        } else {
                                            $sts = 'false';
                                        }
                                    } else {
                                        $sts = 'true';
                                    }
                                    $shuttletime = "ANext";
                                }
                                if ($sts == 'true') {

                                    $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    $traveldist = $this->GetDrivingKm($origin, $destination);
//                                print_r($traveldist);
//                                exit;
                                    $package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                    $traveltime = $traveldist['time'];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                                
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                                }
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = $this->GetDrivingKm($origin, $destination);
                                // print_r($traveldist);
                                $package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                $traveltime = $traveldist['time'];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                              
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                            $traveltime = $traveldist[1];
//                            $dist=$route_type=='Fixed'? 0.2:$this->distance($endlat,$endlong, $plat, $plong, 'K');
//                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($startlat,$startlong, $dlat, $dlong, 'K');
//                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
////                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
////                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
////                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                        } else {
                            if ($shedule_date == $curdate && $stime > $curtime) {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "ANext";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($endlat,$endlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($startlat,$startlong, $dlat, $dlong, 'K');
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);                              
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($endlat,$endlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($startlat,$startlong, $dlat, $dlong, 'K');
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }
    
    public function withoutgooglekey_search($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category)
    {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation=1.5;
        $time_deviation=180;
        $dist=0;
        $dist1=0;
       // $seat_count_time= date("H:i:s", strtotime('+2 hours', strtotime($curtime)));

       // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type,$resheduledate,$reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type,$resheduledate,$reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type,$resheduledate,$reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type,$resheduledate,$reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
//        print_r($pickarray);
//        print_r($droparray);
//        exit;
        
        $branchid=$dao->getbranchid($emp_id, $mobileno);
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }           
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used=0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id,$resheduledate);                    
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id=$row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];                        
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id=$val['PICK_STOP_ID']; 
                        $seats_used=$row['Seats_Used'];
                        $route_name=$row['Route_Name'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);  
                        $seat_count_time= date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if($resheduledate!='0000-00-00')
                        {
                            $shuttletime="ANext"; 
                            //$dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            //$dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            //$traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));                            
                            //$package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                            //$traveltime = $traveldist[1];
                           // echo $plat, $plong, $dlat, $dlong;
                              $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
                              $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
                              $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
                              $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0,$branchid);  
							  $traveltime =round($traveldist*$km_deviation*$time_deviation);
                              $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                           // $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                        }
                        else
                        {
                           // if($shedule_date==$curdate && $stime > $curtime)
                             if($shedule_date==$curdate && $a_p_picktime > $curtime)
                            {
                                $sts="";                   
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                     $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                    $sts = 'true';
                                }
                                else
                                {
                                   if($route_name==TRIP_P){
                                        if($a_p_picktime <= $seat_count_time && $seats_used > 0){
                                            $sts='true';
                                        }
                                        else if($a_p_picktime > $seat_count_time && $seats_used >= 0)
                                        {
                                           $sts='true'; 
                                        }
                                        else{
                                            $sts='false';
                                        }
                                   }
                                   else {
                                       $sts='true'; 
                                   }
                                   $shuttletime="ANext";                                                               
                                }
                                if($sts=='true'){
                                   
                               // $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                               // $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                //$traveldist = $this->GetDrivingKm($origin, $destination);
//                                print_r($traveldist);
//                                exit;
                                //$package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type,$vehicle_id, 0);
                                //$traveltime = $traveldist['time'];   
                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                                
                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0,$branchid);
                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
							    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                               
 //                               $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                                }
                            }                        
                            else if($shedule_date > $curdate)
                            {
                                if (date("w", strtotime($curdate))==6 || date("w", strtotime($curdate))== 5) {
                                    
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                    $sts = 'true';
                                }
                                else
                                {
                                   $shuttletime="BTomorrow";                                                               
                                }
                                //$dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                //$dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                //$traveldist = $this->GetDrivingKm($origin, $destination);
                               // print_r($traveldist);
                                //$package = $this->tariff_cal(round($traveldist['distance']), $ttype, $route_type,$vehicle_id, 0);
                                //$traveltime = $traveldist['time'];  
                               $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                              
                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0,$branchid);
                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
								 $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
//                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            } 
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {                    
                    $seatavial = $dao->get_seatavailability($route_id,$resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id=$row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];   
                        $route_name=$row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id=$val['PICK_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if($resheduledate!='0000-00-00' && $stime > $curtime)
                        {
                            $shuttletime="ANext"; 
                           // $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            //$dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            //$traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            //$package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                            //$traveltime = $traveldist[1];
                            $dist=$route_type=='Fixed'? 0.2:$this->distance($endlat,$endlong, $plat, $plong, 'K');
                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($startlat,$startlong, $dlat, $dlong, 'K');
                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0,$branchid);
                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
					        $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                    ////                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
////                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
        //                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                        }
                        else
                        {
                            if($shedule_date==$curdate && $stime > $curtime)
                            {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                     $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                    $sts = 'true';
                                }
                                else
                                {
                                   $shuttletime="ANext";                                                               
                                }
                              //  $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                              //  $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                              //  $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                              //  $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                              //  $traveltime = $traveldist[1];
                                $dist=$route_type=='Fixed'? 0.2:$this->distance($endlat,$endlong, $plat, $plong, 'K');
                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($startlat,$startlong, $dlat, $dlong, 'K');
                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0,$branchid);
                                $traveltime =round($traveldist*$km_deviation*$time_deviation); 
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         								
//                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id,'package_details' => $package);                         
                            }                        
                            else if($shedule_date > $curdate)
                            {
                                if (date("w", strtotime($curdate))==6 || date("w", strtotime($curdate))== 5) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                    $sts = 'true';
                                }
                                else
                                {
                                   $shuttletime="BTomorrow";                                                               
                                }
                               // $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                               // $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                               // $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                              //  $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type,$vehicle_id, 0);
                              //  $traveltime = $traveldist[1]; 
                                $dist=$route_type=='Fixed'? 0.2:$this->distance($endlat,$endlong, $plat, $plong, 'K');
                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($startlat,$startlong, $dlat, $dlong, 'K');
                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0,$branchid);
                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
								$element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist*$km_deviation), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
//                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong,'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME,'stop_id'=>$stop_id, 'package_details' => $package);                         
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc',$element_arr, array('route_date','approxtime','shuttle_time'));
        $d_array = $this->msort('asc',$element_arr1, array('route_date','approxtime','shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }

    //5045@@#6093@@#AVADI BUS STATION@@#13.11969@@#80.1028@@#DLF MANAPAKKAM@@#13.02283@@#80.17507@@#P@@#13:00:32@@#00:00:00@@#51563@@#0@@#30@@#Next@@#0@@#Km@@#0@@#0e1a1197-2f88-4fd1-b0c4-82a84d218700@@#358575596@@#9751869896@@#595@@#0@@#1@@#83@@#paytm@@#22@@#1091@@#20.0
    public function shuttle_booking_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, $comp_id, $payment_category) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
       // $this->load->model('ShuttleElasticmdl');

        if ($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0 || $dlat == 0 || $dlong == 0 || $ssotoken == 'null') {
            $element_array = array('status' => 0, 'message' => 'Subsciption Failed');
            return $element_array;
            exit;
        }
        if ($noofdays > 1) {
            $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            $totdays=$noofdays-1;
            $enddate = date('Y-m-d', strtotime("+$totdays day", strtotime($shuttledate)));
            $where = "EmployeeId='$eid' and '$shuttledate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $dao->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {            
            if ($ptag == "Next") {
                if ($ttype == "B" && $dtag == "Tomorrow") {
                    $shuttledate = $curdate;
                    $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                } else {
                    $shuttledate = $curdate;
                    $enddate = $curdate;
                }
            } else if ($ptag == "Tomorrow") {
                $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            } else {
                $shuttledate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $enddate = date('Y-m-d', strtotime('next monday', strtotime($curdate)));
                $i=$dao->check_noservice_date($shuttledate);
                if($i==0){
                    $shuttledate =  date('Y-m-d', strtotime('+1 day', strtotime($shuttledate))); 
                    $enddate=$shuttledate;
                }
            } 
            if (date("w", strtotime($shuttledate)) == 6 || date("w", strtotime($shuttledate)) == 0 ) 
            {
                $element_array = array('status' => 0, 'message' => 'There is no shuttle service for this date');
                return $element_array;
                exit; 
            }
            $subscribtionsts = 0;
        }
        $message = $this->seats_confirmed_sts($ttype, $tripid_p, $tripid_d);
        if ($message == "Confirmed") {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {
                $origin = $plat . "," . $plong;
                $destination = $dlat . "," . $dlong;        
                $km=$this->GetDrivingKm($origin, $destination);  
                $tdist=round($km['distance']);
               //$tdist = round($this->distance($plat,$plong,$dlat,$dlong,'K')*1.5);//without google key
                $totdays = 0;
                $tfare = 0;
                $validtrip = 0;                
                
                //if (count($tariff) > 0 && $noofdays > 0) {                    
                if ($ttype == "B") {
                    $totdays = $noofdays;
                    $veh_id_p = $dao->getvehicleid($tripid_p);
                    $tariff_p = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_p, $noofdays,$branch_id);
                    $veh_id_d = $dao->getvehicleid($tripid_d);
                    $tariff_d = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_d, $noofdays,$branch_id);
                    if (count($tariff_p) > 0 && count($tariff_d) > 0) {
                        $tfare = $tariff_p[0]['fare'] + $tariff_d[0]['fare'];
                        $validtrip = $tariff_p[0]['validTrip'] * 2;
                        $totdist = $tdist * 2;
                        $tariff_id = $tariff_p[0]['tariff_id'];
                    }
                } else {
                    $totdays = $noofdays;
                    $veh_id_p = $dao->getvehicleid($tripid_p);
                    $tariff = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_p, $noofdays,$branch_id);
                    $tfare = $tariff[0]['fare'];
                    //$tfare = $tariff[0]['fare'];
                    $validtrip = $tariff[0]['validTrip'];
                    $totdist = $tdist;
                    $tariff_id = $tariff[0]['tariff_id'];
                }
                $data = array('BranchId' => $branch_id, 'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => trim($ppoint), 'DropPoint' => trim($dpoint), 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $tfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $shuttledate, 'EndDate' => $enddate, 'NoofDays' => $totdays, 'NoofRides' => $validtrip, 'TotalPaidAmt' => $tfare, 'RouteType' => $route_type, 'TariffId' => $tariff_id);
                $cnt = $dao->c_insert('shuttle_booking', $data);
                //echo $this->db->last_query();
                // exit;
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = $payment_category == "paytm" ? "P" . strtotime(date('YmdHis')) . $id : "J" . strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $dao->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {                      
                        if ($payment_category == "paytm" || empty($payment_category)) {
                            $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $tfare, $stopid_p, $stopid_d,0,0,0,0);
                            $element_array = array('status' => 1, 'message' => $response);
                        } else {
                            $response = $this->jiomoney_debitbalance($paymentno, $mobileno, $tfare, $stopid_p, $stopid_d,0,0,0,0);
                            if (is_array($response)) {
                                $element_array = array('status' => 1, 'message' => 'success', 'jio_response' => $response);
                            } else {
                                $element_array = array('status' => 1, 'message' => $response);
                            }
                        }

                        // $response=$this->paytmwithdraw_response($paymentno, 0, 0, $mobileno,$stopid_p,$stopid_d);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscribtion already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
     //   $this->ShuttleElasticmdl->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, json_encode($element_array));
        return $element_array;
    }

    public function seats_confirmed_sts($ttype, $tripid_p, $tripid_d) {
        $dao = new Shuttledao();
        $avail_p = false;
        $avail_d = false;
        $message = "";
        if ($ttype == "B") {
            $avail_p = $dao->get_seatcount($tripid_p);
            $avail_d = $dao->get_seatcount($tripid_d);
            if ($avail_p == true && $avail_d == true) {
                $message = "Confirmed";
            } else if ($avail_p == true && $avail_d == false) {
                $message = "Pickup routes only available";
            } else if ($avail_p == false && $avail_d == true) {
                $message = "Return routes only available";
            } else {
                $message = "No seats available";
            }
        } else {
            $avail_p = $dao->get_seatcount($tripid_p);
            if ($avail_p == true) {
                $message = "Confirmed";
            } else {
                $message = "No seats avialable";
            }
        }
        return $message;
    }

    public function tracking_updated($rosterid, $cabid, $eid, $gpsdate) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $cur_datetime = $getdate['cur_date_time'];
        $cur_date = $getdate['cur_date'];
        $data = array();
        $element_arr1 = array();
        $element_arr = array();
        $gps_det = array();
        $tripsts = "";
        $tripmsg = "";
        $drivername = "";
        $drivermobile = "";
        $vehno = "";
        $vehmod = "";
        $blat = 0;
        $blong = 0;
        $slat = 0;
        $slong = 0;
        $sloc = "";
        $eloc = "";
        //employee details
        $row1 = $dao->track1($rosterid, $cabid, $eid);
        if ($row1) {
            $cab_arrived_time = $row1['cab_arrived_time'];
            $boarded_time = $row1['droptime'];
            $trip_type = $row1['TRIP_TYPE'];
            $rsts = $row1['ROSTER_PASSENGER_STATUS'];
            $blat = $row1['blat'];
            $blong = $row1['blong'];
            $slat = $row1['LATITUDE'];
            $slong = $row1['LONGITUDE'];
            $route_order = $row1['ROUTE_ORDER'];
            $sloc = $row1['ADDRESS'];
            $eloc = $row1['bloc'];
            if ($trip_type == TRIP_P) {
//                    $order = "rp.ROUTE_ORDER>=".$row1['ROUTE_ORDER']."";
                $order = $row1['ROUTE_ORDER'];
                if (is_null($cab_arrived_time)) {
                    $tripsts = "1";
                    $tripmsg = "Live Tracking";
                    $tracking = 1;
                } else if ((!is_null($cab_arrived_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "2";
                    $tripmsg = "Cab reached your Location.OTP is " . $row1['OTP'] . " ";
                    $tracking = 1;
                } else if (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP))) {
                    $tripsts = "3";
                    $tripmsg = "You have entered the cab.Have a safe journey!!!!";
                    $tracking = 1;
                }
                $blat = $row1['blat'];
                $blong = $row1['blong'];
                $slat = $row1['LATITUDE'];
                $slong = $row1['LONGITUDE'];
                $sloc = $sloc;
                $eloc = $eloc;
            } else {
                $order = $row1['ROUTE_ORDER'];
                if (is_null($boarded_time) && (in_array($rsts, unserialize(RP_CREATED)))) {
                    $tripsts = "1";
                    //$tripmsg = "Not Boarded";
                    $tripmsg = " OTP :" . $row1['OTP'];
                    $tracking = 1;
                } else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_CREATED)))) {
                    $tripsts = "1";
                    //$tripmsg = "Not Boarded";
                    $tripmsg = " OTP :" . $row1['OTP'];
                    $tracking = 1;
                } else if ((!is_null($boarded_time)) && (in_array($rsts, unserialize(RP_ARRIVED_BOARDED)))) {
                    $tripsts = "3";
                    $tripmsg = " OTP :" . $row1['OTP'];
                    $tracking = 1;
                } else if (!(is_null($boarded_time)) && (in_array($rsts, unserialize(RP_PICKUP_DROP_OTP)))) {
                    $tripsts = "3";
                    $tripmsg = "You have reached your location!!!!";
                    $tracking = 0;
                }
                $slat = $row1['blat'];
                $slong = $row1['blong'];
                $blat = $row1['LATITUDE'];
                $blong = $row1['LONGITUDE'];
                $sloc = $row1['bloc'];
                $eloc = $row1['ADDRESS'];
            }

            if ($tracking == 1) {
                $waypoints = '';
                $row2 = $dao->track2($rosterid, $cabid, floatval($order), $eid); //get all employee details  for particular traking roster id                   
                $r_order = 0;
                $sts = "";
                $rowcnt = count($row2);
                if ($rowcnt > 0) {
                    foreach ($row2 as $val) {
                        $otpsts = $val['ROSTER_PASSENGER_STATUS'];
                        $r_order = $val['ROUTE_ORDER'];
                        if ($trip_type == TRIP_P) {
                            if ($route_order > $r_order) {
                                $waypoints = $sts == "true" ? $val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|" : '';
                            } else {
                                $element_arr1[] = array('slat' => $slat, 'slong' => $slong, 'pickuptime' => $val['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'], AES_ENCRYPT_KEY),
                                    'sloc' => $sloc, 'eloc' => $eloc, 'empid' => $val['EMPLOYEE_ID'], 'route_order' => $val['ROUTE_ORDER'], 'elat' => $blat, 'elong' => $blong, 'trip_type' => $trip_type);
                                if (!in_array($otpsts, unserialize(RP_PICKUP_DROP_OTP))) {
                                    $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                                }
                                if ($eid == $val['EMPLOYEE_ID'] && in_array($otpsts, unserialize(RP_PICKUP_DROP_OTP))) {
                                    $sts = "true";
                                }
                            }
                        } else {
//echo "otp sts==".$otpsts;
                            $element_arr1[] = array('slat' => $slat, 'slong' => $slong, 'pickuptime' => $val['picktime'], 'empname' => $dao->AES_DECRYPT($val['NAME'], AES_ENCRYPT_KEY),
                                'sloc' => $sloc, 'eloc' => $eloc, 'empid' => $val['EMPLOYEE_ID'], 'route_order' => $val['ROUTE_ORDER'], 'elat' => $blat, 'elong' => $blong, 'trip_type' => $trip_type);
                            if (in_array($otpsts, unserialize(RP_CREATED))) {
                                $sts = "true";
                            } else if (in_array($otpsts, unserialize(RP_ARRIVED_BOARDED)) && !in_array($otpsts, unserialize(RP_PICKUP_DROP_OTP))) {
                                $waypoints.=$val['LATITUDE'] . ',' . $val['LONGITUDE'] . "|";
                            }
                        }
                    }
                } else {
                    $element_arr1[] = array('slat' => $slat, 'slong' => $slong, 'pickuptime' => $row1['picktime'], 'empname' => $dao->AES_DECRYPT($row1['NAME'], AES_ENCRYPT_KEY),
                        'sloc' => $sloc, 'eloc' => $eloc, 'empid' => $eid, 'elat' => $blat, 'elong' => $blong, 'trip_type' => $trip_type);
                    $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
                }
//                echo json_encode($element_arr1);
//                exit;
                $destination = $row1['LATITUDE'] . ',' . $row1['LONGITUDE'];
                $dlat = $row1['LATITUDE'];
                $dlong = $row1['LONGITUDE'];
                if ($gpsdate == "1900-01-01 00:00:00") {
                    $driverdet = $dao->get_driver_details($cabid);
                    if ($driverdet) {
                        $drivername = $driverdet['DRIVERS_NAME'];
                        $drivermobile = $driverdet['DRIVER_MOBILE'];
                        $vehno = $driverdet['VEHICLE_REG_NO'];
                        $driverimage = $driverdet['DRIVER_IMAGE'];
                        $vehmod = $driverdet['MODEL'];
                    }
                }
                //echo json_encode($element_arr1);
                $ret = $this->ShuttleElasticmdl->cabNavigation($cabid, '1900-01-01 00:00:00');
                $origin = $ret['RESULT'][0]['POSITION'];
                $spos = explode(',', $origin);
                $slat = $spos[0];
                $slong = $spos[1];

                $gps_currdate = $ret['RESULT'][0]['GPS_DATE'];
                // $driverimage = mysql_real_escape_string("https://mytransportportal.com/cog_track/img/driver_img/photo.jpg");

                if ($sts == "true") {
                    $waypoints.=$blat . ',' . $blong . "|";
                    $destination = $blat . ',' . $blong;
                    $dlat = $blat;
                    $dlong = $blong;
                }
                $km_deviation = 1.4;
                $time_deviation = 180;

                //google api is not working below code is used
//            $km = $this->distance($slat,$slong,$dlat,$dlong,'K');
//            $traveltime=$km*$km_deviation*$time_deviation;
//            $hours = floor($traveltime / 3600);
//            $mins = floor($traveltime / 60 % 60);
//            if ($hours == 0) {
//                $minutes = $mins . " mins";
//            } else {
//                $minutes = $hours . 'hrs -' . $mins . " mins";
//            }
                $km = $this->calkm($origin, $destination, $waypoints, 'both', 'driving');
                $x = explode("-", $km);
                if ($x[1] > 0) {
                    $hours = floor($x[1] / 3600);
                    $mins = floor($x[1] / 60 % 60);
                    if ($hours == 0) {
                        $minutes = $mins . " mins";
                    } else {
                        $minutes = $hours . 'hrs -' . $mins . " mins";
                    }
                } else {
                    $minutes = "NA";
                }
                $tsts_row = $dao->alert_signal_failure($rosterid, $cabid, $gps_currdate);
                if ($tsts_row) {
                    if ($tsts_row['tripsts'] == 'signalfailed') {
                        $tripmsg = "Alert signal failure";
                    }
                }
                $data[] = array('DriverName' => $drivername,
                    "DriverMobile" => $drivermobile,
                    "CabNo" => $vehno,
                    "cabLatLong" => $origin,
                    "TripStatus" => $tripsts,
                    "TripMsg" => $tripmsg,
                    "DriverImage" => $driverimage,
                    'CabModel' => $vehmod,
                    "ETA" => $minutes);
            }

            $element_arr = $this->ShuttleElasticmdl->cabNavigation($cabid, $gpsdate);
            //print_r($element_arr);
            $driver_arr = array();
            $gps_det = count($data) == 0 ? $driver_arr : $element_arr['RESULT'];
        }
        $element = array('DriverDetails' => $data, "details" => $gps_det, 'empdetails' => $element_arr1);
        return $element;
    }

    public function getlocation() {
        $tmp = array();
        $dao = new Shuttledao();
        $row = $dao->getcompany_location();
        foreach ($row as $arg) {
            $tmp[$arg['sitename']][] = array('site_location' => $arg['siteloc'], 'branch_name' => $arg['branchname'], 'branch_id' => $arg['branchid'], 'company_id' => $arg['COMPANY_ID']);
        }
        $output = array();
        foreach ($tmp as $type => $labels) {
            $output[] = array(
                'category' => $type,
                'sub_category' => $labels
            );
        }
        // print_r($output);
        //echo json_encode($output);
        return array('Apiresponse' => $output);
    }

    public function search_group($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        // $seat_count_time= date("H:i:s", strtotime('+2 hours', strtotime($curtime)));
        // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
//        print_r($pickarray);
//        print_r($droparray);
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            $return_route = 0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $return_route = $row['Return_Route_No'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                            $traveltime = $traveldist[1];
                            // echo $plat, $plong, $dlat, $dlong;
//                            $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                        } else {
                            // if($shedule_date==$curdate && $stime > $curtime)
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $sts = "";
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime = "CMonday";
                                    $sts = 'true';
                                } else {
                                    if ($route_name == TRIP_P) {
                                        if ($a_p_picktime <= $seat_count_time && $seats_used > 0) {
                                            $sts = 'true';
                                        } else if ($a_p_picktime > $seat_count_time && $seats_used >= 0) {
                                            $sts = 'true';
                                        } else {
                                            $sts = 'false';
                                        }
                                    } else {
                                        $sts = 'true';
                                    }
                                    $shuttletime = "ANext";
                                }
                                if ($sts == 'true') {
                                    $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                    $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                    $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                                
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                                }
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {

                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');                              
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $return_route = $row['Return_Route_No'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                            $traveltime = $traveldist[1];
//                            $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                            $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                            $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                            $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                            $traveltime =round($traveldist*$km_deviation*$time_deviation);
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                        } else {
                            if ($shedule_date == $curdate && $stime > $curtime) {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "ANext";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $traveldist[1];
//                                 $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $traveldist[1];
//                                $dist=$route_type=='Fixed'? 0.2:$this->distance($startlat, $startlong, $plat, $plong, 'K');
//                                $dist1=$route_type=='Fixed'? 0.2:$this->distance($endlat, $endlong, $dlat, $dlong, 'K');
//                                $traveldist=$this->distance($plat, $plong, $dlat, $dlong, 'K');
//                                $package = $this->tariff_cal(round($traveldist*$km_deviation), $ttype, $route_type,$vehicle_id, 0);
//                                $traveltime =round($traveldist*$km_deviation*$time_deviation);
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'return_route' => $return_route, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
//        $p_array = $this->msort('asc',$element_arr, array('route_date','approxtime','shuttle_time'));
//        $d_array = $this->msort('asc',$element_arr1, array('route_date','approxtime','shuttle_time'));
        $p_array = $this->msort('asc', $this->arraygroup($element_arr), array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $this->arraygroup($element_arr1), array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        // echo json_encode($this->arraygroup($p_array));
        return $element;
    }
    
    
    
    //stage wise search
    
    public function shuttle_stage_searchroute($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;      
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
        //print_r($pickarray);

        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d=$val['DROP_STOP_ID'];
                
                $p_route_order = $dao->get_route_order($route_id,$stop_id);
                $d_route_order = $dao->get_route_order($route_id,$stop_id_d);
                
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seats($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                       
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        
                        $seats=$this->check_seats_instages($p_route_order,$d_route_order,$trip_id,'check');
                        
                        if($seats=='true')
                            {
                                $comparetime = $curdate . " " . $stime;
                                $secs = strtotime($stime) - strtotime("00:00:00");
                                $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                                $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                       
                            if ($resheduledate != '0000-00-00') {
                                $shuttletime = "ANext";
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');                           
                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order);                                
                                $package = $this->tariff_cal(round($traveldist['dist']), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $this->TimeToSec($traveldist['time']);                          
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['dist']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id,'routeorder_p'=>$p_route_order,'routeorder_d'=>$d_route_order, 'package_details' => $package);
                            } else {                          
                                if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                    $sts = "";
                                    if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                        $weekday = date('l', strtotime($shedule_date));
                                        $shuttletime = "C".$weekday;                                 
                                        $sts = 'true';
                                    } else {
                                        if ($route_name == TRIP_P) {                                        
                                           $sts= ($a_p_picktime <= $seat_count_time && $seats_used > 0) ? 'true' : 
                                                   ($a_p_picktime > $seat_count_time && $seats_used >= 0) ? 'true' : 'false';                                      
                                        } else {
                                            $sts = 'true';
                                        }
                                        $shuttletime = "ANext";
                                    }
                                    if ($sts == 'true') {

                                        $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                        $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');                                     
                                        $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order);
                                        $package = $this->tariff_cal(round($traveldist['dist']), $ttype, $route_type, $vehicle_id, 0);
                                        $traveltime = $this->TimeToSec($traveldist['time']);
                                        $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['dist']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id,'routeorder_p'=>$p_route_order,'routeorder_d'=>$d_route_order, 'package_details' => $package);
                                    }
                                } else if ($shedule_date > $curdate) {
                                    if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                        $weekday = date('l', strtotime($shedule_date));
                                        $shuttletime = "C".$weekday;                                 
                                    } else {
                                        $shuttletime = "BTomorrow";
                                    }
                                    $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');                               
                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order);
                                    $package = $this->tariff_cal(round($traveldist['dist']), $ttype, $route_type, $vehicle_id, 0);
                                    $traveltime = $this->TimeToSec($traveldist['time']);
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['dist']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id,'routeorder_p'=>$p_route_order,'routeorder_d'=>$d_route_order, 'package_details' => $package);
                                }
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                $stop_id = $val['PICK_STOP_ID'];
                $stop_id_d=$val['DROP_STOP_ID'];
                
                $p_route_order = $dao->get_route_order($route_id,$stop_id);
                $d_route_order = $dao->get_route_order($route_id,$stop_id_d);
                
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    //$seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                     $seatavial = $dao->get_seats($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $seats=$this->check_seats_instages($p_route_order,$d_route_order,$trip_id,'check');                        
                        if($seats=='true')
                        {
                            $comparetime = $curdate . " " . $stime;
                            $secs = strtotime($stime) - strtotime("00:00:00");
                            $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);                          
                            if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                                $shuttletime = "ANext";
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                $package = $this->tariff_cal(round($traveldist['dist']), $ttype, $route_type, $vehicle_id, 0);
                                $traveltime = $this->TimeToSec($traveldist['time']);
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['dist']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id,'routeorder_p'=>$p_route_order,'routeorder_d'=>$d_route_order, 'package_details' => $package);
                            } else {
                                if ($shedule_date == $curdate && $stime > $curtime) {
                                    if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                        $weekday = date('l', strtotime($shedule_date));
                                        $shuttletime = "C".$weekday;                                  
                                    } else {
                                        $shuttletime = "ANext";
                                    }
                                    $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                    $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                    $package = $this->tariff_cal(round($traveldist['dist']), $ttype, $route_type, $vehicle_id, 0);
                                    $traveltime = $this->TimeToSec($traveldist['time']);                             
                                    $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['dist']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id,'routeorder_p'=>$p_route_order,'routeorder_d'=>$d_route_order, 'package_details' => $package);
                                } else if ($shedule_date > $curdate) {
                                    if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                        $weekday = date('l', strtotime($shedule_date));
                                        $shuttletime = "C".$weekday;                                   
                                    } else {
                                        $shuttletime = "BTomorrow";
                                    }
                                    $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                    $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                    $traveldist=$dao->calculate_distance($route_id, $p_route_order, $d_route_order); 
                                    $package = $this->tariff_cal(round($traveldist['dist']), $ttype, $route_type, $vehicle_id, 0);
                                    $traveltime = $this->TimeToSec($traveldist['time']);
                                    $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['dist']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id,'routeorder_p'=>$p_route_order,'routeorder_d'=>$d_route_order, 'package_details' => $package);
                                }
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }
    
    public function check_seats_instages($p_route_order,$d_route_order,$trip_id,$sts)
    {
        $dao = new Shuttledao();
        $i = 0; $route_diff = 0; $stage_count = 0;
        $oddNumbers=0;$evenNumbers=0;
        
        $output = array();
        $retarray=array();
        $ret="";   
        $output=$dao->update_stages($trip_id,$p_route_order,$d_route_order);
        $arr_size=count($output);
       
        $route_diff = $d_route_order - $p_route_order;
        if($arr_size>0 && isset($output['ret'])=='true')
        {
            $ret = "true";
            $retarray=array('status'=>1,'pickup_order'=>$p_route_order,'drop_order'=>$d_route_order,'stage_arr'=>$output);
        }
        else{   
            if ($arr_size == $route_diff) {               
                $ret = "true";
            }
            else{
                $ret="false";
            }
            
            
//            if ($arr_size >= $route_diff) {               
//                $ret = "true";
//            } else if ($arr_size <= ($route_diff - 1) && $arr_size > 0) { 
//                for ($j = 0; $j < $arr_size; $j++) {
//                    if (($output[$j]['stage_id'] % 2) == 1)
//                        $oddNumbers++;
//                    else
//                        $evenNumbers++;
//                }    
//                 $ret = (($d_route_order % 2) == 0 && ($p_route_order % 2) == 0)
//			      ? ($evenNumbers == intval((($d_route_order - $p_route_order) / 2) + 1)
//			        || $oddNumbers == intval(($d_route_order - $p_route_order + 1) / 2)) ? "true" : "false"
//			      : ($oddNumbers == intval((($d_route_order - $p_route_order) / 2) + 1)
//			        || $evenNumbers == intval(($d_route_order - $p_route_order + 1) / 2)) ? "true" : "false";  
//                
//            }
//            else {               
//                $ret = "false";
//            }
            
            $retarray=array('status'=>2,'pickup_order'=>$p_route_order,'drop_order'=>$d_route_order,'stage_arr'=>$output);
        }
        if($sts=='booking' && $ret=='true')
            return $retarray;
        else
            return $ret;

    }
    
     public function shuttle_stage_booking_insert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, $comp_id, $payment_category,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d,$p_request_date,$d_request_date) {
        $element_array = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $cur_time = $getdate['cur_date_time'];
        $subscribtionsts = 0;
       // $this->load->model('ShuttleElasticmdl');

        if ($ppoint == 'null' || $dpoint == 'null' || $plat == 0 || $plong == 0 || $dlat == 0 || $dlong == 0 || $ssotoken == 'null') {
            $element_array = array('status' => 0, 'message' => 'Subsciption Failed');
            return $element_array;
            exit;
        }
        if ($noofdays > 1) {
            $shuttledate = date('Y-m-d', strtotime('+1 day', strtotime($curdate)));
            $totdays=$noofdays-1;
            $enddate = date('Y-m-d', strtotime("+$totdays day", strtotime($shuttledate)));
            $where = "EmployeeId='$eid' and '$shuttledate' between StartDate and EndDate and PaymentStatus='S' and Active!=3";
            $row = $dao->c_selectrow('shuttle_booking', $where);
            if ($row) {
                $subscribtionsts = 1;
            } else {
                $subscribtionsts = 0;
            }
        } else {  
                if ($ttype == "B" ) {
                    $shuttledate = $p_request_date;
                    $enddate = $d_request_date;
                } else {
                    $shuttledate = $p_request_date;
                    $enddate = $p_request_date;
                }               
                $i=$dao->check_noservice_date($shuttledate);
                if($i==0){
                    $shuttledate =  date('Y-m-d', strtotime('+1 day', strtotime($shuttledate))); 
                    $enddate=$shuttledate;
                }
            
            if (date("w", strtotime($shuttledate)) == 6 || date("w", strtotime($shuttledate)) == 0 ) 
            {
                $element_array = array('status' => 0, 'message' => 'There is no shuttle service for this date');
                return $element_array;
                exit; 
            }
            $subscribtionsts = 0;
        }
        $message = $this->stage_seats_confirmed_sts($ttype,$tripid_p,$tripid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);       
        if ($message == "Confirmed") {
            if ($subscribtionsts == 0 || $subs_confirm_sts == 1) {
                $origin = $plat . "," . $plong;
                $destination = $dlat . "," . $dlong;        
                $km=$this->GetDrivingKm($origin, $destination);  
                $tdist=round($km['distance']);
               //$tdist = round($this->distance($plat,$plong,$dlat,$dlong,'K')*1.5);//without google key
                $totdays = 0;
                $tfare = 0;
                $validtrip = 0;
                //if (count($tariff) > 0 && $noofdays > 0) {                    
                if ($ttype == "B") {
                    $totdays = $noofdays;
                    $veh_id_p = $dao->getvehicleid($tripid_p);
                    $tariff_p = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_p, $noofdays);
                    $veh_id_d = $dao->getvehicleid($tripid_d);
                    $tariff_d = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_d, $noofdays);
                    if (count($tariff_p) > 0 && count($tariff_d) > 0) {
                        $tfare = $tariff_p[0]['fare'] + $tariff_d[0]['fare'];
                        $validtrip = $tariff_p[0]['validTrip'] * 2;
                        $totdist = $tdist * 2;
                        $tariff_id = $tariff_p[0]['tariff_id'];
                    }
                } else {
                    $totdays = $noofdays;
                    $veh_id_p = $dao->getvehicleid($tripid_p);
                    $tariff = $this->tariff_cal($tdist, $ttype, $route_type, $veh_id_p, $noofdays);
                    $tfare = $tariff[0]['fare'];
                    //$tfare = $tariff[0]['fare'];
                    $validtrip = $tariff[0]['validTrip'];
                    $totdist = $tdist;
                    $tariff_id = $tariff[0]['tariff_id'];
                }
                $data = array('BranchId' => $branch_id, 'EmployeeId' => $eid, 'PickupRosterId' => $tripid_p, 'DropRosterId' => $tripid_d, 'PickupPoint' => trim($ppoint), 'DropPoint' => trim($dpoint), 'TravelMode' => $ttype, 'StartTime' => $ptime, 'EndTime' => $dtime, 'PackageKm' => $totdist, 'PackageAmt' => $tfare, 'CreatedDatetime' => $cur_time,
                    'PickLatitude' => $plat, 'PickLongitude' => $plong, 'DropLatitude' => $dlat, 'DropLongitude' => $dlong, 'StartDate' => $shuttledate, 'EndDate' => $enddate, 'NoofDays' => $totdays, 'NoofRides' => $validtrip, 'TotalPaidAmt' => $tfare, 'RouteType' => $route_type, 'TariffId' => $tariff_id);
                $cnt = $dao->c_insert('shuttle_booking', $data);
                //echo $this->db->last_query();
                // exit;
                $id = $this->db->insert_id();
                if ($cnt > 0) {
                    $paymentno = $payment_category == "paytm" ? "P" . strtotime(date('YmdHis')) . $id : "J" . strtotime(date('YmdHis')) . $id;
                    $data = array('PaymentNo' => $paymentno);
                    $where = "Sno=$id";
                    $cnt1 = $dao->c_update('shuttle_booking', $data, $where);
                    if ($cnt1 > 0) {                      
                        if ($payment_category == "paytm" || empty($payment_category)) {
                            $response = $this->paytmwalletmoney_withdraw($paymentno, $ssotoken, $cust_id, $mobileno, $tfare, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
                            $element_array = array('status' => 1, 'message' => $response);
                        } else {
                            $response = $this->jiomoney_debitbalance($paymentno, $mobileno, $tfare, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
                            if (is_array($response)) {
                                $element_array = array('status' => 1, 'message' => 'success', 'jio_response' => $response);
                            } else {
                                $element_array = array('status' => 1, 'message' => $response);
                            }
                        }

                        // $response=$this->paytmwithdraw_response($paymentno, 0, 0, $mobileno,$stopid_p,$stopid_d);
                    }
                } else {
                    $element_array = array('status' => 0, 'message' => 'Please try again');
                }
            } else {
                $element_array = array('status' => 2, 'message' => 'Subscribtion already avilable for this date.Do you want to continue');
            }
        } else {
            $element_array = array('status' => 0, 'message' => $message);
        }
       // $this->ShuttleElasticmdl->shuttleBookingInsert($eid, $ppoint, $plat, $plong, $dpoint, $dlat, $dlong, $ttype, $ptime, $dtime, $tripid_p, $tripid_d, $noofdays, $ptag, $dtag, $route_type, $subs_confirm_sts, $ssotoken, $cust_id, $mobileno, $stopid_p, $stopid_d, $branch_id, json_encode($element_array));
        return $element_array;
    }
    public function stage_seats_confirmed_sts($ttype,$tripid_p,$tripid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        $dao = new Shuttledao();
        $avail_p = 'false';
        $avail_d = 'false';
        $message = "";
        if ($ttype == "B") {           
            $avail_p = $this->check_seats_instages($p_routeorder_p,$p_routeorder_d,$tripid_p,'check');
            $avail_d = $this->check_seats_instages($d_routeorder_p,$d_routeorder_d,$tripid_d,'check');
            if ($avail_p == 'true' && $avail_d == 'true') {
                $message = "Confirmed";
            } else if ($avail_p == 'true' && $avail_d == 'false') {
                $message = "Pickup routes only available";
            } else if ($avail_p == 'false' && $avail_d == 'true') {
                $message = "Return routes only available";
            } else {
                $message = "No seats available";
            }
        } else {
            $avail_p = $this->check_seats_instages($p_routeorder_p,$p_routeorder_d,$tripid_p,'check');           
            if ($avail_p == 'true') {
                $message = "Confirmed";
            } else {
                $message = "No seats avialable";
            }
        }
        return $message;
    }


    public function c_insert($tablename, $values) {
        $this->db->set($values);
        $this->db->insert($tablename, $this);
        if ($this->db->affected_rows() > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function c_update($tablename, $values, $where) {
        $where1 = $where;
        if ($where1 != '') {
            $this->db->where($where1);
            $this->db->update($tablename, $values);
            if ($this->db->affected_rows() > 0) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public function c_selectrow($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            return $row1;
        }
    }

    public function c_selectarray($tablename, $where) {
        $where2 = $where;
        $this->db->select('*');
        $this->db->from($tablename);
        $this->db->where($where2);
        $query1 = $this->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->result_array();
            return $row1;
        }
    }

    public function output($element_array, $ct) {
        if ($ct == "ip") {
            return json_encode($element_array);
        } else {
            $string = json_encode($element_array);
            $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
            $valreturn = array('xyssdff' => $returnval);
            return json_encode($valreturn);
        }
    }

    function encrypt($message, $initialVector, $secretKey) {
        return base64_encode(
                mcrypt_encrypt(
                        MCRYPT_RIJNDAEL_128, md5($secretKey), $message, MCRYPT_MODE_CFB, $initialVector
                )
        );
    }

    function decrypt($data, $key, $secretKey) {
        $decode = base64_decode($data);
        return mcrypt_decrypt(
                MCRYPT_RIJNDAEL_128, md5($key), $decode, MCRYPT_MODE_CFB, $secretKey
        );
    }

    function msort($sorder, $array, $key, $sort_flags = SORT_REGULAR) {
        if (is_array($array) && count($array) > 0) {
            if (!empty($key)) {
                $mapping = array();
                foreach ($array as $k => $v) {
                    $sort_key = '';
                    if (!is_array($key)) {
                        $sort_key = $v[$key];
                    } else {
                        // @TODO This should be fixed, now it will be sorted as string
                        foreach ($key as $key_key) {
                            $sort_key .= $v[$key_key];
                        }
                        $sort_flags = SORT_STRING;
                    }
                    $mapping[$k] = $sort_key;
                }
                if ($sorder == "asc") {
                    asort($mapping, $sort_flags);
                } else {
                    arsort($mapping, $sort_flags);
                }
                $sorted = array();
                foreach ($mapping as $k => $v) {
                    $sorted[] = $array[$k];
                }
                return $sorted;
            }
        }
        return $array;
    }

    private function record_sort($records, $field, $reverse = false) {
        $hash = array();

        foreach ($records as $record) {
            $hash[$record[$field]] = $record;
        }

        ($reverse) ? krsort($hash) : ksort($hash);

        $records = array();

        foreach ($hash as $record) {
            $records [] = $record;
        }

        return $records;
    }

    public function getrandomnumber() {
        $a = 0;
        for ($i = 0; $i < 6; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(100000, 999999);

        return $a;
    }

    public function getrandomnumber1() {
        $a = 0;
        for ($i = 0; $i < 4; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(1000, 9999);

        return $a;
    }

    function Encrypt_Script($string, $key) {
        $result = '';
        for ($i = 0; $i < strlen($string); $i++) {
            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) + ord($keychar));
            $result.=$char;
        }
        return base64_encode($result);
    }

    function Decrypt_Script($string, $key) {
        $result = '';
        //$string = base64_decode($string);

        for ($i = 0; $i < strlen($string); $i++) {

            $char = substr($string, $i, 1);
            $keychar = substr($key, ($i % strlen($key)) - 1, 1);
            $char = chr(ord($char) - ord($keychar));
            $result.=$char;
        }
        return $result;
    }

    function sec_to_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor($seconds % 3600 / 60);
        $seconds = $seconds % 60;

        return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
    }
    function TimeToSec($time) {
	$sec = 0;
	foreach (array_reverse(explode(':', $time)) as $k => $v) $sec += pow(60, $k) * $v;
	return $sec;
    }

    public function sendsms($mobile, $sms) {
        $msg = urlencode($sms);
        $urltopost = "http://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=ntltaxi1&send=TOPSCS&dest=$mobile&msg=" . $msg;
        $ch = curl_init($urltopost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //Sms_Response
        $returndata = curl_exec($ch);
        return $returndata;
    }

    function getAddress($latitude, $longitude) {
        if (!empty($latitude) && !empty($longitude)) {

            $url = $this->signUrl("http://maps.googleapis.com/maps/api/geocode/json?address=" . $latitude . "," . $longitude . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($ch);
            //echo $response;
            curl_close($ch);

            //  $geocodeFromLatLong = json_decode($response);

            $output = json_decode($response);
            $status = $output->status;
            //Get address from json data
            $address = ($status == "OK") ? $output->results[1]->formatted_address : '';
            //Return address of the given latitude and longitude
            if (!empty($address)) {
                return $address;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    function encodeBase64UrlSafe($value) {
        return str_replace(array('+', '/'), array('-', '_'), base64_encode($value));
    }

    function decodeBase64UrlSafe($value) {
        return base64_decode(str_replace(array('-', '_'), array('+', '/'), $value));
    }

    function signUrl($myUrlToSign, $privateKey) {
        // parse the url
        $url = parse_url($myUrlToSign);
        $urlPartToSign = $url['path'] . "?" . $url['query'];

        // Decode the private key into its binary format
        $decodedKey = $this->decodeBase64UrlSafe($privateKey);

        // Create a signature using the private key and the URL-encoded
        // string using HMAC SHA1. This signature will be binary.
        $signature = hash_hmac("sha1", $urlPartToSign, $decodedKey, true);

        $encodedSignature = $this->encodeBase64UrlSafe($signature);

        return $myUrlToSign . "&signature=" . $encodedSignature;
    }

    function calkm($origin, $destination, $waypoints, $type, $mode) {
        if ($mode == "walk") {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //$url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking";
        } else {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&units=metric&mode=driving&alternatives=true&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //$url= $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=driving&alternatives=true&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //  $url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false";
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        //echo $response;
        curl_close($ch);

        $data = json_decode($response);

        $km = 0;
        $tim = "";
        // If we got directions, output all of the HTML instructions
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                //foreach ($leg->steps as $step) {
                $i = 0;
                foreach ($leg->distance as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        $x = explode(" ", $val);
                        if ($x[1] == 'km') {
                            $km+=$x[0];
                        } else {
                            $km+=($x[0] * 0.001);
                        }
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        
                    } else {
                        $tim +=$val;
                    }
                }
            }
            if ($type == 'km') {
                return $km;
            } else {
                return $km . '-' . $tim;
            }
        }
    }

    public function getWorkingDays($startDate, $endDate) {
        $begin = strtotime($startDate);
        $end = strtotime($endDate);
        if ($begin > $end) {

            return 0;
        } else {
            $no_days = 0;
            while ($begin <= $end) {
                $what_day = date("N", $begin);
                if (!in_array($what_day, [6, 7])) // 6 and 7 are weekend
                    $no_days++;
                $begin += 86400; // +1 day
            };

            return $no_days;
        }
    }

    function distance($lat1, $lon1, $lat2, $lon2, $unit) {
        //  echo $lat1, $lon1, $lat2, $lon2, $unit;
        $rtn = 0;
        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit = strtoupper($unit);

        if ($unit == "K") {
            $rtn = $miles * 1.609344;
        } else if ($unit == "N") {
            $rtn = $miles * 0.8684;
        } else {
            $rtn = $miles;
        }
        if (is_nan($miles)) {
            return 0;
        } else {
            return round($rtn, 2);
        }
    }

    function arraygroup($data) {
        $final_arr = array();
        foreach ($data as $key => $value) {
            if (!array_key_exists($value['routeno'], $final_arr)) {
                $final_arr[$value['routeno']] = $value;
                unset($final_arr[$value['routeno']]['approxtime']);
                $final_arr[$value['routeno']]['approxtime'] = array();
                $time_array = array('time' => $value['approxtime'], 'roster_id' => $value['roster_id'], 'shuttle_time' => $value['shuttle_time']);
                array_push($final_arr[$value['routeno']]['approxtime'], $time_array);
            } else {
                $time_array = array('time' => $value['approxtime'], 'roster_id' => $value['roster_id'], 'shuttle_time' => $value['shuttle_time']);
                array_push($final_arr[$value['routeno']]['approxtime'], $time_array);
            }
        }
//       print_r($final_arr);
//       exit;
        return $final_arr;
    }

    function GetDrivingKm($lat, $long) {
        $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $lat . "&destination=" . $long . "&sensor=false&units=metric&mode=driving&alternatives=true&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPTSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPTSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        curl_close($ch);


        $data = json_decode($response);
        // print_r($data);
        $km = 0;
        $km1 = 0;
        $km2 = 0;
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {

                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km = $x[0];
                    } else {
                        $km = ($x[0] * 0.001);
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $tim = $val;
                }
            }

            $route = $data->routes[1];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {
                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km1 = $x[0];
                    } else {
                        $km1 = ($x[0] * 0.001);
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $tim1 = $val;
                }
            }

            $route = $data->routes[2];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {
                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km2 = $x[0];
                    } else {
                        $km2 = ($x[0] * 0.001);
                    }
                }

                foreach ($leg->duration as $key => $val) {
                    $tim2 = $val;
                }
            }
        }
//echo $km.'|'.$km1.'|'.$km2;
        if ($km2 == 0) {
            if ($km1 == 0) {
                $kms = $km;
            } else {
                $kms = min(array($km, $km1));
            }
        } else if ($km1 == 0) {
            $kms = $km;
        } else {
            $kms = min(array($km, $km1, $km2));
        }


        $time = 0;
        if ($kms == $km) {
            $time = $tim;
        }
        if ($kms == $km1) {
            $time = $tim1;
        }
        if ($kms == $km2) {
            $time = $tim2;
        }
        return array('distance' => $kms, 'time' => $time, 'time_mins' => $time);
    }

    function GetTransitKm($origin, $destination) {
        $url = $this->signUrl("https://maps.googleapis.com/maps/api/distancematrix/json?origins=" . $origin . "&destinations=" . $destination . "&sensor=false&units=metric&mode=transit&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
        $json = json_decode(file_get_contents($url, null), true);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPTSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPTSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        curl_close($ch);
        $data = json_decode($response);
//        print_r($json);
//        exit();
        return $data;
    }

    function jiomoney_registration($mobile_no, $fname, $lname) {
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "REGISTER_USER";
        $checksumSeed = JM_CHECKSUM_SEED;
        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('first_name' => $fname, 'last_name' => $lname, 'mobile_no' => $mobile_no, 'merchant_id' => $merchant_id);

        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'FirstName' => $fname, 'LastName' => $lname, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $dao->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_registration_otpverification($mobile_no, $otp) {
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "REGISTER_USER_VERIFY_OTP";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('mobile_no' => $mobile_no, 'otp' => $otp, 'merchant_id' => $merchant_id);
        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $dao->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_deregistration($mobile_no) {
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "DEREGISTER_USER";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('mobile_no' => $mobile_no, 'merchant_id' => $merchant_id);
        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $dao->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_checkbalance($mobile_no) {
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "FETCH_JM_BALANCE";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('mobile_no' => $mobile_no, 'merchant_id' => $merchant_id);
        $data = "$merchant_id|$api_name|$cur_datetime|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);

        $data_string = json_encode($request);
        $ch = curl_init(JM_REGISTRATION_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date, 'ResponseJson' => $result);
        $dao->c_insert('jiomoney_request', $data1);
        $ret = array("result" => (array) json_decode($result));
        return $ret;
    }

    function jiomoney_debitbalance($paymentno, $mobileno, $t_amount, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
       // echo "debit ";
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "DEBIT_JM_BALANCE";
        $tran_ref_no = $paymentno;
        $txn_amount = number_format($t_amount, 2);
        $capture_flag = 'N';
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('merchant_id' => $merchant_id, 'tran_ref_no' => $tran_ref_no, 'txn_amount' => $txn_amount, 'capture_flag' => $capture_flag, 'mobile_no' => $mobileno);

        $auth_jm_tran_ref_no = "";
        $auth_code = "";
        $auth_timestamp = "";
        $product_description = "";
        $udf1 = "";
        $udf2 = "";
        $udf3 = "";
        $udf4 = "";
        $udf5 = "";

        $data = "$merchant_id|$api_name|$cur_datetime|$tran_ref_no|$txn_amount|$auth_jm_tran_ref_no|$auth_code|$auth_timestamp|$capture_flag|$mobileno|$product_description|$udf1|$udf2|$udf3|$udf4|$udf5";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);
        $data_string = json_encode($request);
        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobileno, 'TransRefNo' => $tran_ref_no, 'TxnAmount' => $txn_amount, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobileno, 'CreatedDatetime' => $cur_date);
        $dao->c_insert('jiomoney_request', $data1);

        $ch = curl_init(JM_BALANCE_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
       // exit;
        $ret = array("result" => (array) json_decode($result));
        
        $data_update=array('ResponseJson'=>$result);
        $where="ApiName='$api_name' and TransRefNo='$tran_ref_no'";
        $dao->c_update('jiomoney_request', $data_update, $where);

        $retapi_name = $ret['result']['response']->response_header->api_name;
        $api_sts = $ret['result']['response']->response_header->api_status;
        $api_msg = $ret['result']['response']->response_header->api_msg;
        $timestamp = $ret['result']['response']->response_header->timestamp;
        $ret_trans_ref_no = $ret['result']['response']->payload_data->tran_ref_no;
        $jm_tran_ref_no = $ret['result']['response']->payload_data->jm_tran_ref_no;
        $txn_sts = $ret['result']['response']->payload_data->txn_status;
        $error_code = $ret['result']['response']->payload_data->error_code;
        $rettxn_amount = $ret['result']['response']->payload_data->txn_amount;
        $retmerchant_id = $ret['result']['response']->payload_data->merchant_id;
        $ret_checksum = $ret['result']['response']->checksum;

        $data2 = array('ApiName' => $retapi_name, 'ApiStatus' => $api_sts, 'ApiMessage' => $api_msg, 'TimeStamp' => $timestamp, 'CheckSum' => $ret_checksum, 'ErrorCode' => $error_code,
            'TransRefNo' => $ret_trans_ref_no, 'JmTransRefNo' => $jm_tran_ref_no, 'TxnAmount' => $rettxn_amount, 'TxnStatus' => $txn_sts, 'CreatedDatetime' => $cur_date, 'CreatedBy' => $mobileno);
        $dao->c_insert('jiomoney_response', $data2);

        //if($api_sts=="1" && $api_msg=="Approved" && $txn_sts=="SUCCESS" && $error_code=="000")
        if ($api_sts == "1" && $txn_sts == "SUCCESS" && $error_code == "000") {
            $data3 = array('ResponseMessage' => $api_msg, 'CheckSumHash' => $ret_checksum, 'ResponseCode' => $error_code,
                'OrderId' => $ret_trans_ref_no, 'TxnId' => $jm_tran_ref_no, 'TxnAmount' => $rettxn_amount, 'Status' => $txn_sts,
                'CreatedTime' => $cur_date, 'CreatedBy' => $mobileno, 'PaymentMode' => 'JIO');
            $dao->c_insert('shuttle_payment_response', $data3);
            $ret = $this->jiomoney_shuttlebooking($paymentno, $jm_tran_ref_no, $mobileno, $t_amount, $stopid_p, $stopid_d, $txn_sts,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
            $element_array = array('status' => 1, 'message' => $ret);
            return $element_array;
        } else if ($api_sts == "0" && $txn_sts == 'FAILED' && $error_code == "110") {
            $this->jiomoney_shuttlebooking($paymentno, $jm_tran_ref_no, $mobileno, $t_amount, $stopid_p, $stopid_d, $txn_sts,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
            $ret = "Payment Failed";
            $element_array = array('status' => 0, 'message' => $ret);
            return $element_array;
        } else {
            $arr = array('stopid_p' => $stopid_p, 'stopid_d' => $stopid_d);
            return array_merge($ret, $arr);
        }
    }

    function jiomoney_debitbalanceotp($tran_ref_no, $otp, $mobile_no, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        ///tran_ref_no ==>debit balance response (tran_ref_no) value                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "DEBIT_JM_BALANCE_VERIFY_OTP";
        $product_description = "withdraw";
        $udf1 = "123";
        $udf2 = "123";
        $udf3 = "123";
        $udf4 = "123";
        $udf5 = "123";
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime);
        $payload_data = array('merchant_id' => $merchant_id, 'tran_ref_no' => $tran_ref_no, 'otp' => $otp, 'mobile_no' => $mobile_no, 'product_description' => $product_description, 'udf1' => $udf1, 'udf2' => $udf2, 'udf3' => $udf3, 'udf4' => $udf4, 'udf5' => $udf5);

        $data = "$merchant_id|$api_name|$cur_datetime|$tran_ref_no|$otp|$mobile_no|$product_description|$udf1|$udf2|$udf3|$udf4|$udf5";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);
        $data_string = json_encode($request);

        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'TransRefNo' => $tran_ref_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date);
        $dao->c_insert('jiomoney_request', $data1);
        $ch = curl_init(JM_BALANCE_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 3.0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $ret = array("result" => (array) json_decode($result));
        
        $data_update=array('ResponseJson'=>$result);
        $where="ApiName='$api_name' and TransRefNo='$tran_ref_no'";
        $dao->c_update('jiomoney_request', $data_update, $where);

        $retapi_name = $ret['result']['response']->response_header->api_name;
        $api_sts = $ret['result']['response']->response_header->api_status;
        $api_msg = $ret['result']['response']->response_header->api_msg;
        $timestamp = $ret['result']['response']->response_header->timestamp;
        $ret_trans_ref_no = $ret['result']['response']->payload_data->tran_ref_no;
        $jm_tran_ref_no = $ret['result']['response']->payload_data->jm_tran_ref_no;
        $txn_sts = $ret['result']['response']->payload_data->txn_status;
        $error_code = $ret['result']['response']->payload_data->error_code;
        $rettxn_amount = $ret['result']['response']->payload_data->txn_amount;
        $ret_checksum = $ret['result']['response']->checksum;

        $data2 = array('ApiName' => $retapi_name, 'ApiStatus' => $api_sts, 'ApiMessage' => $api_msg, 'TimeStamp' => $timestamp, 'CheckSum' => $ret_checksum, 'ErrorCode' => $error_code,
            'TransRefNo' => $ret_trans_ref_no, 'JmTransRefNo' => $jm_tran_ref_no, 'TxnAmount' => $rettxn_amount, 'TxnStatus' => $txn_sts, 'CreatedDatetime' => $cur_date, 'CreatedBy' => $mobile_no);
        $dao->c_insert('jiomoney_response', $data2);
        // if($api_sts=="1" && $api_msg=="Payment successful" && $txn_sts=="SUCCESS" && $error_code=="000")
        if ($api_sts == "1" && $txn_sts == "SUCCESS" && $error_code == "000") {
            $data3 = array('ResponseMessage' => $api_msg, 'CheckSumHash' => $ret_checksum, 'ResponseCode' => $error_code,
                'OrderId' => $ret_trans_ref_no, 'TxnId' => $jm_tran_ref_no, 'TxnAmount' => $rettxn_amount, 'Status' => $txn_sts,
                'CreatedTime' => $cur_date, 'CreatedBy' => $mobile_no, 'PaymentMode' => 'JIO', 'TxnTimeStamp' => $timestamp);
            $dao->c_insert('shuttle_payment_response', $data3);
            $ret = $this->jiomoney_shuttlebooking($tran_ref_no, $jm_tran_ref_no, $mobile_no, $rettxn_amount, $stopid_p, $stopid_d, $txn_sts,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
            $element_array = array('status' => 1, 'message' => $ret);
            return $element_array;
        } else if ($api_sts == "0" && $txn_sts == 'FAILED' && $error_code == "110") {
            $this->jiomoney_shuttlebooking($tran_ref_no, $jm_tran_ref_no, $mobile_no, $rettxn_amount, $stopid_p, $stopid_d, $txn_sts,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
            $ret = "Payment Failed";
            $element_array = array('status' => 0, 'message' => $ret);
            return $element_array;
        } else {
            // $this->jiomoney_shuttlebooking($tran_ref_no,$jm_tran_ref_no,$mobile_no,$rettxn_amount,$stopid_p,$stopid_d,$txn_sts);
            $arr = array('stopid_p' => $stopid_p, 'stopid_d' => $stopid_d, 'status' => 0);
            return array_merge($ret, $arr);
        }
    }

    function jiomoney_refund($orderid, $txnid, $refundfare, $mobileno, $txnTimeStamp) {
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $merchant_id = JM_MERCHANT_ID;
        $api_name = "REFUND";
        $txn_amount = number_format($refundfare, 2);
        $additional_info = "NA";
        $org_txn_timestamp = $txnTimeStamp; //debit balance verify otp response(timestamp) value
        $org_jm_tran_ref_no = $txnid; //debit balance verify otp response (jm_tran_ref_no) value
        $tran_ref_no = "NTL" . rand(111111, 999999); //generate new trans no
        $checksumSeed = JM_CHECKSUM_SEED;

        $request_header = array("api_name" => $api_name, "timestamp" => $cur_datetime, 'version' => '3.0');
        $payload_data = array('org_txn_timestamp' => $org_txn_timestamp, 'additional_info' => $additional_info, 'org_jm_tran_ref_no' => $org_jm_tran_ref_no, 'merchant_id' => $merchant_id,
            'txn_amount' => $txn_amount, 'tran_ref_no' => $tran_ref_no);
        $data = "$merchant_id|$api_name|$cur_datetime|$tran_ref_no|$txn_amount|$org_jm_tran_ref_no|$org_txn_timestamp|$additional_info";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);

        $r_h = array('request_header' => $request_header, 'payload_data' => $payload_data, 'checksum' => $checksum);
        $request = array('request' => $r_h);
        $data_string = json_encode($request);

        $data1 = array('ApiName' => $api_name, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobileno, 'TransRefNo' => $tran_ref_no, 'CheckSum' => $checksum,
            'RequestJson' => $data_string, 'CreatedBy' => $mobileno, 'CreatedDatetime' => $cur_date);
        $dao->c_insert('jiomoney_request', $data1);

        $ch = curl_init(JM_BALANCE_URL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        //curl_setopt($ch, CURLOPT_VERBOSE, 3.0);  
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Accept: application/json', 'APIVer: 3.0'));
        $result = curl_exec($ch);
        $ret = array("result" => (array) json_decode($result));
        
        $data_update=array('ResponseJson'=>$result);
        $where="ApiName='$api_name' and TransRefNo='$tran_ref_no'";
        $dao->c_update('jiomoney_request', $data_update, $where);

        $retapi_name = $ret['result']['response']->response_header->api_name;
        $api_sts = $ret['result']['response']->response_header->api_status;
        $api_msg = $ret['result']['response']->response_header->api_msg;
        $timestamp = $ret['result']['response']->response_header->timestamp;
        $ret_trans_ref_no = $ret['result']['response']->payload_data->tran_ref_no;
        $jm_tran_ref_no = $ret['result']['response']->payload_data->jm_tran_ref_no;
        $txn_sts = $ret['result']['response']->payload_data->txn_status;
        $error_code = $ret['result']['response']->payload_data->error_code;
        $rettxn_amount = $ret['result']['response']->payload_data->txn_amount;
        $ret_checksum = $ret['result']['response']->checksum;

        $data2 = array('ApiName' => $retapi_name, 'ApiStatus' => $api_sts, 'ApiMessage' => $api_msg, 'TimeStamp' => $timestamp, 'CheckSum' => $ret_checksum, 'ErrorCode' => $error_code,
            'TransRefNo' => $ret_trans_ref_no, 'JmTransRefNo' => $jm_tran_ref_no, 'TxnAmount' => $rettxn_amount, 'TxnStatus' => $txn_sts, 'CreatedDatetime' => $cur_date, 'CreatedBy' => $mobileno);
        $dao->c_insert('jiomoney_response', $data2);

        if ($api_sts == "1" && $txn_sts == "SUCCESS" && $error_code == "000") {
            $respdata = array('OrderId' => $orderid, 'TxnId' => $jm_tran_ref_no, 'RefundAmount' => $rettxn_amount,
                'ResponseCode' => $error_code, 'ResponseMessage' => $api_msg, 'Status' => $txn_sts, 'RefundId' => $ret_trans_ref_no,
                'CreatedTime' => $cur_date, 'CreatedBy' => $mobileno, 'PaymentMode' => 'JIO');
            $dao->c_insert('shuttle_payment_response', $respdata);
            $where = "PaymentNo='$orderid'";
            $this->db->set("RefundAmt", "if(RefundAmt is null," . $rettxn_amount . ",RefundAmt+" . $rettxn_amount . ")", FALSE);
            $this->db->set("RefundReferanceId", "if(RefundReferanceId is null,'" . $ret_trans_ref_no . "',CONCAT(RefundReferanceId,',','" . $ret_trans_ref_no . "'))", FALSE);
            $this->db->set("Active", "if(NoofRides-NoofCancelRides=1,3,2)", FALSE);
            $this->db->set("NoofCancelRides", "NoofCancelRides+1", FALSE);
            $this->db->set("UpdatedDatetime", $cur_date);
            $this->db->where($where);
            $this->db->update('shuttle_booking');
        }
    }

    public function jiomoney_add($mobile_no, $transaction_refno, $txn_amount) {
        $dao = new Shuttledao();
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('YmdHis');
        $cur_date = date('Y-m-d H:i:s');
        $clientid = JM_CLIENT_ID;
        $merchantid = JM_MERCHANT_ID;
        $checksumSeed = JM_CHECKSUM_SEED;
        $returl = JM_RETURN_URL;

        $transaction_timestamp = $cur_datetime;
        $transaction_txntype = "LOADMONEY";
        $transaction_currency = 'INR';
        $channel = "MOBILE"; //WEB
        $version = 2.0;
        $token = "";

        $data = "$clientid|$txn_amount|$transaction_refno|$channel|$merchantid|$token|$returl|$transaction_timestamp|$transaction_txntype|$mobile_no";
        $checksum = hash_hmac('SHA256', $data, $checksumSeed);
        $ret = array('merchantid' => $merchantid, 'clientid' => $clientid, 'channel' => $channel, 'returl' => $returl, 'checksum' => $checksum,
            'token' => $token, 'timestamp' => $cur_datetime, 'txntype' => $transaction_txntype, 'currency' => $transaction_currency, 'version' => number_format($version, 1));
        $data1 = array('ApiName' => LOADMONEY, 'Timestamp' => $cur_datetime, 'MobileNo' => $mobile_no, 'TransRefNo' => $transaction_refno, 'CheckSum' => $checksum,
            'RequestJson' => json_encode($ret), 'CreatedBy' => $mobile_no, 'CreatedDatetime' => $cur_date);
        $dao->c_insert('jiomoney_request', $data1);
        return $ret;
    }

    function jiomoney_shuttlebooking($paymentno, $jm_tran_ref_no, $mobileno, $t_amount, $stopid_p, $stopid_d, $txn_sts,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d) {
        $response = "";
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        if ($txn_sts == "SUCCESS") {
            $where = "PaymentNo='$paymentno'";
            $data1 = array('PaymentStatus' => 'S', 'UpdatedDatetime' => $curdatetime);
            $dao->c_update('shuttle_booking', $data1, $where);
            $response = $this->paytmwithdraw_response($paymentno, $jm_tran_ref_no, $t_amount, $mobileno, $stopid_p, $stopid_d,$p_routeorder_p,$p_routeorder_d,$d_routeorder_p,$d_routeorder_d);
        } else {
            $where = "PaymentNo='$paymentno'";
            $data1 = array('PaymentStatus' => 'F', 'UpdatedDatetime' => $curdatetime);
            $dao->c_update('shuttle_booking', $data1, $where);
            $response = "Payment Failed";
            //$response = $this->paytmwithdraw_response($paymentno, $jm_tran_ref_no, $t_amount, $mobileno,$stopid_p,$stopid_d); 
        }
        return $response;
    }

    public function shuttle_registraton_new($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct, $branch_id, $comp_id, $comp_name) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        try {
            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $element_array = array();
            $ENC_EMAIL = $dao->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "(EMAIL = '" . $ENC_EMAIL . "' || MOBILE = '" . $ENC_MOBILE . "') AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row = $this->c_selectrow('employees', $where);
//            echo $this->db->last_query();
//            print_r($row);
//            echo "ename==".$ename=$dao->AES_DECRYPT($row['NAME'], AES_ENCRYPT_KEY);
//            exit;
            if ($row) {
                $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
            } else {
                //$eid = rand(10000, 100000);
                $eid=mt_rand(100000,999999);
                if ($comp_name != "0") {
                    $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdatetime);
                    $this->c_insert('shuttle_company_master', $data1);
                    $comp_id = $this->db->insert_id();
                }

                $ENC_NAME = $dao->AES_ENCRYPT($name, AES_ENCRYPT_KEY);
                $data = array('BRANCH_ID' => $branch_id, 'SHUTTLE_COMPANY_ID' => $comp_id, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                    'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
                $cnt = $this->c_insert('employees', $data);
                if ($cnt > 0) {
                    $message = "Thank you for registering with " . SMS_TITLE . ".enjoy the ride!";
                    $dao->insert_sms($branch_id, 'TOPSCS', $mobile, $message);
                    $data1 = array('branch_id' => $branch_id, 'shuttle_company_id' => $comp_id, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                    $this->c_insert('shuttle_notification', $data1);
                    $element_array = array('status' => 1, 'Message' => 'Successfully registered');
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Registration failed');
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

    public function profile_update_new($emergencyno, $homeaddr, $homelat, $homelong, $eid, $officeaddr, $officelat, $officelong, $branch_id, $comp_id, $comp_name, $auto_id, $mobileno) {
        $data1 = array();
        $data2 = array();
        $data3 = array();
        $element_array = array();
        $data4 = array();
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        if ($comp_name != "0") {
            $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdate_time);
            $this->c_insert('shuttle_company_master', $data1);
            $comp_id = $this->db->insert_id();
            //$data6 = array('SHUTTLE_COMPANY_ID' => $comp_id);
        }

        if ($emergencyno != "0") {
            $data2 = array('EMERGENCY_CONTACT_NO' => $dao->AES_ENCRYPT($emergencyno, AES_ENCRYPT_KEY));
        }
        if ($homeaddr != "0") {
            $data3 = array('ADDRESS' => $homeaddr, 'LATITUDE' => $homelat, 'LONGITUDE' => $homelong);
        }
        $data4 = array('UPDATED_BY' => $eid, 'updated_at' => $curdate_time, 'SHUTTLE_COMPANY_ID' => $comp_id);
        $data5 = array_merge($data2, $data3, $data4);
        //$where = "EMPLOYEES_ID='$eid' and ACTIVE='1' and CATEGORY='Shuttle' and BRANCH_ID='$branch_id' and SHUTTLE_COMPANY_ID='$comp_id'";
        // $where="id='$auto_id'";
        $ENC_MOBILE = $dao->AES_ENCRYPT($mobileno, AES_ENCRYPT_KEY);
        $where = "MOBILE = '" . $ENC_MOBILE . "' and ACTIVE='1' and CATEGORY='Shuttle'";
        $cnt = $dao->c_update('employees', $data5, $where);
        //echo $this->db->last_query();
//        exit;
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True", 'company_id' => $comp_id);
        } else {
            $element_array = array('status' => 1, 'Message' => "Please try again");
        }
        return $element_array;
    }
    
    public function update_rating($auto_id,$rating_sts)
    {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate_time = $getdate['cur_date_time'];
        $element_array=array();
        
        $data = array('RATE_STATUS' => $rating_sts,'RATE_DATE' => $curdate_time);
        $where = "id='$auto_id'";
        $cnt = $dao->c_update('employees', $data, $where);
        if ($cnt > 0) {
            $element_array = array('status' => 1, 'Message' => "True");
        } else {
            $element_array = array('status' => 0, 'Message' => "Please try again");
        }
        return $element_array;
    }
    
    //tariff for corporate    
    
    public function shuttle_searchroute_corp($startlat, $startlong, $endlat, $endlong, $ttype, $requiredtime, $returntime, $resheduledate, $reshedulderoute, $branchid, $route_type, $emp_id, $shuttle_category) {
        $dao = new Shuttledao();
        $this->load->model('ShuttleElasticmdl');
        $getdate = $dao->get_datetime();
        $curdate = $getdate['cur_date'];
        $curtime = $getdate['cur_time'];
        $curdatetime = $getdate['cur_date_time'];
        $shuttletime = "";
        $km_deviation = 1.5;
        $time_deviation = 180;
        $dist = 0;
        $dist1 = 0;
        // $seat_count_time= date("H:i:s", strtotime('+2 hours', strtotime($curtime)));
        // echo "date==".$resheduledate;
        if ($ttype == "Twoway") {
            $pickarray1 = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, $branchid, 'D', $route_type, $resheduledate, $reshedulderoute);
            $droparray1 = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, $branchid, 'P', $route_type, $resheduledate, $reshedulderoute);
        }
        $pickarray = $this->ShuttleElasticmdl->get_searchgroup($startlat, $startlong, 1, 'P', $route_type, $resheduledate, $reshedulderoute);
        $droparray = $this->ShuttleElasticmdl->get_searchgroup($endlat, $endlong, 1, 'D', $route_type, $resheduledate, $reshedulderoute);
        $output = array();
        $output1 = array();
        $element_arr = array();
        $element_arr1 = array();
        $package = array();
        $element = array();
        $p_array = array();
        $d_array = array();
        
        if (count($pickarray > 0) && count($droparray) > 0) {
            $arrayAB = array_merge($pickarray, $droparray);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output[$id])) {
                    $output[$id] = array();
                }
                $output[$id] = array_merge($output[$id], $value);
            }           
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            $seats_used = 0;
            foreach ($output as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];               
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {                   
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);                    
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        $seats_used = $row['Seats_Used'];
                        $route_name = $row['Route_Name'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        $seat_count_time = date("H:i:s", strtotime('+1 hours', strtotime($curtime)));
                        if ($resheduledate != '0000-00-00') {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                            $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal_corp(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                            $traveltime = $traveldist[1];                          
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                        } else {
                            // if($shedule_date==$curdate && $stime > $curtime)
                            if ($shedule_date == $curdate && $a_p_picktime > $curtime) {
                                $sts = "";
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                    $sts = 'true';
                                } else {
                                    if ($route_name == TRIP_P) {
                                        if ($a_p_picktime <= $seat_count_time && $seats_used > 0) {
                                            $sts = 'true';
                                        } else if ($a_p_picktime > $seat_count_time && $seats_used >= 0) {
                                            $sts = 'true';
                                        } else {
                                            $sts = 'false';
                                        }
                                    } else {
                                        $sts = 'true';
                                    }
                                    $shuttletime = "ANext";
                                }
                                if ($sts == 'true') {

                                    $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                    $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                    $traveldist = $this->GetDrivingKm($origin, $destination);
                                    $package = $this->tariff_cal_corp(round($traveldist['distance']), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                    $traveltime = $traveldist['time'];
                                    $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                                }
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($startlat . ',' . $startlong, $origin, $startlat . ',' . $startlong, 'km', 'walk');
                                $dist1 = $this->calkm($endlat . ',' . $endlong, $destination, $endlat . ',' . $endlong, 'km', 'walk');
                                $traveldist = $this->GetDrivingKm($origin, $destination);
                                // print_r($traveldist);
                                $package = $this->tariff_cal_corp(round($traveldist['distance']), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                $traveltime = $traveldist['time'];
                                $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist['distance']), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        if (count($pickarray1) > 0 && count($droparray1) > 0) {
            $arrayAB = array_merge($pickarray1, $droparray1);
            foreach ($arrayAB as $value) {
                $id = $value['ROUTE_ID'];
                if (!isset($output1[$id])) {
                    $output1[$id] = array();
                }
                $output1[$id] = array_merge($output1[$id], $value);
            }
            $spos = "";
            $dpos = "";
            $p_time = "";
            $d_time = "";
            foreach ($output1 as $val) {
                $p_time = $this->sec_to_time($val['APROX_PICK']);
                $d_time = $this->sec_to_time($val['APROX_DROP']);
                $plandmark = $val['PICK_LOCATION'];
                $dlandmark = $val['DROP_LOCATION'];
                $route_id = $val['ROUTE_ID'];
                if (date('H:i:s', strtotime($p_time)) < date('H:i:s', strtotime($d_time)) && $plandmark != '' && $dlandmark != '') {
                    $seatavial = $dao->get_seatavailability($route_id, $resheduledate);
                    foreach ($seatavial as $row) {
                        $trip_id = $row['Trip_ID'];
                        $vehicle_id = $row['Vehicle_ID'];
                        $shedule_date = $row['Schedule_Date'];
                        $route_name = $row['Route_Name'];
                        $a_pick = $val['APROX_PICK'];
                        $origin = $val['PICK_POSITION'];
                        $destination = $val['DROP_POSITION'];
                        $pick_date = $val['PICK_DATE'];
                        $drop_date = $val['DROP_DATE'];
                        $stop_id = $val['PICK_STOP_ID'];
                        //$stop_id_d=$val['DROP_STOP_ID'];
                        $spos = explode(',', $origin);
                        $dpos = explode(',', $destination);
                        $plat = $spos[0];
                        $plong = $spos[1];
                        $dlat = $dpos[0];
                        $dlong = $dpos[1];
                        $stime = $row['Schedule_Time'];
                        $comparetime = $curdate . " " . $stime;
                        $secs = strtotime($stime) - strtotime("00:00:00");
                        $a_p_picktime = date("H:i:s", strtotime($p_time) + $secs);
                        if ($resheduledate != '0000-00-00' && $stime > $curtime) {
                            $shuttletime = "ANext";
                            $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                            $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                            $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                            $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                            $traveltime = $traveldist[1];
                            $element_arr[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                        } else {
                            if ($shedule_date == $curdate && $stime > $curtime) {
                                if (date("w", strtotime($shedule_date)) == 6 || date("w", strtotime($shedule_date)) == 0) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "ANext";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                $traveltime = $traveldist[1];                         
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            } else if ($shedule_date > $curdate) {
                                if (date("w", strtotime($curdate)) == 6 || date("w", strtotime($curdate)) == 5) {
                                    $weekday = date('l', strtotime($shedule_date));
                                    $shuttletime = "C".$weekday;
                                   // $shuttletime = "CMonday";
                                } else {
                                    $shuttletime = "BTomorrow";
                                }
                                $dist = $this->calkm($endlat . ',' . $endlong, $origin, $endlat . ',' . $endlong, 'km', 'walk');
                                $dist1 = $this->calkm($startlat . ',' . $startlong, $destination, $startlat . ',' . $startlong, 'km', 'walk');
                                $traveldist = explode('-', $this->calkm($origin, $destination, $origin, 'both', 'driving'));
                                $package = $this->tariff_cal(round($traveldist[0]), $ttype, $route_type, $vehicle_id, 0,$branchid);
                                $traveltime = $traveldist[1];
                                $element_arr1[] = array('routeno' => $route_id, 'roster_id' => $trip_id, 'pick_date' => $pick_date, 'drop_date' => $drop_date, 'route_date' => $shedule_date, 'dist' => $dist, 'd_dist' => $dist1, 'stime_secs' => $a_pick, 'approxtime' => $a_p_picktime, 'approxdroptime' => $val['APROX_DROP'], 'pickuppoint' => $plandmark, 'plat' => $plat, 'plong' => $plong, 'droppoint' => $dlandmark, 'dlat' => $dlat, 'dlong' => $dlong, 'travel_dist' => round($traveldist[0]), 'travel_time' => $traveltime, 'shuttle_time' => $shuttletime, 'buffer_time' => BUFFER_TIME, 'stop_id' => $stop_id, 'package_details' => $package);
                            }
                        }
                    }
                }
            }
        }
        $p_array = $this->msort('asc', $element_arr, array('route_date', 'approxtime', 'shuttle_time'));
        $d_array = $this->msort('asc', $element_arr1, array('route_date', 'approxtime', 'shuttle_time'));
//        print_r($p_array);
//        print_r($d_array);
        if ($ttype == "Twoway") {
            if (count($element_arr) > 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) > 0 && count($element_arr1) == 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) > 0) {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array, 'dropsearch' => $d_array);
            } else if (count($element_arr) == 0 && count($element_arr1) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            }
        } else {
            if (count($element_arr) == 0) {
                $this->ShuttleElasticmdl->shuttleUnavailableInsert($startlat, $startlong, $endlat, $endlong, $requiredtime, $returntime
                        , $ttype, $resheduledate, $searchtype, $emp_id, $branchid, $route_type);
                $element = array('status' => 0, 'Message' => 'No routes');
            } else {
                $element = array('status' => 1, 'Message' => 'success', 'pickupsearch' => $p_array);
            }
        }
        //echo json_encode($element);
        return $element;
    }
    
    public function tariff_cal_corp($dist, $ttype, $tariff_type, $vehicle_id, $noofdays,$branch_id) {
        if ($dist == 0) {
            $dist = 1;
        }
        $condition = "and";
        if ($noofdays > 0) {
            $condition = "and NumberofDays='$noofdays' and ";
        }
        $oneday_fixed_price = 0;
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdate= $getdate['cur_date'];
        $curday=intval(date('d',strtotime($curdate)));
        
        // echo $dist, $ttype, $tariff_type,$noofdays;
        try {
            $element_array = array();
            if (strtolower($tariff_type) == strtolower("Km")) {

                $dist=4;
                $where = " BranchId='$branch_id' and Active ='1' and  '$dist'  "
                        . "BETWEEN Tariff_St_Km AND Tariff_Cl_Km and Tariff_Type='$tariff_type' "
                        . " and VehicleId='$vehicle_id' "
                        . " $condition if(`Start_Date` != '0' , $curday between Start_Date and End_Date  
                             ,`Start_Date` = 0 and End_Date=0
                            )  order by NumberofDays";
                $row = $this->c_selectarray('shuttle_tariff', $where);               
                foreach ($row as $val) {
                    $tariffId = $val['TariffId'];
                    $validDays = $val['NumberofDays'];
                    $validTrip = $val['NumberofTrips'];
                    $baseKm = $val['Tariff_Base_Km'];
                    $basCost = $val['Tariff_Base_Cost'];
                    $extKmCost = $val['Tariff_Ext_Km_Cost'];
                    $flatCost = $val['Tariff_Flat_Cost'];
                    $percentage = $val['Percentage'];
                    $oneday_fixed_price = $val['Oneday_Fixed_Cost'];
                    $tariff_name = $val['Tariff_Name'];

                    if ($flatCost == 0) {
                        $extKmAmt = 0;
                        $exkm = 0;
                        $fare = 0;
                        $percentAmt = 0;
                        $exkm = $dist - $baseKm;
                        if ($exkm > 0) {
                            $extKmAmt = $exkm * $extKmCost;
                        }
                        if ($percentage == 0) {
                            $fare = ($basCost + $extKmAmt) * $validTrip;
                        } else {
                            $percentAmt = ($basCost + $extKmAmt) * $percentage / 100;
                            $fare = ($basCost + $extKmAmt + $percentAmt) * $validTrip;
                        }
                    } else {
                        if ($percentage == 0) {
                            $fare = $flatCost * $validTrip;
                        } else {
                            $percentAmt = ($flatCost) * $percentage / 100;
                            $fare = ($flatCost + $percentAmt) * $validTrip;
                        }
                    }
                    $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'fare' => round($fare), 'validDays' => $validDays, 'validTrip' => $validTrip, 'onedayFixedprice' => $oneday_fixed_price);
                }
            } else {               
                $where = "BranchId='$branch_id' and Active ='1' and Tariff_Type='Fixed' and VehicleId='$vehicle_id' "
                        . "$condition if(`Start_Date` != '0' , $curday between Start_Date and End_Date  
                             ,`Start_Date` = 0 and End_Date=0
                            )  order by NumberofDays";
                $row = $this->c_selectarray('shuttle_tariff', $where);
                foreach ($row as $val) {
                    $tariffId = $val['TariffId'];
                    $validDays = $val['NumberofDays'];
                    $validTrip = $val['NumberofTrips'];
                    $flatCost = $val['Tariff_Flat_Cost'];
                    $tariff_name = $val['Tariff_Name'];
                    $element_array[] = array('tariff_id' => $tariffId, 'tariff_name' => $tariff_name, 'fare' => ($flatCost * $validTrip), 'validDays' => $validDays, 'validTrip' => $validTrip, 'onedayFixedprice' => $oneday_fixed_price);
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }
    
    //refer and earn registration and re
    
    public function shuttle_registration_referral($name, $email, $mobile, $password, $gender, $deviceinfo, $deviceid, $gcmid, $ct, $branch_id, $comp_id, $comp_name,$referal_code) {
        $dao = new Shuttledao();
        $getdate = $dao->get_datetime();
        $curdatetime = $getdate['cur_date_time'];
        try {
            $ENCRYP_PWD = $dao->AES_ENCRYPT($password, AES_ENCRYPT_KEY);
            $element_array = array();
            $ENC_EMAIL = $dao->AES_ENCRYPT($email, AES_ENCRYPT_KEY);
            $ENC_MOBILE = $dao->AES_ENCRYPT($mobile, AES_ENCRYPT_KEY);
            $where = "(EMAIL = '" . $ENC_EMAIL . "' || MOBILE = '" . $ENC_MOBILE . "') AND CATEGORY='Shuttle' AND ACTIVE=1";
            $row = $this->c_selectrow('employees', $where);
            if ($row) {
                $element_array = array('status' => 0, 'Message' => 'E-mail or Mobile no already registered');
            } else {
                //$eid = rand(10000, 100000);
                $eid=mt_rand(100000,999999);
                if ($comp_name != "0") {
                    $data1 = array('BRANCH_ID' => $branch_id, 'NAME' => ucwords(strtolower($comp_name)), 'ACTIVE' => 0, 'CREATED_BY' => $eid, 'created_at' => $curdatetime);
                    $this->c_insert('shuttle_company_master', $data1);
                    $comp_id = $this->db->insert_id();
                }

                $ENC_NAME = $dao->AES_ENCRYPT($name, AES_ENCRYPT_KEY);
                $data = array('BRANCH_ID' => $branch_id, 'SHUTTLE_COMPANY_ID' => $comp_id, 'EMPLOYEES_ID' => $eid, 'NAME' => $ENC_NAME, 'password' => $ENCRYP_PWD, 'EMAIL' => $ENC_EMAIL, 'MOBILE' => $ENC_MOBILE, 'CATEGORY' => 'Shuttle', 'CREATED_DATE' => $curdatetime,
                    'GENDER' => $gender, 'DEVICE_INFO' => $deviceinfo, 'MOBILE_GCM' => $gcmid, 'MOBILE_CATEGORY' => $ct, 'DEVICE_ID' => $deviceid);
                $cnt = $this->c_insert('employees', $data);
                if ($cnt > 0) {
                    $message = "Thank you for registering with " . SMS_TITLE . ".enjoy the ride!";
                    $dao->insert_sms($branch_id, 'TOPSCS', $mobile, $message);
                    $data1 = array('branch_id' => $branch_id, 'shuttle_company_id' => $comp_id, 'emp_id' => $eid, 'heading' => 'Welcome', 'message' => 'Welcome to shuttle', 'created_at' => $curdatetime);
                    $this->c_insert('shuttle_notification', $data1);
                    $element_array = array('status' => 1, 'Message' => 'Successfully registered');
                } else {
                    $element_array = array('status' => 0, 'Message' => 'Registration failed');
                }
            }
            return $element_array;
        } catch (Exception $e) {
            //echo $e->getMessage();
            log_message('error', 'USER_INFO ' . $e->getMessage());
        }
    }

}
