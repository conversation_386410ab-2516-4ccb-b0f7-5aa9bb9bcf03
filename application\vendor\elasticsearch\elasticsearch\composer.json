{"name": "elasticsearch/elasticsearch", "description": "PHP Client for Elasticsearch", "keywords": ["search", "client", "elasticsearch"], "type": "library", "license": "Apache-2.0", "authors": [{"name": "<PERSON>"}], "require": {"php": "^5.6|^7.0", "psr/log": "~1.0", "guzzlehttp/ringphp": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.7|^5.4", "mockery/mockery": "0.9.4", "symfony/yaml": "^2.8", "symfony/finder": "^2.8", "cpliakas/git-wrapper": "~1.0", "sami/sami": "~3.2", "doctrine/inflector": "^1.1"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "autoload": {"psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "autoload-dev": {"psr-4": {"Elasticsearch\\Tests\\": "tests/Elasticsearch/Tests/"}}}