<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class ShuttleCommonmdl extends CI_Model {

    public function c_insert($tablename, $values) {
        $CI = & get_instance();
        $CI->db->set($values);
        $CI->db->insert($tablename, $CI);
        if ($CI->db->affected_rows() > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    public function c_update($tablename, $values, $where) {
        $CI = & get_instance();
        $where1 = $where;
        if ($where1 != '') {
            $CI->db->where($where1);
            $CI->db->update($tablename, $values);
            if ($CI->db->affected_rows() > 0) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public function c_selectrow($tablename, $where) {
        $CI = & get_instance();
        $where2 = $where;
        $CI->db->select('*');
        $CI->db->from($tablename);
        $CI->db->where($where2);
        $query1 = $CI->db->get();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->row_array();
            return $row1;
        }
    }

    public function c_selectarray($tablename, $where) {
        $CI = & get_instance();
        $where2 = $where;
        $CI->db->select('*');
        $CI->db->from($tablename);
        $CI->db->where($where2);
        $query1 = $CI->db->get();
       //echo $CI->db->last_query();
        if ($query1->num_rows() > 0) {
            $row1 = $query1->result_array();
            return $row1;
        }
    }
     public function insert_sms($branchid, $originator, $mobileno, $message) {
        $getdate = $this->get_datetime();
        $cur_date_time = $getdate['cur_date_time'];
        $data = array('BRANCH_ID' => $branchid, 'ORIGINATOR' => $originator, 'RECIPIENT' => $mobileno, 'MESSAGE' => $message, 'STATUS' => 'U',
            'SENT_DATE' => '1900-01-01 00:00:00', 'CREATED_DATE' => $cur_date_time);
        $CI = & get_instance();
        $CI->db->set($data);
        $CI->db->insert('sms', $CI);
    }
    public function get_datetime() {
        date_default_timezone_set('Asia/Kolkata');
        $cur_datetime = date('Y-m-d H:i:s');
        $cur_date = date('Y-m-d');
        $cur_time = date('H:i:s');
        $arr = array('cur_date_time' => $cur_datetime, 'cur_date' => $cur_date, 'cur_time' => $cur_time);
        return $arr;
    }
    public function holiday_count($branch_id,$sdate,$edate)
    {
        $count=0;
        $CI = & get_instance();
        $where = "Active=1 and HolidayDate between '$sdate' and '$edate' and Branch='$branch_id'";
        $CI->db->select("count(HolidayDate) as holidaycount");
        $CI->db->from("shuttle_noservice");       
        $CI->db->where($where);
        $query = $CI->db->get();
        // echo $CI->db->last_query();
        if ($query->num_rows() > 0) {
            $row = $query->row_array();
            $count=$row['holidaycount'];           
        }
        return $count;      
    }
    function AES_ENCRYPT($value, $secret) {
        return rtrim(
                base64_encode(
                        mcrypt_encrypt(
                                MCRYPT_RIJNDAEL_256, $secret, $value, MCRYPT_MODE_ECB, mcrypt_create_iv(
                                        mcrypt_get_iv_size(
                                                MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
                                        ), MCRYPT_RAND)
                        )
                ), "\0"
        );
    }

    function AES_DECRYPT($value, $secret) {
        return rtrim(
                mcrypt_decrypt(
                        MCRYPT_RIJNDAEL_256, $secret, base64_decode($value), MCRYPT_MODE_ECB, mcrypt_create_iv(
                                mcrypt_get_iv_size(
                                        MCRYPT_RIJNDAEL_256, MCRYPT_MODE_ECB
                                ), MCRYPT_RAND
                        )
                ), "\0"
        );
    }

    function TimeToSec($time) {
        $sec = 0;
        foreach (array_reverse(explode(':', $time)) as $k => $v)
            $sec += pow(60, $k) * $v;
        return $sec;
    }

    function distance($lat1, $lon1, $lat2, $lon2, $unit) {
        //  echo $lat1, $lon1, $lat2, $lon2, $unit;
        $rtn = 0;
        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit = strtoupper($unit);

        if ($unit == "K") {
            $rtn = $miles * 1.609344;
        } else if ($unit == "N") {
            $rtn = $miles * 0.8684;
        } else {
            $rtn = $miles;
        }
        if (is_nan($miles)) {
            return 0;
        } else {
            return round($rtn, 2);
        }
    }

    function GetDrivingKm($lat, $long) {
        $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $lat . "&destination=" . $long . "&sensor=false&units=metric&mode=driving&alternatives=true&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPTSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPTSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        curl_close($ch);


        $data = json_decode($response);
        // print_r($data);
        $km = 0;
        $km1 = 0;
        $km2 = 0;
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {

                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km = $x[0];
                    } else {
                        $km = ($x[0] * 0.001);
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $tim = $val;
                }
            }

            $route = $data->routes[1];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {
                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km1 = $x[0];
                    } else {
                        $km1 = ($x[0] * 0.001);
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $tim1 = $val;
                }
            }

            $route = $data->routes[2];
            foreach ($route->legs as $leg) {
                foreach ($leg->distance as $key => $val) {
                    $x = explode(" ", $val);
                    if ($x[1] == 'km') {
                        $km2 = $x[0];
                    } else {
                        $km2 = ($x[0] * 0.001);
                    }
                }

                foreach ($leg->duration as $key => $val) {
                    $tim2 = $val;
                }
            }
        }
//echo $km.'|'.$km1.'|'.$km2;
        if ($km2 == 0) {
            if ($km1 == 0) {
                $kms = $km;
            } else {
                $kms = min(array($km, $km1));
            }
        } else if ($km1 == 0) {
            $kms = $km;
        } else {
            $kms = min(array($km, $km1, $km2));
        }


        $time = 0;
        if ($kms == $km) {
            $time = $tim;
        }
        if ($kms == $km1) {
            $time = $tim1;
        }
        if ($kms == $km2) {
            $time = $tim2;
        }
        return array('distance' => $kms, 'time' => $time, 'time_mins' => $time);
    }

    function GetTransitKm($origin, $destination) {
        $url = $this->signUrl("https://maps.googleapis.com/maps/api/distancematrix/json?origins=" . $origin . "&destinations=" . $destination . "&sensor=false&units=metric&mode=transit&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
        $json = json_decode(file_get_contents($url, null), true);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPTSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPTSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        curl_close($ch);
        $data = json_decode($response);
//        print_r($json);
//        exit();
        return $data;
    }

    public function output($element_array, $ct) {
        if ($ct == "ip") {
            return json_encode($element_array);
        } else {
            $string = json_encode($element_array);
            $returnval = $this->encrypt($string, ENCRYPT_KEY1, ENCRYPT_KEY2);
            $valreturn = array('xyssdff' => $returnval);
            return json_encode($valreturn);
        }
    }

    function encrypt($message, $initialVector, $secretKey) {
        return base64_encode(
                mcrypt_encrypt(
                        MCRYPT_RIJNDAEL_128, md5($secretKey), $message, MCRYPT_MODE_CFB, $initialVector
                )
        );
    }

    function decrypt($data, $key, $secretKey) {
        $decode = base64_decode($data);
        return mcrypt_decrypt(
                MCRYPT_RIJNDAEL_128, md5($key), $decode, MCRYPT_MODE_CFB, $secretKey
        );
    }

    function msort($sorder, $array, $key, $sort_flags = SORT_REGULAR) {
        if (is_array($array) && count($array) > 0) {
            if (!empty($key)) {
                $mapping = array();
                foreach ($array as $k => $v) {
                    $sort_key = '';
                    if (!is_array($key)) {
                        $sort_key = $v[$key];
                    } else {
                        // @TODO This should be fixed, now it will be sorted as string
                        foreach ($key as $key_key) {
                            $sort_key .= $v[$key_key];
                        }
                        $sort_flags = SORT_STRING;
                    }
                    $mapping[$k] = $sort_key;
                }
                if ($sorder == "asc") {
                    asort($mapping, $sort_flags);
                } else {
                    arsort($mapping, $sort_flags);
                }
                $sorted = array();
                foreach ($mapping as $k => $v) {
                    $sorted[] = $array[$k];
                }
                return $sorted;
            }
        }
        return $array;
    }

    private function record_sort($records, $field, $reverse = false) {
        $hash = array();

        foreach ($records as $record) {
            $hash[$record[$field]] = $record;
        }

        ($reverse) ? krsort($hash) : ksort($hash);

        $records = array();

        foreach ($hash as $record) {
            $records [] = $record;
        }

        return $records;
    }

    public function getrandomnumber() {
        $a = 0;
        for ($i = 0; $i < 6; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(100000, 999999);

        return $a;
    }

    public function getrandomnumber1() {
        $a = 0;
        for ($i = 0; $i < 4; $i++) {
            $a .= mt_rand(0, 9);
        }
        $a = mt_rand(1000, 9999);

        return $a;
    }

    function sec_to_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor($seconds % 3600 / 60);
        $seconds = $seconds % 60;

        return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
    }

    public function sendsms($mobile, $sms) {
        $msg = urlencode($sms);
       // $urltopost = "http://hp.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=NTL@ind1&send=GTAXEI&dest=$mobile&msg=" . $msg;
        $urltopost = "http://hpt.dial4sms.com/SendSMS/sendmsg.php?uname=ntltaxi&pass=NTL@ind1&send=GTAXEI&dest=".$mobile."&msg=".$msg;

        $ch = curl_init($urltopost);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //Sms_Response
        $returndata = curl_exec($ch);
        return $returndata;
    }

    function getAddress($latitude, $longitude) {
        if (!empty($latitude) && !empty($longitude)) {

            $url = $this->signUrl("http://maps.googleapis.com/maps/api/geocode/json?address=" . $latitude . "," . $longitude . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            $response = curl_exec($ch);
            //echo $response;
            curl_close($ch);

            //  $geocodeFromLatLong = json_decode($response);

            $output = json_decode($response);
            $status = $output->status;
            //Get address from json data
            $address = ($status == "OK") ? $output->results[1]->formatted_address : '';
            //Return address of the given latitude and longitude
            if (!empty($address)) {
                return $address;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    function encodeBase64UrlSafe($value) {
        return str_replace(array('+', '/'), array('-', '_'), base64_encode($value));
    }

    function decodeBase64UrlSafe($value) {
        return base64_decode(str_replace(array('-', '_'), array('+', '/'), $value));
    }

    function signUrl($myUrlToSign, $privateKey) {
        // parse the url
        $url = parse_url($myUrlToSign);
        $urlPartToSign = $url['path'] . "?" . $url['query'];

        // Decode the private key into its binary format
        $decodedKey = $this->decodeBase64UrlSafe($privateKey);

        // Create a signature using the private key and the URL-encoded
        // string using HMAC SHA1. This signature will be binary.
        $signature = hash_hmac("sha1", $urlPartToSign, $decodedKey, true);

        $encodedSignature = $this->encodeBase64UrlSafe($signature);

        return $myUrlToSign . "&signature=" . $encodedSignature;
    }

    function calkm1($origin, $destination, $waypoints, $type, $mode) {
        if ($mode == "walk") {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //$url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking";
        } else {
            $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
            //  $url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false";
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $response = curl_exec($ch);
        //echo $response;
        curl_close($ch);

        $data = json_decode($response);

        $km = 0;
        $tim = "";
        // If we got directions, output all of the HTML instructions
        if ($data->status === 'OK') {
            $route = $data->routes[0];
            foreach ($route->legs as $leg) {
                //foreach ($leg->steps as $step) {
                $i = 0;
                foreach ($leg->distance as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        $x = explode(" ", $val);
                        if ($x[1] == 'km') {
                            $km += $x[0];
                        } else {
                            $km += ($x[0] * 0.001);
                        }
                    }
                }
                foreach ($leg->duration as $key => $val) {
                    $i++;
                    $j = $i % 2;
                    if ($j == 1) {
                        
                    } else {
                        $tim += $val;
                    }
                }
            }
            if ($type == 'km') {
                return $km;
            } else {
                return $km . '-' . $tim;
            }
        }
    }

	function calkm($origin, $destination, $waypoints, $type, $mode) {            
        $url = "https://maps.googleapis.com/maps/api/directions/json?origin=".$origin."&destination=".$destination."&key=".GOOGLE_MAP_API_KEY."&sensor=false&units=metric&mode=$mode";                    
        $travel_distance=0; $distance = 0;
        $time_mins = 0;
        $json = json_decode(file_get_contents($url), true);
       // print_r($json);
      
        /*  $CI = & get_instance();   
        $req_values = array( 't1' => $url);
        $CI->db->set($req_values);
        $CI->db->insert('test', $CI);*/

        if (count($json['routes']) > 0) {
                $route = $json['routes'][0]['legs'];
                //print_r($json['routes'][0]['legs']);
                $count = count($json['routes'][0]['legs']);
                $distance = 0;
                $time_mins = 0;
                for ($i = 0; $i < $count; $i++) {
                        $distance = $distance + $json['routes'][0]['legs'][$i]['distance']['value'];
                        $time_mins = $time_mins + $json['routes'][0]['legs'][$i]['duration']['value'];
                        $address = $json['routes'][0]['legs'][$i]['start_address'];
                }
                $travel_distance=number_format(($distance / 1000), 2);
        }       
        if ($type == 'km') {
                return $travel_distance;
            } 
            else if ($type == 'time') {
                return $time_mins;
            }
            else {
                return $travel_distance . '-' . $time_mins;
            }
            
//        if ($mode == "walk") {
//			$url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins=".$origin."&destinations=".$destination."&key=".GOOGLE_MAP_API_KEY."&sensor=false&units=metric&mode=walking";
//
//            //$url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&destination=" . $destination . //"&sensor=false&units=metric&mode=walking&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
//            //$url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&destination=" . $destination . "&sensor=false&units=metric&mode=walking";
//        } else {
//			$url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins=".$origin."&destinations=".$destination."&key=".GOOGLE_MAP_API_KEY."&sensor=false&units=metric&mode=driving";
//           // $url = $this->signUrl("http://maps.googleapis.com/maps/api/directions/json?origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . //$destination . "&sensor=false&client=gme-newtravellinesindia", '1QFDWGiIi2lM5d69MgetP1Vy3OA=');
//            //  $url = "https://www.google.com/maps/dir/?api=1&origin=" . $origin . "&waypoints=" . $waypoints . "&destination=" . $destination . "&sensor=false";
//        }
//        $ch = curl_init();
//        curl_setopt($ch, CURLOPT_URL, $url);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//        curl_setopt($ch, CURLOPT_PROXYPORT, 3128);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
//        $response = curl_exec($ch);
//        //echo $response;
//        curl_close($ch);
//
//        $data = json_decode($response);		
//        $km = 0;
//        $tim = "";
//        // If we got directions, output all of the HTML instructions
//        if ($data->status === 'OK') {
//            $route = $data->rows[0];			
//            foreach ($route->elements as $leg) {
//                //foreach ($leg->steps as $step) {
//                $i = 0;			
//                foreach ($leg->distance as $key => $val) {
//                    $i++;
//                    $j = $i % 2;
//					//echo "dist".$leg->distance;
//                    if ($j == 1) {
//                        $x = explode(" ", $val);
//                        if ($x[1] == 'km') {
//                            $km += $x[0];
//                        } else {
//                            $km += ($x[0] * 0.001);
//                        }
//                    }
//                }
//                foreach ($leg->duration as $key => $val) {
//                    $i++;
//                    $j = $i % 2;
//                    if ($j == 1) {
//                        
//                    } else {
//                        $tim += $val;
//                    }
//                }
//            }
//            if ($type == 'km') {
//                return $km;
//            } else {
//                return $km . '-' . $tim;
//            }
     //   }
    }
    public function getWorkingDays($startDate, $endDate,$branch_id) {
        $begin = strtotime($startDate);
        $end = strtotime($endDate);
        $holiday_count=$this->holiday_count($branch_id, $startDate, $endDate);
        if ($begin > $end) {

            return 0;
        } else {
            $no_days = 0;
            while ($begin <= $end) {
                $what_day = date("N", $begin);
                if (!in_array($what_day, [6, 7])) // 6 and 7 are weekend
                    $no_days++;
                $begin += 86400; // +1 day
            };

            return $no_days-$holiday_count;
        }
    }

    private function check_time($t1, $t2, $tn) {
        if ($t2 >= $t1) {
            return $t1 <= $tn && $tn <= $t2;
        } else {
            return !($t2 <= $tn && $tn <= $t1);
        }
    }
   function sum_the_time($time1, $time2) {
       
        $times = array($time1, $time2);
        $seconds = 0;
        foreach ($times as $time)
        {
          list($hour,$minute,$second) = explode(':', $time);
          $seconds += $hour*3600;
          $seconds += $minute*60;
          $seconds += $second;
        }
        $hours = floor($seconds/3600);
        $seconds -= $hours*3600;
        $minutes  = floor($seconds/60);
        $seconds -= $minutes*60;
        // return "{$hours}:{$minutes}:{$seconds}";
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds); 
    }
    public function createDateRangeArray($strDateFrom,$strDateTo)
    {
        // takes two dates formatted as YYYY-MM-DD and creates an
        // inclusive array of the dates between the from and to dates.

        // could test validity of dates here but I'm already doing
        // that in the main script

        $aryRange="";

        $iDateFrom=mktime(1,0,0,substr($strDateFrom,5,2),     substr($strDateFrom,8,2),substr($strDateFrom,0,4));
        $iDateTo=mktime(1,0,0,substr($strDateTo,5,2),     substr($strDateTo,8,2),substr($strDateTo,0,4));

        if ($iDateTo>$iDateFrom)
        {
            array_push($aryRange,date('Y-m-d',$iDateFrom)); // first entry
            while ($iDateFrom<$iDateTo)
            {
                $iDateFrom+=86400; // add 24 hours
                $aryRange.="'".date('Y-m-d',$iDateFrom)."',";
            }
        }
        return $aryRange;
    }

}
