<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname(dirname($vendorDir));

return array(
    'React\\Promise\\' => array($vendorDir . '/react/promise/src'),
    'Razorpay\\Tests\\' => array($vendorDir . '/razorpay/razorpay/tests'),
    'Razorpay\\Api\\' => array($vendorDir . '/razorpay/razorpay/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'GuzzleHttp\\Stream\\' => array($vendorDir . '/guzzlehttp/streams/src'),
    'GuzzleHttp\\Ring\\' => array($vendorDir . '/guzzlehttp/ringphp/src'),
    'Elasticsearch\\' => array($vendorDir . '/elasticsearch/elasticsearch/src/Elasticsearch'),
);
