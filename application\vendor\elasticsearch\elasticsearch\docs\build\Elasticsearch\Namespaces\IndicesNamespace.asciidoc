

[[Elasticsearch_Namespaces_IndicesNamespace]]
=== Elasticsearch\Namespaces\IndicesNamespace



Class IndicesNamespace


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_IndicesNamespaceexists_exists,`exists()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacesegments_segments,`segments()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteTemplate_deleteTemplate,`deleteTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedelete_delete,`delete()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacestats_stats,`stats()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputSettings_putSettings,`putSettings()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacesnapshotIndex_snapshotIndex,`snapshotIndex()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceshrink_shrink,`shrink()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetMapping_getMapping,`getMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetFieldMapping_getFieldMapping,`getFieldMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceflush_flush,`flush()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceflushSynced_flushSynced,`flushSynced()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacerefresh_refresh,`refresh()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacerecovery_recovery,`recovery()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsType_existsType,`existsType()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputAlias_putAlias,`putAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputTemplate_putTemplate,`putTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacevalidateQuery_validateQuery,`validateQuery()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetAlias_getAlias,`getAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputMapping_putMapping,`putMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteMapping_deleteMapping,`deleteMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetTemplate_getTemplate,`getTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsTemplate_existsTemplate,`existsTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacecreate_create,`create()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceforceMerge_forceMerge,`forceMerge()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteAlias_deleteAlias,`deleteAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceopen_open,`open()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceanalyze_analyze,`analyze()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceclearCache_clearCache,`clearCache()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceupdateAliases_updateAliases,`updateAliases()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetAliases_getAliases,`getAliases()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsAlias_existsAlias,`existsAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacestatus_status,`status()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetSettings_getSettings,`getSettings()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceclose_close,`close()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceseal_seal,`seal()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceupgrade_upgrade,`upgrade()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetUpgrade_getUpgrade,`getUpgrade()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceshardStores_shardStores,`shardStores()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacerollover_rollover,`rollover()`>>



[[Elasticsearch_Namespaces_IndicesNamespaceexists_exists]]
.`exists()`
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of indices to check (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->exists($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceget_get]]
.`get()`
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of indices to check (Required)
       ['feature'] = (list) A comma-separated list of features to return
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->get($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacesegments_segments]]
.`segments()`
****
[source,php]
----
/*
$params['index']               = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['operation_threading'] = () TODO: ?
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->segments($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteTemplate_deleteTemplate]]
.`deleteTemplate()`
****
[source,php]
----
/*
$params['name']    = (string) The name of the template (Required)
       ['timeout'] = (time) Explicit operation timeout
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->deleteTemplate($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedelete_delete]]
.`delete()`
****
[source,php]
----
/*
$params['index']   = (list) A comma-separated list of indices to delete; use `_all` or empty string to delete all indices
       ['timeout'] = (time) Explicit operation timeout
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->delete($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacestats_stats]]
.`stats()`
****
[source,php]
----
/*
$params['fields']         = (boolean) A comma-separated list of fields for `fielddata` metric (supports wildcards)
       ['index']          = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['indexing_types'] = (list) A comma-separated list of document types to include in the `indexing` statistics
       ['metric_family']  = (enum) Limit the information returned to a specific metric
       ['search_groups']  = (list) A comma-separated list of search groups to include in the `search` statistics
       ['all']            = (boolean) Return all available information
       ['clear']          = (boolean) Reset the default level of detail
       ['docs']           = (boolean) Return information about indexed and deleted documents
       ['fielddata']      = (boolean) Return information about field data
       ['filter_cache']   = (boolean) Return information about filter cache
       ['flush']          = (boolean) Return information about flush operations
       ['get']            = (boolean) Return information about get operations
       ['groups']         = (boolean) A comma-separated list of search groups for `search` statistics
       ['id_cache']       = (boolean) Return information about ID cache
       ['ignore_indices'] = (enum) When performed on multiple indices, allows to ignore `missing` ones
       ['indexing']       = (boolean) Return information about indexing operations
       ['merge']          = (boolean) Return information about merge operations
       ['refresh']        = (boolean) Return information about refresh operations
       ['search']         = (boolean) Return information about search operations; use the `groups` parameter to include information for specific search groups
       ['store']          = (boolean) Return information about the size of the index
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->stats($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputSettings_putSettings]]
.`putSettings()`
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['body']  = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->putSettings($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacesnapshotIndex_snapshotIndex]]
.`snapshotIndex()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->snapshotIndex($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceshrink_shrink]]
.`shrink()`
****
[source,php]
----
/*
$params['index']          = (string) The name of the source index to shrink
       ['target']         = (string) The name of the target index to shrink into
       ['timeout']        = (time) Explicit operation timeout
       ['master_timeout'] = (time) Specify timeout for connection to master
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->shrink($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetMapping_getMapping]]
.`getMapping()`
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['type']  = (list) A comma-separated list of document types
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getMapping($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetFieldMapping_getFieldMapping]]
.`getFieldMapping()`
****
[source,php]
----
/*
$params['index']            = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['type']             = (list) A comma-separated list of document types
       ['field']            = (list) A comma-separated list of document fields
       ['include_defaults'] = (bool) specifies default mapping values should be returned
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getFieldMapping($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceflush_flush]]
.`flush()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['force']              = (boolean) TODO: ?
       ['full']               = (boolean) TODO: ?
       ['refresh']            = (boolean) Refresh the index after performing the operation
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->flush($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceflushSynced_flushSynced]]
.`flushSynced()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['force']              = (boolean) TODO: ?
       ['full']               = (boolean) TODO: ?
       ['refresh']            = (boolean) Refresh the index after performing the operation
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->flushSynced($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacerefresh_refresh]]
.`refresh()`
****
[source,php]
----
/*
$params['index']               = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['operation_threading'] = () TODO: ?
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->refresh($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacerecovery_recovery]]
.`recovery()`
****
[source,php]
----
/*
$params['index']       = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['detailed']    = (bool) Whether to display detailed information about shard recovery
       ['active_only'] = (bool) Display only those recoveries that are currently on-going
       ['human']       = (bool) Whether to return time and byte values in human-readable format.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->recovery($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsType_existsType]]
.`existsType()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` to check the types across all indices (Required)
       ['type']               = (list) A comma-separated list of document types to check (Required)
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->existsType($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputAlias_putAlias]]
.`putAlias()`
****
[source,php]
----
/*
$params['index']   = (string) The name of the index with an alias
       ['name']    = (string) The name of the alias to be created or updated
       ['timeout'] = (time) Explicit timestamp for the document
       ['body']    = (time) Explicit timestamp for the document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->putAlias($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputTemplate_putTemplate]]
.`putTemplate()`
****
[source,php]
----
/*
$params['name']    = (string) The name of the template (Required)
       ['order']   = (number) The order for this template when merging multiple matching ones (higher numbers are merged later, overriding the lower numbers)
       ['timeout'] = (time) Explicit operation timeout
       ['body']    = (time) Explicit operation timeout
       ['create']  = (bool) Whether the index template should only be added if new or can also replace an existing one
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->putTemplate($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacevalidateQuery_validateQuery]]
.`validateQuery()`
****
[source,php]
----
/*
$params['index']               = (list) A comma-separated list of index names to restrict the operation; use `_all` or empty string to perform the operation on all indices
       ['type']                = (list) A comma-separated list of document types to restrict the operation; leave empty to perform the operation on all types
       ['explain']             = (boolean) Return detailed information about the error
       ['ignore_indices']      = (enum) When performed on multiple indices, allows to ignore `missing` ones
       ['operation_threading'] = () TODO: ?
       ['source']              = (string) The URL-encoded query definition (instead of using the request body)
       ['body']                = (string) The URL-encoded query definition (instead of using the request body)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->validateQuery($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetAlias_getAlias]]
.`getAlias()`
****
[source,php]
----
/*
$params['name']           = (list) A comma-separated list of alias names to return (Required)
       ['index']          = (list) A comma-separated list of index names to filter aliases
       ['ignore_indices'] = (enum) When performed on multiple indices, allows to ignore `missing` ones
       ['name']           = (list) A comma-separated list of alias names to return
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getAlias($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputMapping_putMapping]]
.`putMapping()`
****
[source,php]
----
/*
$params['index']            = (list) A comma-separated list of index names; use `_all` to perform the operation on all indices (Required)
       ['type']             = (string) The name of the document type
       ['ignore_conflicts'] = (boolean) Specify whether to ignore conflicts while updating the mapping (default: false)
       ['timeout']          = (time) Explicit operation timeout
       ['body']             = (time) Explicit operation timeout
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->putMapping($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteMapping_deleteMapping]]
.`deleteMapping()`
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of index names; use `_all` for all indices (Required)
       ['type']  = (string) The name of the document type to delete (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->deleteMapping($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetTemplate_getTemplate]]
.`getTemplate()`
****
[source,php]
----
/*
$params['name'] = (string) The name of the template (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getTemplate($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsTemplate_existsTemplate]]
.`existsTemplate()`
****
[source,php]
----
/*
$params['name'] = (string) The name of the template (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->existsTemplate($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacecreate_create]]
.`create()`
****
[source,php]
----
/*
$params['index']   = (string) The name of the index (Required)
       ['timeout'] = (time) Explicit operation timeout
       ['body']    = (time) Explicit operation timeout
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->create($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceforceMerge_forceMerge]]
.`forceMerge()`
****
[source,php]
----
/*
$params['index']                = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['flush']                = (boolean) Specify whether the index should be flushed after performing the operation (default: true)
       ['max_num_segments']     = (number) The number of segments the index should be merged into (default: dynamic)
       ['only_expunge_deletes'] = (boolean) Specify whether the operation should only expunge deleted documents
       ['operation_threading']  = () TODO: ?
       ['refresh']              = (boolean) Specify whether the index should be refreshed after performing the operation (default: true)
       ['wait_for_merge']       = (boolean) Specify whether the request should block until the merge process is finished (default: true)
       ['ignore_unavailable']   = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']     = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']     = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->forceMerge($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteAlias_deleteAlias]]
.`deleteAlias()`
****
[source,php]
----
/*
$params['index']   = (string) The name of the index with an alias (Required)
       ['name']    = (string) The name of the alias to be deleted (Required)
       ['timeout'] = (time) Explicit timestamp for the document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->deleteAlias($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceopen_open]]
.`open()`
****
[source,php]
----
/*
$params['index']   = (string) The name of the index (Required)
       ['timeout'] = (time) Explicit operation timeout
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->open($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceanalyze_analyze]]
.`analyze()`
****
[source,php]
----
/*
$params['index']        = (string) The name of the index to scope the operation
       ['analyzer']     = (string) The name of the analyzer to use
       ['field']        = (string) Use the analyzer configured for this field (instead of passing the analyzer name)
       ['filter']       = (list) A comma-separated list of filters to use for the analysis
       ['prefer_local'] = (boolean) With `true`, specify that a local shard should be used if available, with `false`, use a random shard (default: true)
       ['text']         = (string) The text on which the analysis should be performed (when request body is not used)
       ['tokenizer']    = (string) The name of the tokenizer to use for the analysis
       ['format']       = (enum) Format of the output
       ['body']         = (enum) Format of the output
       ['char_filter']  = (list) A comma-separated list of character filters to use for the analysis
       ['explain']      = (bool) With `true`, outputs more advanced details. (default: false)
       ['attributes']   = (list) A comma-separated list of token attributes to output, this parameter works only with `explain=true`
       ['format']       = (enum) Format of the output (["detailed", "text"])
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->analyze($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceclearCache_clearCache]]
.`clearCache()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index name to limit the operation
       ['field_data']         = (boolean) Clear field data
       ['fielddata']          = (boolean) Clear field data
       ['fields']             = (list) A comma-separated list of fields to clear when using the `field_data` parameter (default: all)
       ['filter']             = (boolean) Clear filter caches
       ['filter_cache']       = (boolean) Clear filter caches
       ['filter_keys']        = (boolean) A comma-separated list of keys to clear when using the `filter_cache` parameter (default: all)
       ['id']                 = (boolean) Clear ID caches for parent/child
       ['id_cache']           = (boolean) Clear ID caches for parent/child
       ['recycler']           = (boolean) Clear the recycler cache
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->clearCache($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceupdateAliases_updateAliases]]
.`updateAliases()`
****
[source,php]
----
/*
$params['index']   = (list) A comma-separated list of index names to filter aliases
       ['timeout'] = (time) Explicit timestamp for the document
       ['body']    = (time) Explicit timestamp for the document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->updateAliases($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetAliases_getAliases]]
.`getAliases()`
****
[source,php]
----
/*
$params['local']   = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['timeout'] = (time) Explicit timestamp for the document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getAliases($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsAlias_existsAlias]]
.`existsAlias()`
****
[source,php]
----
/*
$params['name']               = (list) A comma-separated list of alias names to return (Required)
       ['index']              = (list) A comma-separated list of index names to filter aliases
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->existsAlias($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacestatus_status]]
.`status()`
****
[source,php]
----
/*
$params['index']               = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['ignore_indices']      = (enum) When performed on multiple indices, allows to ignore `missing` ones
       ['operation_threading'] = () TODO: ?
       ['recovery']            = (boolean) Return information about shard recovery
       ['snapshot']            = (boolean) TODO: ?
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->status($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetSettings_getSettings]]
.`getSettings()`
****
[source,php]
----
/*
$params['index'] = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getSettings($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceclose_close]]
.`close()`
****
[source,php]
----
/*
$params['index']   = (string) The name of the index (Required)
       ['timeout'] = (time) Explicit operation timeout
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->close($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceseal_seal]]
.`seal()`
****
[source,php]
----
/*
$params['index']   = (string) The name of the index
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->seal($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceupgrade_upgrade]]
.`upgrade()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['wait_for_completion']= (boolean) Specify whether the request should block until the all segments are upgraded (default: false)
       ['only_ancient_segments'] = (boolean) If true, only ancient (an older Lucene major release) segments will be upgraded
       ['refresh']            = (boolean) Refresh the index after performing the operation
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->upgrade($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetUpgrade_getUpgrade]]
.`getUpgrade()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
       ['wait_for_completion']= (boolean) Specify whether the request should block until the all segments are upgraded (default: false)
       ['only_ancient_segments'] = (boolean) If true, only ancient (an older Lucene major release) segments will be upgraded
       ['refresh']            = (boolean) Refresh the index after performing the operation
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->getUpgrade($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceshardStores_shardStores]]
.`shardStores()`
****
[source,php]
----
/*
$params['index']   = (string) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
       ['status']   = (list) A comma-separated list of statuses used to filter on shards to get store information for
       ['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices'] = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards'] = (boolean) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->shardStores($params);
----
****



[[Elasticsearch_Namespaces_IndicesNamespacerollover_rollover]]
.`rollover()`
****
[source,php]
----
/*
$params['newIndex']       = (string) The name of the rollover index
       ['alias']          = (string) The name of the alias to rollover
       ['timeout']        = (time) Explicit operation timeout
       ['master_timeout'] = (time) Specify timeout for connection to master
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices()->rollover($params);
----
****


