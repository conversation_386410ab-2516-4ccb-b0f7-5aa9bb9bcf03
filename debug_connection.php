<?php
echo "<h2>🔍 Database Connection Debug</h2>";

// Load CodeIgniter database config
include 'C:/shuttleb2bemployeeapp/shuttleb2bemployeeapp/application/config/database.php';

echo "<h3>📋 Current Database Configuration:</h3>";
echo "<pre>";
print_r($db['default']);
echo "</pre>";

echo "<h3>🔧 Missing Required Settings:</h3>";
$required = ['hostname', 'username', 'password', 'database', 'dbdriver'];
foreach ($required as $key) {
    if (!isset($db['default'][$key]) || empty($db['default'][$key])) {
        echo "❌ <strong>$key</strong> is missing or empty<br>";
    } else {
        echo "✅ <strong>$key</strong>: " . $db['default'][$key] . "<br>";
    }
}

echo "<h3>🌐 Testing Direct Database Connection:</h3>";
try {
    // Try to connect with current config
    $hostname = isset($db['default']['hostname']) ? $db['default']['hostname'] : 'localhost';
    $username = isset($db['default']['username']) ? $db['default']['username'] : 'root';
    $password = isset($db['default']['password']) ? $db['default']['password'] : '';
    $database = isset($db['default']['database']) ? $db['default']['database'] : 'test';
    
    echo "Attempting connection with:<br>";
    echo "Host: $hostname<br>";
    echo "User: $username<br>";
    echo "Pass: " . (empty($password) ? '(empty)' : '***') . "<br>";
    echo "DB: $database<br><br>";
    
    $pdo = new PDO("mysql:host=$hostname;dbname=$database", $username, $password);
    echo "✅ <strong>Connection successful!</strong><br>";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "📋 Tables found: " . count($tables) . "<br>";
    
} catch (PDOException $e) {
    echo "❌ Connection failed: " . $e->getMessage() . "<br>";
}
?>
